/* force show for sliders */
.tempWrap, .bd { visibility: visible !important; opacity: 1 !important; }
.bd li, .bd li img { display: block !important; }
.banner img, .slideBox img, .focusBox img, .bd img { max-width: 100% !important; height: auto !important; }
.banner, .slideBox, .focusBox, .bd { min-height: 220px !important; }
/* force show ALL images */
img { visibility: visible !important; opacity: 1 !important; display: inline-block !important; }
img[src*=".png"], img[src*=".jpg"], img[src*=".jpeg"], img[src*=".gif"], img[src*=".svg"] { display: inline-block !important; }
/* fix: buttons text invisible */
.btn, a.btn, button.btn, .btn-success, .btn-green, .trade-btn, .opt-btn, .buy-btn, .sell-btn {
  font-size: 14px !important;
  line-height: 34px !important;
  color: #fff !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  overflow: visible !important;
}
.btn span, .btn i, .btn em { font-size: inherit !important; }

/* fix: admin/agent 登录页验证码看不见 */
#verifycode, img#verifycode, .login-box #verifycode, .login-form #verifycode {
  display: inline-block !important;
  width: 120px !important;
  height: 42px !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: transparent !important;
}

/* 通用图片可见（兜底） */
img { visibility: visible !important; opacity: 1 !important; }
/* 允许按钮点击 & 恢复文字 */
a, a.btn, .btn, .btn-success, .btn-green, .trade-btn, .opt-btn, .buy-btn, .sell-btn {
  pointer-events: auto !important;
  cursor: pointer !important;
  text-indent: 0 !important;
  font-size: 14px !important;
  line-height: 34px !important;
  color: #fff !important;
  overflow: visible !important;
}

/* 常见遮罩禁止拦截点击（如有透明层覆盖） */
[class*="mask"], [class*="overlay"], [class*="shade"], .modal-backdrop, .modal-mask, .page-mask {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 兜底：所有图片可见 */
img { visibility: visible !important; opacity: 1 !important; display: inline-block !important; }
/* bring buttons above any overlay and enable clicks */
a.btn, .btn, .btn-green, .btn-success, .trade-btn, .opt-btn, .buy-btn, .sell-btn,
table tr td:last-child a, table tr td:last-child .btn {
  position: relative !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
  color: #fff !important;
  text-indent: 0 !important;
}

/* 提升按钮所在单元格层级 */
table tr td:last-child, .opt, .opt-cell, .operate, .operate-cell {
  position: relative !important;
  z-index: 9998 !important;
}

/* 降低常见透明遮罩的层级并禁用拦截点击（即便存在未知类名的遮罩也不再挡点击） */
[class*="mask"], [class*="overlay"], [class*="shade"],
.modal-backdrop, .modal-mask, .page-mask, .page-overlay, .page-shade {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 登录页验证码区域抬高，避免被白块覆盖 */
#verifycode, img#verifycode { position: relative !important; z-index: 10000 !important; }
/* 禁用透明覆盖层的点击拦截（逐一列出右上导航/覆盖容器的哈希类） */
.css-11y6cix,
.css-wu6zme,
.css-1ql2hru,
.order_navlist,
.trad_navlist,
.trad-sub {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 只给链接元素恢复点击 */
.css-wu6zme a,
.css-1ql2hru a,
.order_navlist a,
.trad_navlist a,
.trad-sub a,
.nav a,
a {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 右列操作区整体抬高，确保在最上层 */
table tr td:last-child,
.operate,
.opt,
.operate-cell,
.opt-cell {
  position: relative !important;
  z-index: 9999 !important;
}

/* 按钮容器（常见标签）显示文字、消除缩进 */
.optionli span,
.css-vurnku,
.f16 {
  display: inline-block !important;
  text-indent: 0 !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 34px !important;
}

/* 强制关闭常见透明覆盖层的点击拦截 */
.css-11y6cix,
.css-wu6zme,
.css-1ql2hru,
.order_navlist,
.trad_navlist,
.trad-sub {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 链接恢复点击 */
.css-wu6zme a,
.css-1ql2hru a,
.order_navlist a,
.trad_navlist a,
.trad-sub a,
.nav a,
a {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 操作列抬高，确保可点 */
table tr td:last-child,
.operate,
.opt,
.operate-cell,
.opt-cell {
  position: relative !important;
  z-index: 9999 !important;
}

/* 按钮文本样式兜底（显示白字） */
.option-box a,
.optionli span,
.css-vurnku,
.f16 {
  display: inline-block !important;
  text-indent: 0 !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 34px !important;
}
/* 让按钮里的 <a> 必定可见可点 */
.market-div .option-box a,
.market-div a[href^="/Trade/index"],
.market-div a[href^="/Contract/index"],
.market-div a[href^="/Issue/details"] {
  display: inline-block !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 34px !important;
  text-indent: 0 !important;
}

/* 对空 <a> 注入可见文本（不改 HTML，用 ::after 渲染） - 仅限市场区块，避免影响首页按钮样式 */
.market-div a[href^="/Trade/index"]::after     { content: 'Buy'; color:#fff; }
.market-div a[href^="/Contract/index"]::after  { content: 'Contract'; color:#fff; }
.market-div a[href^="/Issue/details"]::after   { content: 'Details'; color:#fff; }

/* 行容器抬高，避免被透明层覆盖 */
.market-div .option-box,
.market-div .option-box a {
  position: relative !important;
  z-index: 10001 !important;
}

/* 顶部透明层默认不拦截，链接恢复点击 */
.css-11y6cix, .css-wu6zme, .css-1ql2hru, .order_navlist, .trad_navlist, .trad-sub {
  pointer-events: none !important; z-index: 0 !important; background: transparent !important;
}
.css-wu6zme a, .css-1ql2hru a, .order_navlist a, .trad_navlist a, .trad-sub a, .nav a, a {
  pointer-events: auto !important; cursor: pointer !important;
}
/* 全站：禁用透明遮罩拦截，链接/按钮恢复可点 */
[class*="mask"], [class*="overlay"], [class*="shade"], .css-11y6cix,
.trad-sub, .order_navlist, .css-wu6zme, .css-1ql2hru {
  pointer-events: none !important; z-index: 0 !important; background: transparent !important;
}
a, button, [role="button"], .action-button { pointer-events: auto !important; cursor: pointer !important; }

/* 通用：任何被隐藏的文字恢复显示 */
a, button, [role="button"], .action-button, .btn {
  visibility: visible !important;
  opacity: 1 !important;
  filter: none !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
}

/* 首页市场动作按钮文字可见且可点 */
.action-buttons .btn,
.action-buttons a.btn {
  color: #0d6efd !important;
  text-indent: 0 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Trade/Contract 交易区块：提升层级，避免被覆盖 */
.tradebox, .tradebox * { position: relative; z-index: 10001; }

/* 交易页买/卖按钮内文字显式为白色（绿色/红色底上清晰可见） */
[onclick*="bb_buycoin(1)"] span, [onclick*="bb_buycoin(2)"] span,
a[href*="/Login/index"] span { color: #fff !important; }

/* 若模板渲染为空，则兜底自动补文字 */
[onclick*="bb_buycoin(1)"] span:empty::after { content: 'Buy'; color:#fff; }
[onclick*="bb_buycoin(2)"] span:empty::after { content: 'Sell'; color:#fff; }

/* 顶部“登录/注册/交易/充币”等文字确保可见 */
.css-vurnku, .header-title, .optionli span { color: #fff !important; text-indent:0 !important; }

/* 语言/下拉菜单保持可点 */
.nav a, .order_navlist a, .trad_navlist a, .trad-sub a { pointer-events: auto !important; }

/* 强化按钮文字显示 - 首页专用 */
.action-buttons .btn,
.action-buttons a.btn,
.btn-outline-primary {
    color: #0d6efd !important;
    text-indent: 0 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-block !important;
    font-size: 0.8rem !important;
    line-height: normal !important;
    letter-spacing: normal !important;
    overflow: visible !important;
}

/* 火币导航栏按钮强化 */
.huobi-btn,
.huobi-btn-login,
.huobi-btn-register {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    display: inline-block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    font-size: 14px !important;
    line-height: normal !important;
}

/* 注册表单按钮强化 */
.register-form .btn,
.register-form button {
    color: #fff !important;
    text-indent: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 通用按钮文字强化 */
.btn, a.btn, .layui-btn {
    color: #fff !important;
    text-indent: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    letter-spacing: normal !important;
    overflow: visible !important;
}

/* 全局按钮文字修复 - 适用于所有页面（强化跳转页）*/
button, input[type="button"], input[type="submit"],
.btn, a.btn, .layui-btn,
[onclick], [class*="btn"], [id*="btn"],
.option-box a, .optionli span, .css-vurnku, .f16,
span[onclick], div[onclick],
/* 交易页常用类名：确保可见 */
.jy-btn, .jy-btn-buy, .btn_bg_color, .btn_bg_color_tcg, .btn_bg_color_tch,
.klinetitle-s-box, .input-desc,
/* 首页卡片按钮 */
.huobi-btn, .huobi-btn-login, .huobi-btn-register {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    letter-spacing: normal !important;
    font-size: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    overflow: visible !important;
    display: inline-block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 特殊按钮容器修复 */
#subbtn, #tyj_subbtn, .btnbox_l, .btnbox_r,
[id*="subbtn"], [class*="subbtn"] {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 按钮内的span和文字元素 */
button span, .btn span, a.btn span,
[onclick] span, [class*="btn"] span,
#subbtn span, #tyj_subbtn span {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
    display: inline !important;
}

/* 合约页面特殊按钮 */
.fcf.f12 {
    color: #fff !important;
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
}

/* 统一按钮逻辑：复用“立即注册/公告”风格到详情/交易/合约 */
/* 登录态/未登录态显示由模板控制，这里确保样式可见且交互正常 */
.btn-primary, .btn-outline-primary, .btn-warning, .btn-success,
.action-button, .order-button, .issue-button, .contract-button {
  text-indent: 0 !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}
/* 如果模板渲染为空文字，追加兜底 */
.issue-button:empty::after { content: '立即参与'; color:#fff; }
.order-button.buy:empty::after { content: '买入'; color:#fff; }
.order-button.sell:empty::after { content: '卖出'; color:#fff; }
.contract-button:empty::after { content: '下单'; color:#fff; }

/* 首页：操作列按钮强制显示文本与尺寸 */
.action-buttons .btn,
.action-buttons a.btn,
.market-row .action-buttons a {
  display: inline-block !important;
  padding: 4px 10px !important;
  min-width: 52px !important;
  white-space: nowrap !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  color: #0d6efd !important;
}
/* 防止单元格把内容裁掉 */
.market-row td:last-child, .action-buttons { overflow: visible !important; }

/* 导航兜底：即使未加载Bootstrap也能横向排列并显示 */
.modern-header nav.navbar { display:flex; align-items:center; }
.modern-header .navbar-collapse { display:flex !important; }
.modern-header .navbar-nav { display:flex; gap:12px; align-items:center; }
.modern-header .nav-link { display:inline-block !important; padding:8px 10px !important; color:#1e2329 !important; font-weight:500; }

/* 精准导航补丁：通过唯一类名确保横排、可见、顺序与样式 */
.nav-aug-fixed { display:flex; align-items:center; padding: 8px 0; }
.nav-aug-fixed .navbar-brand { margin-right: 16px; }
.main-nav-aug { display:flex !important; gap: 12px !important; align-items:center !important; flex-wrap: nowrap !important; }
.main-nav-aug .nav-link { display:inline-block !important; padding:8px 10px !important; color:#1e2329 !important; font-weight:600 !important; white-space: nowrap !important; }
.main-nav-aug .nav-link.active { color:#f0b90b !important; }
/* 禁掉所有透明遮罩对导航的点击拦截 */
.nav-aug-fixed, .nav-aug-fixed * { position: relative; z-index: 9999; pointer-events: auto !important; }

/* ========== 全局按钮文字修复增强版 ========== */

/* 交易页面买卖按钮特殊修复 */
[onclick*="bb_buycoin"], [onclick*="bb_sellcoin"] {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    font-size: 14px !important;
    line-height: 36px !important;
    color: #fff !important;
    overflow: visible !important;
    display: block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 9999 !important;
}

/* 交易页面按钮内的span文字 */
[onclick*="bb_buycoin"] span, [onclick*="bb_sellcoin"] span {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: #fff !important;
    font-size: 14px !important;
    line-height: inherit !important;
    display: inline !important;
    letter-spacing: normal !important;
    overflow: visible !important;
}

/* 移动端交易按钮 */
.transbtn, .bggreen, .bgred, .tradebox_l_buybtn, .tradebox_l_sellbtn {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: #fff !important;
    overflow: visible !important;
    display: block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

.transbtn span, .bggreen span, .bgred span,
.tradebox_l_buybtn span, .tradebox_l_sellbtn span {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: #fff !important;
    display: inline !important;
    overflow: visible !important;
}

/* 登录注册页面按钮 */
.alltn, .allbtn, .btn-primary, .btn-hover {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: #fff !important;
    overflow: visible !important;
    display: block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    font-size: 14px !important;
    line-height: normal !important;
}

/* 通用按钮类名强化 */
.btn_bg_color_tcg, .btn_bg_color_tch, .jy-btn, .jy-btn-buy,
.formbox_op_list, .input-desc, .klinetitle-s-box {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    overflow: visible !important;
    display: inline-block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
}

/* 按钮内所有文字元素 */
button *, .btn *, a.btn *, [onclick] *,
[class*="btn"] *, div[onclick] *, span[onclick] * {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: inherit !important;
    overflow: visible !important;
    display: inline !important;
}

/* 特殊情况：如果按钮文字为空，添加兜底文字 */
[onclick*="bb_buycoin(1)"] span:empty::after {
    content: "买入";
    color: #fff !important;
    font-size: 14px !important;
}

[onclick*="bb_sellcoin(2)"] span:empty::after {
    content: "卖出";
    color: #fff !important;
    font-size: 14px !important;
}

.transbtn.bggreen span:empty::after {
    content: "买入";
    color: #fff !important;
}

.transbtn.bgred span:empty::after {
    content: "卖出";
    color: #fff !important;
}

/* 强制所有可能的按钮文字可见 */
.f12, .f14, .f16, .fzmm, .fzmmm, .fcf, .fcc, .fcy {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: inherit !important;
    overflow: visible !important;
    display: inline !important;
}

/* 确保按钮容器不被遮挡 */
.formbox, .tradebox, .contentbox, .btnbox_l, .btnbox_r {
    position: relative !important;
    z-index: 1000 !important;
}

/* 最终兜底：所有包含中文的元素 */
*:contains("买入"), *:contains("卖出"), *:contains("登录"),
*:contains("注册"), *:contains("提交"), *:contains("确认") {
    visibility: visible !important;
    opacity: 1 !important;
    text-indent: 0 !important;
    color: inherit !important;
    overflow: visible !important;
}



/* --- Global hard fix for invisible texts inside green/red boxes and login/register buttons --- */
/* Ensure text in common action containers is visible and not clipped/hidden by any prior theme rules */
.transbtn, .bggreen, .bgred, .tradebox_l_buybtn, .tradebox_l_sellbtn,
.action-button, .btn, a.btn, .btn-success, .btn-green, .jy-btn, .jy-btn-buy,
.btn_bg_color, .btn_bg_color_tcg, .btn_bg_color_tch {
  visibility: visible !important;
  opacity: 1 !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  color: #fff !important;
  -webkit-text-fill-color: #fff !important; /* fix Safari/WebKit */
  overflow: visible !important;
}

/* Ensure nested inline text elements are visible */
.transbtn *, .bggreen *, .bgred *, .tradebox_l_buybtn *, .tradebox_l_sellbtn *,
.action-button *, .btn *, a.btn *, .jy-btn *, .jy-btn-buy * {
  visibility: visible !important;
  opacity: 1 !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  color: inherit !important;
  -webkit-text-fill-color: currentColor !important;
  display: inline !important;
}

/* Explicit white text for green/red backgrounds for best contrast */
.bggreen, .bgred, .tradebox_l_buybtn, .tradebox_l_sellbtn,
.bggreen span, .bgred span, .tradebox_l_buybtn span, .tradebox_l_sellbtn span {
  color: #fff !important;
  -webkit-text-fill-color: #fff !important;
}

/* Fallback content injection when spans are empty */
[onclick*="bb_buycoin(1)"] span:empty::after { content: '买入'; color:#fff; }
[onclick*="bb_sellcoin(2)"] span:empty::after { content: '卖出'; color:#fff; }
.transbtn.bggreen span:empty::after { content: '买入'; color:#fff; }
.transbtn.bgred span:empty::after { content: '卖出'; color:#fff; }

/* Login/Register primary actions fallback (server i18n failure safe) */
button.action-button[onclick*="uplogin"]:empty::after { content: '登录'; }
button.action-button[onclick*="upreg"]:empty::after { content: '立即注册'; }

/* Avoid any accidental text masking via background-clip/text-fill from other themes */
.transbtn, .bggreen, .bgred, .action-button, .btn, a.btn {
  -webkit-background-clip: border-box !important;
  background-clip: border-box !important;
}


/* 秒合约：金额块和周期块可视修复 */
.dong_money_list_box_option, .dong_order_option_list {
  background: #2c2d2e !important;
  color: #fff !important;
  border: 1px solid rgba(255,255,255,0.08) !important;
}
.dong_money_list_box_option span, .dong_order_option_list span,
.vo-time, .vo-ykbl { color: #fff !important; }

/* 选中态与 hover 提示 */
.option_list_active,
.dong_money_list_box_option.option_list_active,
.dong_order_option_list.option_list_active { background: #f5465c !important; color: #fff !important; }
.dong_money_list_box_option:hover, .dong_order_option_list:hover {
  background: #3a3f46 !important; color: #fff !important;
}

/* 周期方块内层文字居中可见 */
.dong_order_option_list > div { color:#fff !important; }

/* 百分比/数字灰色块修复 */
.dong_order_option_list, .dong_money_list_box_option { box-shadow: none !important; }

/* Captcha image: fixed height 50px, auto width, always visible and above overlays */
.captcha-img, img.captcha-img {
  display: inline-block !important;
  width: 100% !important;
  height: 50px !important;
  object-fit: contain !important;
  border-radius: 5px !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10000 !important;
}

/* ===== Login/Register layout fixes (scoped) ===== */
/* Respect explicit display:none inline styles (avoid forcing hidden controls to show) */
button[style*="display:none"],
[style*="display: none"] { display: none !important; }

/* Contain floats and prevent children from visually overflowing the card */
.card-form { overflow: hidden !important; }

/* Inputs stay within the card width */
.card-form .input-field {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Toggle buttons (Mobile / E-mail) alignment */
.card-form .input .btn-hover { width: 48% !important; height: 36px !important; line-height: 36px !important; }
.card-form .input .btnl, .card-form .input .btnr { position: static !important; margin-bottom: 1rem !important; }
.card-form .input .btnl { float: left !important; margin-right: 4% !important; }
.card-form .input .btnr { float: right !important; }

/* Ensure verification code row doesn't spill out */
.css-hiy16i { overflow: hidden !important; }


/* Verification section layout in login/register */
.css-15651n7 .css-13xytqh { box-sizing: border-box !important; }
.css-15651n7 .css-13xytqh:first-child { width: 60% !important; float: left !important; }
.css-15651n7 .css-13xytqh:last-child { width: 35% !important; float: right !important; padding: 0 !important; }
/* Login box verification row */
.card-form .input .login-send-button { box-sizing: border-box !important; }


/* Login page vcode row stable layout */
.input.input-vcode { display: block !important; padding-top: 1.5rem !important; }
.input.input-vcode .input-field { width: 60% !important; float: left !important; }
.input.input-vcode .login-send-button { width: 35% !important; float: right !important; padding: 0 !important; background: transparent !important; }
.input.input-vcode::after { content:""; display:block; clear:both; }

/* Trade page: ensure green/red box texts visible and layout stable */
/* Make sure BUY/SELL tabs text shows */
#buybtnspan, #sellbtnspan,
.tradebox_l_buybtn span, .tradebox_l_sellbtn span {
  visibility: visible !important;
  opacity: 1 !important;
  text-indent: 0 !important;
  color: #fff !important;
  -webkit-text-fill-color: #fff !important;
  font-size: 14px !important;
  line-height: 36px !important;
}
#buybtnspan:empty::after { content: '买入'; color: #fff !important; }
#sellbtnspan:empty::after { content: '卖出'; color: #fff !important; }

/* Ensure clickable BUY/SELL action bars are above any overlays */
div[onclick*="bb_buycoin"], div[onclick*="bb_sellcoin"],
#buybtn, #sellbtn {
  position: relative !important;
  z-index: 10005 !important;
  display: block !important;
  overflow: visible !important;
}

/* Stabilize form-op toggle row layout */
.formbox_op { display: block !important; overflow: hidden !important; }
.formbox_op .formbox_op_list { width: 48% !important; float: left !important; margin-right: 2% !important; text-align: center !important; }
.formbox_op .formbox_op_list:last-child { margin-right: 0 !important; }
.formbox_op .formbox_op_list span { display: inline-block !important; text-indent: 0 !important; }

/* Titles/labels inside trade forms (e.g., 买入价/买入量/可用) */
.klinetitle-s-box, .input-desc, .formbox .f12, .formbox .f14 {
  visibility: visible !important; opacity: 1 !important; text-indent: 0 !important; color: #cfd3dc !important;
}

/* Mobile bottom bar buttons safety net */
.transbtn span { color: #fff !important; -webkit-text-fill-color: #fff !important; }
.transbtn.bggreen span:empty::after { content: '买入'; color:#fff !important; }
.transbtn.bgred span:empty::after { content: '卖出'; color:#fff !important; }

/* Absolute fallback: render labels via ::after on the button containers */
/* Absolute fallback via ::after for BUY/SELL is disabled to avoid duplicate labels.
   We rely on span:empty::after rules instead. */

/* Global: 秒合约（合约交易）“买涨/买跌”按钮文字强制显示与兜底 */
.ks_buy_up, .ks_buy_down,
.btnbox_l, .btnbox_r {
  position: relative !important;
  z-index: 10005 !important;
  visibility: visible !important;
  opacity: 1 !important;
  text-indent: 0 !important;
  overflow: visible !important;
}

.ks_buy_up .ks_buy, .ks_buy_down .ks_buy,
.btnbox_l span, .btnbox_r span {
  visibility: visible !important;
  opacity: 1 !important;
  text-indent: 0 !important;
  color: #fff !important;
  -webkit-text-fill-color: #fff !important;
  display: inline-block !important;
  line-height: inherit !important;
}

/* 若文字为空，追加兜底 */
.ks_buy_up .ks_buy:empty::after, .btnbox_l span:empty::after { content: '买涨'; color: #fff !important; }
.ks_buy_down .ks_buy:empty::after, .btnbox_r span:empty::after { content: '买跌'; color: #fff !important; }

/* 绝对兜底：容器层直接绘制文字，避免内部被隐藏 */
/* 合约按钮容器 ::after 兜底临时停用，避免重复文案。需要时可按需开启
.ks_buy_up::after,
.ks_buy_down::after { display:none !important; }
*/



/* === UI 微调：按钮字号与金额方块居中 === */
/* 买涨/买跌按钮：更大字号并强制居中 */
.ks_buy_up, .ks_buy_down, .btnbox_l, .btnbox_r {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}
.ks_buy_up .ks_buy, .ks_buy_down .ks_buy,
.btnbox_l span, .btnbox_r span {
  font-size: 18px !important;
  line-height: 1 !important;
}

/* 金额方块：数字完全居中 */
.dong_money_list_box_option {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  line-height: 1 !important;
}
.dong_money_list_box_option span {
  display: inline-block !important;
  text-align: center !important;
}
