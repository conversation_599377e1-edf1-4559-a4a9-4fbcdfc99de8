<!DOCTYPE html>
<html   >
<head>
    <meta charset="UTF-8">
    <title>{$webname}</title>
    <link rel="stylesheet" href="/Public/Home/login/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />

    <link rel="stylesheet" href="/Public/Dela/demo/demo.css">
    <link rel="stylesheet" href="/Public/Dela/template/dela-template.css">
    <link rel="stylesheet" href="/Public/build/css/countrySelect.css">

    <link rel="stylesheet" href="/Public/Home/static/css/style.css">

    <style>
        .main_demo {
            width: 28% !important;
            float: left;
        }

        .container-input {
            padding-left: 0px;
            width: 60%;
            margin: 0px;
        }

        button {
            appearance: auto;
            writing-mode: horizontal-tb !important;
            font-style: ;
            font-variant-ligatures: ;
            font-variant-caps: ;
            font-variant-numeric: ;
            font-variant-east-asian: ;
            font-weight: ;
            font-stretch: ;
            font-size: ;
            font-family: ;
            text-rendering: auto;
            color: buttontext;
            letter-spacing: normal;
            word-spacing: normal;
            line-height: normal;
            text-transform: none;
            text-indent: 0px;
            text-shadow: none;
            display: inline-block;
            text-align: center;
            align-items: flex-start;
            cursor: default;
            box-sizing: border-box;
            background-color: buttonface;
            margin: 0em;
            padding: 1px 6px;
            border-width: 2px;
            border-style: outset;
            border-color: buttonborder;
            border-image: initial;
        }
        .btn-hover {
            width: 7em;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            cursor: pointer;
            height: 30px;
            text-align: center;
            border: none;
            background-size: 300% 100%;
            border-radius: 50px;
            moz-transition: all .4s ease-in-out;
            -o-transition: all .4s ease-in-out;
            -webkit-transition: all .4s ease-in-out;
            transition: all .4s ease-in-out;
        }
        .btn-hover.color-1 {
            /*color: #0052fe;*/
            color: #6d7c82;
            border: none !important;
            /*background-image: linear-gradient(to right, #25aae1, #40e495, #30dd8a, #2bb673);*/
            /*box-shadow: 0 4px 15px 0 rgb(49 196 190 / 75%);*/
        }
        .btn-hover.color-6 {
            /*color: #0052fe;*/
            color: #6d7c82;
            border: none !important;
            /*background-image: linear-gradient(to right, #009245, #FCEE21, #00A8C5, #D9E021);*/
            /*box-shadow: 0 4px 15px 0 rgb(83 176 57 / 75%);*/
        }
        .btnl {
            float: left;
            margin-bottom:1.1em;

        }
        .btnr {
            float: right;
            margin-bottom:1.1em;
            position: absolute;
            left: 40%;
        }
        .code {
            width: 100% !important;
        }
        .send-btn {
            width: 20%;
            height: 1.8em;
            position: absolute;
            top: 415px;
            left: 220px;

        }
        .mobile_input {
            width: 70% !important;
            float: right;
        }

        .btn-hover.color-4 {
            background-image: linear-gradient(to right, #fc6076, #ff9a44, #ef9d43, #e75516);
            box-shadow: 0 4px 15px 0 rgb(252 104 110 / 75%);
        }

        .css-jmskxt {
            background: #000000;
        }

        .country-name {
            color: #000;
        }

    </style>
</head>
<body>
<include file="Public:header"/>



<!-- partial:index.partial.html -->
<div class="starfield">
    <div class="static"></div>
    <div class="moving-1"></div>
    <div class="moving-2"></div>
    <div class="moving-3"></div>
</div>

<!-- partial -->





<!-- partial:index.partial.html -->
<div class="container">
    <!-- code here -->
    <div class="card">
        <form class="card-form" onclick="return false">
            <div class="margin-topbox-px-10">
                <div data-bn-type="text" class="css-1g5tc38  tcl fch f36 fw">{:L('立即注册')}<span class="floginbr"> {:get_config('webname')}</span></div>
                <div data-bn-type="text" class="css-152kxht tcl f16"></div>
            </div>

            <div class="input">
                <button class="btn-hover color-6 btnr" onclick="stype(2)">E-mail</button>
                <button class="btn-hover color-1 btnl" onclick="stype(1)">Mobile</button>
            </div>

            <input type="hidden" name="type" id="type" value="1">
            <input type="hidden" name="account" id="account" >
            <div class="input"  id="e-box" style="display: none">
                <input type="text" class="input-field" name="email" id="email" value=""  required/>
                <label class="input-label">{:L('邮箱')}</label>
            </div>

            <div id="main_demo" style="margin-bottom:1.1em;display: block" >
                <div class="container input container-input">
                    <div class="form-item">
                        <input id="country_selector" style="width: 367px;border-bottom: 1px solid #eee;" class="input-field"  type="text">
                        <label for="country_selector" style="display:none;">Select a country here...</label>
                    </div>
                    <button type="submit" style="display:none;" aria-hidden="true" tabindex="-1">Submit</button>
                </div>
            </div>


            <div class="input" style="margin-top: 1.5rem;">
                <input type="password" class="input-field"  name="lpwd" id="lpwd" value="" required/>
                <label class="input-label">{:L('密码')}</label>
            </div>

            <div class="input">
                <input type="text" class="input-field"  name="invit" id="invit" value="{$qrcode}" required/>
                <label class="input-label">{:L('邀请码')}</label>
            </div>

            <div class="css-15651n7">
                <div class="css-xjlny9">{:L('图形验证码')}</div>
                <div class="css-hiy16i" style="height: 48px;">
                    <div class="css-13xytqh" style="width:60%;float:left;">
                        <input  type="text" id="vcode" name="vcode" class="input-field" value="">
                    </div>
                    <div class="css-13xytqh" style="width:30%;float:right;">
                        <img class="captcha-img" src="{:U('Home/Verify/code')}" onclick="this.src='{:U('Home/Verify/code')}&t='+Math.random()" title="{:L('换一张')}">
                    </div>
                </div>
            </div>
            <br>

            <div class="input" id="yzm">
                <input type="text" class="input-field"  name="ecode" id="ecode" value="" style="width: 70%" required />
                <label class="input-label">{:L('验证码')}</label>
                <div id="sendsms" class="login-send-button tcc" onclick="emailsend();" style="width: 30%; float:right; background:transparent; line-height: 38px;">
                     <span class="f12 fcy" id="smsstr" style="margin: 0px;color: #fff">{:L('获取验证码')}</span>
                </div>
            </div>
            <div class="action">
                <button class="action-button " onclick="upreg()">{:L('立即注册')}</button>
            </div>
        </form>
        <div class="login-reg tcr">
            <a href="{:U('Login/findpwd')}"  class="css-s84t59 fbaseblue" >{:L('忘记密码')}？</a>
            <a href="{:U('Login/index')}"  class="css-utqtyo fbaseblue" >{:L('登录')}</a>
        </div>
    </div>
</div>
<!-- partial -->

</body>


<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
<script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>

<script type="text/javascript">

    function upreg(){
        var  type = $('#type').val();
         console.log(type);
        if (type != 1 && type != 2) {
            layer.msg("{:L('请选择注册类型')}");return false;
        }
        // return false;

        if (type == 1) {
            var account = $("#country_selector").val();
            if(account=='' || account == null){
                layer.msg("{:L('请输入密码')}");return false;
            }
        }
        if (type == 2) {
            var email = $("#email").val();
            var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
            if(email=='' || email == null){
                layer.msg("{:L('请输入邮箱')}");return false;
            }
            if(!reg.test(email)){
                layer.msg("{:L('邮箱格式不正确')}");return false;
            }
            var account = email;

        var ecode = $("#ecode").val();
        if(ecode == ''){
            layer.msg("{:L('请输入邮箱验证码')}");return false;
        }

        }

        var lpwd = $("#lpwd").val();
        if(lpwd == ''){
            layer.msg("{:L('请输入密码')}");return false;
        }
        var invit = $("#invit").val();
        if( invit == ''){
            layer.msg("{:L('请输入邀请码')}");return false;
        }
        console.log(type)
        console.log(account)
        console.log(ecode)
        console.log(lpwd)
        console.log(invit)

        $.post("{:U('Login/upregister')}",
            {'account' : account, 'ecode' : ecode, 'lpwd' : lpwd, 'invit' : invit, 'type' : type},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.href="{:U('Login/index')}";
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            }
        );


    }
</script>

    <script type="text/javascript">
        function emailsend(){
              var  type = $('#type').val();
             if(type == 1){
             var email = $("#country_selector").val();
        }else{
        var email = $("#email").val();

        }
           //  if (type == 2) {
            var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
           if (type == 2) {
            if(email=='' || email == null){
                layer.msg("{:L('请输入邮箱')}");return false;
            }
            if(!reg.test(email)){
                layer.msg("{:L('邮箱格式不正确')}");return false;
            }
           }

            var vcode = $("#vcode").val();
            if(vcode == ''){
                layer.msg("{:L('请输入图形验证码')}");return false;
            }
            $.post("{:U('Login/sendcode')}",
            {'email':email,'vcode':vcode,'type':type},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    var obj = $("#sendsms");
                    var strobj = $("#smsstr");
                    var t = 60;
                    var interval = setInterval(function() {
                        obj.removeAttr('onclick');
                        strobj.text(t + "{:L('秒后再发送')}");
                        t--;
                        if(t < 1){
                            obj.attr("onclick","emailsend();");
                            clearInterval(interval);
                            strobj.text("{:L('获取验证码')}");
                        }
                    },1000);
                }else{
                    layer.msg(data.info);
                    $("#sendsms").attr("onclick","emailsend();");
                    $("#smsstr").text("{:L('获取验证码')}");
                    $("#verifycode").click();
                }
            });
        //}
        }
    </script>
<script type="text/javascript">

    function stype(type) {
       $('.btn-hover').css('color', '#6d7c82')
        if (type == 2) {
            $('#e-box').show();
            //  $('#yzm').show();
            $('#main_demo').hide();
            $('.btnr').css('color','#0052fe')

        }
        if (type == 1) {
            $('#e-box').hide();
           //  $('#yzm').hide();
            $('#main_demo').show();
            $('.btnl').css('color','#0052fe')

        }
        $('#type').val(type);

    }

    function goindex(){
        window.location.href="{:U('Index/index')}";
    }

    function uplogin(){
        window.location.href="{:U('Login/index')}";
    }

    function forgot_password() {
        window.location.href="{:U('Login/findpwd')}";
    }


</script>

<script type="text/javascript">
    $("#nav").slide({
        type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
        titCell:".nLi", //鼠标触发对象
        targetCell:".sub", //titCell里面包含的要显示/消失的对象
        effect:"slideDown", //targetCell下拉效果
        delayTime:300 , //效果时间
        triggerTime:0, //鼠标延迟触发时间（默认150）
        returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
    });
</script>

<script src="/Public/build/js/countrySelect.js"></script>
<script>
    $("#country_selector").countrySelect({
        preferredCountries: ["jp","us"]
    });
</script>

</html>
