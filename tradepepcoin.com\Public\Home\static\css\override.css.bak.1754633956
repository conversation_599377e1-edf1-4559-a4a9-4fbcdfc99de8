/* force show for sliders */
.tempWrap, .bd { visibility: visible !important; opacity: 1 !important; }
.bd li, .bd li img { display: block !important; }
.banner img, .slideBox img, .focusBox img, .bd img { max-width: 100% !important; height: auto !important; }
.banner, .slideBox, .focusBox, .bd { min-height: 220px !important; }
/* force show ALL images */
img { visibility: visible !important; opacity: 1 !important; display: inline-block !important; }
img[src*=".png"], img[src*=".jpg"], img[src*=".jpeg"], img[src*=".gif"], img[src*=".svg"] { display: inline-block !important; }
/* fix: buttons text invisible */
.btn, a.btn, button.btn, .btn-success, .btn-green, .trade-btn, .opt-btn, .buy-btn, .sell-btn {
  font-size: 14px !important;
  line-height: 34px !important;
  color: #fff !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  overflow: visible !important;
}
.btn span, .btn i, .btn em { font-size: inherit !important; }

/* fix: admin/agent 登录页验证码看不见 */
#verifycode, img#verifycode, .login-box #verifycode, .login-form #verifycode {
  display: inline-block !important;
  width: 120px !important;
  height: 42px !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: transparent !important;
}

/* 通用图片可见（兜底） */
img { visibility: visible !important; opacity: 1 !important; }
/* 允许按钮点击 & 恢复文字 */
a, a.btn, .btn, .btn-success, .btn-green, .trade-btn, .opt-btn, .buy-btn, .sell-btn {
  pointer-events: auto !important;
  cursor: pointer !important;
  text-indent: 0 !important;
  font-size: 14px !important;
  line-height: 34px !important;
  color: #fff !important;
  overflow: visible !important;
}

/* 常见遮罩禁止拦截点击（如有透明层覆盖） */
[class*="mask"], [class*="overlay"], [class*="shade"], .modal-backdrop, .modal-mask, .page-mask {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 兜底：所有图片可见 */
img { visibility: visible !important; opacity: 1 !important; display: inline-block !important; }
/* bring buttons above any overlay and enable clicks */
a.btn, .btn, .btn-green, .btn-success, .trade-btn, .opt-btn, .buy-btn, .sell-btn,
table tr td:last-child a, table tr td:last-child .btn {
  position: relative !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
  color: #fff !important;
  text-indent: 0 !important;
}

/* 提升按钮所在单元格层级 */
table tr td:last-child, .opt, .opt-cell, .operate, .operate-cell {
  position: relative !important;
  z-index: 9998 !important;
}

/* 降低常见透明遮罩的层级并禁用拦截点击（即便存在未知类名的遮罩也不再挡点击） */
[class*="mask"], [class*="overlay"], [class*="shade"],
.modal-backdrop, .modal-mask, .page-mask, .page-overlay, .page-shade {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 登录页验证码区域抬高，避免被白块覆盖 */
#verifycode, img#verifycode { position: relative !important; z-index: 10000 !important; }
/* 禁用透明覆盖层的点击拦截（逐一列出右上导航/覆盖容器的哈希类） */
.css-11y6cix,
.css-wu6zme,
.css-1ql2hru,
.order_navlist,
.trad_navlist,
.trad-sub {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 只给链接元素恢复点击 */
.css-wu6zme a,
.css-1ql2hru a,
.order_navlist a,
.trad_navlist a,
.trad-sub a,
.nav a,
a {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 右列操作区整体抬高，确保在最上层 */
table tr td:last-child,
.operate,
.opt,
.operate-cell,
.opt-cell {
  position: relative !important;
  z-index: 9999 !important;
}

/* 按钮容器（常见标签）显示文字、消除缩进 */
.optionli span,
.css-vurnku,
.f16 {
  display: inline-block !important;
  text-indent: 0 !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 34px !important;
}

/* 强制关闭常见透明覆盖层的点击拦截 */
.css-11y6cix,
.css-wu6zme,
.css-1ql2hru,
.order_navlist,
.trad_navlist,
.trad-sub {
  pointer-events: none !important;
  z-index: 0 !important;
  background: transparent !important;
}

/* 链接恢复点击 */
.css-wu6zme a,
.css-1ql2hru a,
.order_navlist a,
.trad_navlist a,
.trad-sub a,
.nav a,
a {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 操作列抬高，确保可点 */
table tr td:last-child,
.operate,
.opt,
.operate-cell,
.opt-cell {
  position: relative !important;
  z-index: 9999 !important;
}

/* 按钮文本样式兜底（显示白字） */
.option-box a,
.optionli span,
.css-vurnku,
.f16 {
  display: inline-block !important;
  text-indent: 0 !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 34px !important;
}
/* 让按钮里的 <a> 必定可见可点 */
.market-div .option-box a,
a[href^="/Trade/index"],
a[href^="/Contract/index"],
a[href^="/Issue/details"] {
  display: inline-block !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 34px !important;
  text-indent: 0 !important;
}

/* 对空 <a> 注入可见文本（不改 HTML，用 ::after 渲染） */
a[href^="/Trade/index"]::after     { content: 'Buy'; color:#fff; }
a[href^="/Contract/index"]::after  { content: 'Contract'; color:#fff; }
a[href^="/Issue/details"]::after   { content: 'Details'; color:#fff; }

/* 行容器抬高，避免被透明层覆盖 */
.market-div .option-box,
.market-div .option-box a {
  position: relative !important;
  z-index: 10001 !important;
}

/* 顶部透明层默认不拦截，链接恢复点击 */
.css-11y6cix, .css-wu6zme, .css-1ql2hru, .order_navlist, .trad_navlist, .trad-sub {
  pointer-events: none !important; z-index: 0 !important; background: transparent !important;
}
.css-wu6zme a, .css-1ql2hru a, .order_navlist a, .trad_navlist a, .trad-sub a, .nav a, a {
  pointer-events: auto !important; cursor: pointer !important;
}
/* 全站：禁用透明遮罩拦截，链接/按钮恢复可点 */
[class*="mask"], [class*="overlay"], [class*="shade"], .css-11y6cix,
.trad-sub, .order_navlist, .css-wu6zme, .css-1ql2hru {
  pointer-events: none !important; z-index: 0 !important; background: transparent !important;
}
a, button, [role="button"], .action-button { pointer-events: auto !important; cursor: pointer !important; }

/* 通用：任何被隐藏的文字恢复显示 */
a, button, [role="button"], .action-button, .btn {
  visibility: visible !important; opacity: 1 !important; filter: none !important;
  text-indent: 0 !important; letter-spacing: normal !important;
}

/* Trade/Contract 交易区块：提升层级，避免被覆盖 */
.tradebox, .tradebox * { position: relative; z-index: 10001; }

/* 交易页买/卖按钮内文字显式为白色（绿色/红色底上清晰可见） */
[onclick*="bb_buycoin(1)"] span, [onclick*="bb_buycoin(2)"] span,
a[href*="/Login/index"] span { color: #fff !important; }

/* 若模板渲染为空，则兜底自动补文字 */
[onclick*="bb_buycoin(1)"] span:empty::after { content: 'Buy'; color:#fff; }
[onclick*="bb_buycoin(2)"] span:empty::after { content: 'Sell'; color:#fff; }

/* 顶部“登录/注册/交易/充币”等文字确保可见 */
.css-vurnku, .header-title, .optionli span { color: #fff !important; text-indent:0 !important; }

/* 语言/下拉菜单保持可点 */
.nav a, .order_navlist a, .trad_navlist a, .trad-sub a { pointer-events: auto !important; }
