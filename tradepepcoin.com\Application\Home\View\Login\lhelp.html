<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助中心 - {:get_config('webname')}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
    <style>
        .help-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .help-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            transition: transform 0.3s ease;
        }
        .help-card:hover {
            transform: translateY(-5px);
        }
        .help-icon {
            width: 60px;
            height: 60px;
            background: #f0b90b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        .faq-item {
            border-bottom: 1px solid #eee;
            padding: 1.5rem 0;
        }
        .faq-question {
            font-weight: 600;
            color: #333;
            cursor: pointer;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .faq-answer {
            color: #666;
            margin-top: 1rem;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <include file="Public/header" />
    
    <!-- Help Header -->
    <section class="help-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-4 fw-bold mb-3">帮助中心</h1>
                    <p class="lead">为您提供全面的平台使用指南和常见问题解答</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Help Categories -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card help-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="help-icon">
                                <i class="bi bi-person-plus text-white fs-3"></i>
                            </div>
                            <h5 class="card-title">新手指南</h5>
                            <p class="card-text">快速了解平台功能，轻松开始数字资产交易</p>
                            <a href="#newbie" class="btn btn-outline-primary">查看详情</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card help-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="help-icon">
                                <i class="bi bi-shield-check text-white fs-3"></i>
                            </div>
                            <h5 class="card-title">安全中心</h5>
                            <p class="card-text">了解账户安全设置，保护您的数字资产</p>
                            <a href="#security" class="btn btn-outline-primary">查看详情</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card help-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="help-icon">
                                <i class="bi bi-graph-up text-white fs-3"></i>
                            </div>
                            <h5 class="card-title">交易指南</h5>
                            <p class="card-text">掌握交易技巧，提升投资收益</p>
                            <a href="#trading" class="btn btn-outline-primary">查看详情</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h2 class="text-center mb-5">常见问题</h2>
                    
                    <div class="faq-item" id="newbie">
                        <div class="faq-question">
                            <span>如何注册账户？</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            1. 点击页面右上角"注册"按钮<br>
                            2. 填写手机号码或邮箱地址<br>
                            3. 设置登录密码<br>
                            4. 输入验证码完成注册
                        </div>
                    </div>

                    <div class="faq-item" id="security">
                        <div class="faq-question">
                            <span>如何设置安全验证？</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            1. 登录后进入"安全设置"页面<br>
                            2. 绑定手机号码进行短信验证<br>
                            3. 设置资金密码<br>
                            4. 开启谷歌验证器（推荐）
                        </div>
                    </div>

                    <div class="faq-item" id="trading">
                        <div class="faq-question">
                            <span>如何进行数字货币交易？</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            1. 完成身份认证<br>
                            2. 充值数字货币到账户<br>
                            3. 选择交易对进入交易页面<br>
                            4. 设置买入/卖出价格和数量<br>
                            5. 确认交易信息并提交订单
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span>充值和提现需要多长时间？</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            充值：区块链确认后即时到账，通常需要1-6个区块确认<br>
                            提现：人工审核通过后发送到区块链，通常在24小时内处理
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span>忘记密码怎么办？</span>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            1. 点击登录页面"忘记密码"链接<br>
                            2. 输入注册时的手机号或邮箱<br>
                            3. 获取验证码<br>
                            4. 设置新密码完成重置
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h3 class="mb-4">还有其他问题？</h3>
                    <p class="text-muted mb-4">我们的客服团队随时为您提供帮助</p>
                    <div class="row g-3 justify-content-center">
                        <div class="col-auto">
                            <a href="mailto:support@{:get_config('webname')}" class="btn btn-primary">
                                <i class="bi bi-envelope me-2"></i>邮件咨询
                            </a>
                        </div>
                        <div class="col-auto">
                            <a href="{:U('Index/gglist')}" class="btn btn-outline-primary">
                                <i class="bi bi-megaphone me-2"></i>查看公告
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <include file="Public/footer" />

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // FAQ toggle functionality
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', () => {
                const answer = question.nextElementSibling;
                const icon = question.querySelector('i');
                
                if (answer.style.display === 'block') {
                    answer.style.display = 'none';
                    icon.classList.remove('bi-chevron-up');
                    icon.classList.add('bi-chevron-down');
                } else {
                    answer.style.display = 'block';
                    icon.classList.remove('bi-chevron-down');
                    icon.classList.add('bi-chevron-up');
                }
            });
        });
    </script>
</body>
</html>
