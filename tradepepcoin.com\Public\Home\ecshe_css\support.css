@charset "utf-8";
/* CSS Document */

body{margin:0;padding:0;font-size:12px;line-height:22px;font-family:"Microsoft YaHei",Arial;-webkit-text-size-adjust:none;}
html,body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td,p{margin:0;padding:0;}
input,select,textarea{font-size:12px;line-height:16px;}img{border:0;}ul,li{list-style-type:none;}
a{color:#333;text-decoration:none;}
.tc{text-align:center;}.tl{text-align:left;}.tr{text-align:right;}
.dis{display:block;}.undis{display:none;}
.fl{float:left;}.fr{float:right;}.cl{clear:both;}.fb{font-weight:bold;}.fnb{font-weight:200;margin-left:-1px;}
.hr_1,.hr_10{font-size:1px;line-height:1px;clear:both;}
.hr_1{height:1px;}.hr_10{height:10px;}
input[type="text"]:focus,input[type="password"]:focus,textarea:focus{outline:none;}


.header{
	position: relative;
	margin: 0 auto;
	max-width: 1160px;
	height: 71px;
	padding: 0 5%;
	align-items: center;
	display: flex;
	justify-content: space-between;
}
@media (min-width: 1160px){
	.header {
		padding: 0;
		width: 90%;
	}
}
.header .logo img{
	max-height: 50px;
}

.header .language {
	display: inline-block;
	cursor: pointer;
}
.header .language img {
	vertical-align: middle;
}
.header .language label {
	color: #0754b4;
	font-size: 15px;
	font-weight: 300;
	margin-right: 8px;
	cursor: pointer;
}
.header .language .arrow-down {
	width: 10px;
	height: 6px;
}
.header .language-box {
	display: none;
	padding: 10px 0;
	position: absolute;
	width: 134px;
	background: #fff;
	border: 1px solid #d8d8d8;
	border-radius: 3px;
	box-shadow: 0 1px 5px rgba(0, 0, 0, .1);
	right: 0;
	top: 50px;
	z-index: 1000;
}
.header .language-box p a{
	display: block;
	padding: 7px 40px 7px 20px;
	overflow: hidden;
	color: #333;
	font-size: 16px;
}
.header .language-box p a:hover{
	background: #f3f3f3;
	color: #0754b4;
}


.main .container-divider{
	border-top: 1px solid #ddd;
    margin-bottom: 20px;
}

.main .hero{
	margin-bottom: 60px;
	height: 200px;
    padding: 0 20px;
    text-align: center;
	background: url('../ecshe_img/bg_support.jpg') center;
	background-size: cover;
}

.main .hero-inner{
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    max-width: 610px;
    margin: 0 auto;
}
.main .hero-inner .search-full input{
    max-width: 100%;
	font-size: 1rem;
	font-weight: 300;
    outline: none;
    transition: border .12s ease-in-out;
}
.main .hero-inner .search-full input[type="search"]{
    border: 1px solid #fff;
	border-radius: 30px;
    box-sizing: border-box;
    color: #999;
    height: 40px;
    padding-left: 40px;
    padding-right: 20px;
    -webkit-appearance: none;
    width: 100%;
}
.main .hero-inner .search-full .icon{
	position: absolute;
	top:25%;
	left: 15px;
	height: 18px;
}


.container{
    max-width: 1160px;
    margin: 0 auto;
    padding: 0 5%;
}
@media (min-width: 768px){
	.container .list {margin: 0 -15px;}
}
.container .list{
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 60px;
}
.container .list li{
	margin: 0 0 30px;
	max-width: 100%;
	display: flex;
	flex: 1 0 340px;
	flex-direction: column;
	justify-content: center;
	text-align: center;
	border: 1px solid #0754b4;
	border-radius: 4px;
}
@media (min-width: 768px){
	.container .list li{margin: 0 15px 30px;}
}
.container .list li a{
	color: #0754b4;
	font-size: 17px;
	padding: 20px 30px;
	text-align: center;
}
.container .list li a:hover{
	color: #fff;
	background-color: #0754b4;
}

.container .recent-activity{
	border-top: 1px solid #ddd;
    padding: 30px 0;
}

.container .recent-activity .recent-activity-header{
	margin-bottom: 10px;
    text-align: center;
	font-size: 22px;
	font-weight: 400;
}


.container .recent-activity-list{
	
}
.container .recent-activity-list li{
	padding: 20px 0;
	border-bottom: 1px solid #ddd;
}
.container .recent-activity-list li a{
	display: inline-block;
}
.container .recent-activity-list li a h3{
	margin: 6px 0;
	color: #333;
	font-size: 16px;
	font-weight: 500;
}
.container .recent-activity-list li a p{
	margin: 6px 0;
	color: #333;
	font-size: 14px;
	font-weight: 300;
}
.container .recent-activity-list li span{
	display: inline-block;
	float: none;
	color: #666;
	font-size: 13px;
    font-weight: 300;
	margin: 15px 0 0 0;
}
@media (min-width: 768px){
	.container .recent-activity-list li span{float:right;margin:0;margin-top:20px;}
}


.container .sub-nav{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 30px;
    min-height: 50px;
    padding-bottom: 15px;
}
@media (min-width: 768px){
	.container .sub-nav {align-items:baseline;flex-direction:row;}
}

.container .sub-nav ol{
    margin: 0 0 15px 0;
    padding: 0;
}
@media (min-width: 768px){
	.container .sub-nav ol {margin: 0;}
}
.container .sub-nav ol li{
    display: inline;
	max-width: 450px;
    overflow: hidden;
    font-weight: 300;
    color: #949494;
	font-size: 15px;
    text-overflow: ellipsis;
}
.container .sub-nav ol li span{
	margin: 0 8px;
}
.container .sub-nav ol li a{
	color: #0754b4;
}

.container .sub-nav .search{
	position: relative;
}
.container .sub-nav .search input[type="search"] {
    border: 1px solid #ddd;
    border-radius: 30px;
    box-sizing: border-box;
    color: #999;
    height: 40px;
    padding-left: 40px;
    padding-right: 20px;
    -webkit-appearance: none;
    width: 100%;
}
@media (min-width: 768px){
	.container .sub-nav .search input[type="search"] {min-width:300px;}
}
.container .sub-nav .search input{
    max-width: 100%;
	font-size: 1rem;
	font-weight: 300;
    outline: none;
    transition: border .12s ease-in-out;
}
.container .sub-nav .search .icon{
	position: absolute;
	top:25%;
	left: 15px;
	height: 18px;
}


.container .category-content{
	
}
.container .category-content .page-header{
	display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 10px 0;
}
@media (min-width: 768px){
	.container .category-content .page-header{align-items:baseline;flex-direction:row;margin:0;}
}
.container .category-content .page-header h1{
	margin-bottom: 30px;
	font-size: 32px;
	font-weight: 400;
	flex-grow: 1;
	color: #2a3a4b;
}
.container .category-content .page-header p.page-header-description{
    font-style: italic;
	font-size: 17px;
	color: #2a3a4b;
    font-weight: 300;
    margin: 0 0 30px 0;
    word-break: break-word;
}
@media (min-width: 1024px){
	.container .category-content .page-header p.page-header-description{flex-basis: 100%;}
}

.container .category-content .section-tree{
	display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-between;
}
.container .category-content .section-tree .section{
	flex: initial;
	margin-bottom: 40px;
}
@media (min-width: 768px){
	.container .category-content .section-tree{flex-direction: row;}
	.container .category-content .section-tree .section{flex: 0 0 45%;}
}

.container .category-content .section-tree .meta-group{
	margin-top: 5px;
	margin-bottom: 15px;
	color: #929292;
	font-size: 13px;
}
.container .category-content .section-tree .search-result-description{
	color: #2a3a4b;
	font-size: 15px;
}


.container .category-content .section-tree .section h3{
	margin-bottom: 0;
    font-size: 18px;
    font-weight: 600;
}
.container .category-content .section-tree .section h3 a:hover{
	color: #0754b4;
	text-decoration: underline;
}
.container .category-content .section-tree .section ul{
	list-style: none;
    margin: 0;
    padding: 0;
}
.container .category-content .section-tree .section ul li{
    border-bottom: 1px solid #ddd;
    font-size: 16px;
    padding: 15px 0;
}
.container .category-content .section-tree .section ul li a{
    color: #333;
	vertical-align: middle;
}
.container .category-content .section-tree .section ul li a:hover{
	color: #0373fd;
	text-decoration: underline;
}
.container .category-content .section-tree .section ul li img{
    vertical-align: middle;
	height: 22px;
	margin-right: 8px;
}
.container .category-content .section-tree .section .more{
	display: block;
	line-height: 50px;
	font-size: 16px;
	color: #0754b4;
}
.container .category-content .section-tree .section .more:hover{
	text-decoration: underline;
}

.wdh100{margin:0 auto;width:100%;}
@media (min-width: 900px){
	.wdh100{width:800px;}
}

.article-container{
    display: flex;
    flex-direction: column;
}
@media (min-width: 1024px){
	.article-container{flex-direction: row;}
}
.article-container .article-sidebar{
	padding: 10px 0;
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd;
    flex: 1 0 auto;
    margin-bottom: 20px;
}
.article-container .article-sidebar .sidenav-title{
	padding: 10px 0;
	font-size: 15px;
	font-weight: 600;
    position: relative;
}
.article-container .article-sidebar ul{
	margin-top: 10px;
}
.article-container .article-sidebar ul li{
	
}
.article-container .article-sidebar ul li a{
	margin-bottom: 10px;
    padding: 10px;
    display: block;
	color: #333;
	font-size: 15px;
    font-weight: 300;
	border-radius: 4px;
}
.article-container .article-sidebar ul li a.on{
	background-color: #2680f1;
    color: #fff;
    text-decoration: none;
}
@media (min-width: 1024px){
	.article-container .article-sidebar{border:0;flex:0 0 17%;height:auto;}
}
@media (max-width: 640px){
	.article-sidebar ul{display: none;}
}


.article-container .article{
	flex: 1 0 auto;
}
@media (min-width: 1024px){
	.article-container .article{flex:1 0 66%;min-width:640px;padding:0 30px;}
}

.article-container .article .article-header{
	align-items: flex-start;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 20px;
    margin-top: 20px;
}
.article-container .article .article-header h1{line-height:1.5em;font-size:32px;font-weight:400;}
.article-container .article .article-header .times{line-height:3em;font-size:1.2em;color:#b7b7b7;}
@media (min-width: 768px){
	.article-container .article .article-header{flex-direction:row;margin-top:0;}
	.article-container .article .article-title{flex-basis:100%;}
}
.article-content{
	font-size:1rem;
	line-height: 2rem;
}
.article-content img{
	height: auto;
	max-width: 100%;
}


/* Fee */
.table-container{
	overflow: hidden;
}

.fee-table{
    border-collapse: collapse;
	color: #546e7a;
}
.fee-table b{
    font-weight: 700;
}
.fee-table th,.fee-table td{
	padding: 10px;
	border: 1px solid #ddd;
	font-size: 12px;
	text-transform:uppercase;
}
.fee-table th{
	background: #f4f4f4;
	font-size: 12px;
}
.fee-table td.icons{
	text-align: center;
}
.fee-table td img {
	display: block;
	margin: 0 auto;
}

@media (min-width: 768px){
	.fee-table th{
		font-size: 15px;
	}
	.fee-table td{
		font-size: 14px;
	}
	.fee-table td.icons{
		text-align: left;
	}
	.fee-table td img {
		display: inline-block;
		margin-left: 30%;
		margin-right: 5px;
		margin-top: -4px;
		vertical-align: middle;
	}
}

/* 数据分页 */
.pages {
    clear: both;
    margin: 15px 15px 0px;
    text-align: center;
	font-size: 13px;
}
.pages a {
    background-color: #fff;
    padding: 5px 12px;
    border-radius: 3px;
	border: 1px solid #646464;
    margin: 0 2px;
    color: #646464;
	font-size: 13px;
}
.pages a:hover {
	background-color: #ececec;
    text-decoration: none;
}
.pages .current {
    background-color: #106cde;
    color: #fff;
    padding: 5px 12px;
    border-radius: 3px;
    margin: 0 2px;
}
.pages a.arr {
    width: 19px;
    height: 19px;
    display: inline-block;
    border-radius: 0;
    border: 0;
}
.pages a.arr.prev {
    background-position: -182px -286px;
}
.pages a:hover.arr.prev {
    background-position: -182px -265px;
}
.pages a.arr.next {
    background-position: -206px -265px;
}
.pages a:hover.arr.next {
    background-position: -206px -286px;
}

/* Footer */
.footer{
	border-top: 1px solid #ddd;
    margin-top: 60px;
    padding: 30px 0;
}
.footer .footer-inner{
    display: flex;
	max-width: 1160px;
    margin: 0 auto;
    padding: 0 5%;
    color: #666;
	font-size: 16px;
    justify-content: space-between;
}
@media (min-width: 1160px){
	.footer .footer-inner{padding:0;width:90%;}
}
.footer .footer-inner a{
    color: #666;
	font-size: 16px;
}