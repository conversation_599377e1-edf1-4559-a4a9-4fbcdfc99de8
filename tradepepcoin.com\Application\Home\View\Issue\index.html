<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
	    <link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
	    <title>{$webname}</title>
	    <style>
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-1odg5z2 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                height: 260px;
                background-color: #0d202d;
                position: -webkit-sticky;
                position: sticky;
                top: -256px;
                z-index: 1;
                padding: 0;
                height: 360px;
            }
            .css-1odg5z2::before {
                content: "";
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
                background-image: url(/Public/Home/static/imgs/bannerissue.png);
                position: absolute;
                z-index: -1;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                -webkit-transform: none;
                -ms-transform: none;
                transform: none;
            }
            .css-1xrgo9z {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                -webkit-box-pack: start;
                -webkit-justify-content: flex-start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                font-size: 26px;
                color: white;
                z-index: 1;
                height: 100%;
                padding-bottom: 48px;
            }.css-1xrgo9z {
                -webkit-box-pack: start;
                -webkit-justify-content: flex-start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                font-size: 40px;
                padding-bottom: 64px;
            }
            .css-1xrgo9z {
                -webkit-box-pack: center;
                -webkit-justify-content: center;
                -ms-flex-pack: center;
                justify-content: center;
            }
            .css-uliqdc {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
            }
            .progress-bar {
                display: -ms-flexbox;
                display: flex;
                -ms-flex-direction: column;
                flex-direction: column;
                -ms-flex-pack: center;
                justify-content: center;
                overflow: hidden;
                color: #fff;
                text-align: center;
                white-space: nowrap;
                background-color: #007bff;
                transition: width .6s ease;
            }
            .progress-bar {
                color: #000;
                background: linear-gradient(to right, #f77062  , #fe5196);
            }
            ::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	        ::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	        input:focus{background:#f5f5f5;outline: 1px solid #f5f5f5;}
	        .allbtn {
                width: 100%;
                height: 50px;
                line-height: 50px;
                text-align: center;
                background: #ccc;
                border-radius: 5px;
                background: linear-gradient(to left,#eeb80d,#ffe35b);
                margin-top: 20px;
            }
            .css-bhso1m {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: inline-block;
                border-radius: 4px;
                padding-left: 8px;
                padding-right: 8px;
                font-size: 14px;
                background-color: #3db485;
                color: rgb(240, 185, 11);
            }
            .css-bhso1ma:hover,.css-bhso1ma:link,.css-bhso1ma:visited,.css-bhso1ma:active{color:rgb(240, 185, 11);text-decoration:none;

            .css-bhso1m:hover {
                color: #0d202d !important;
            }
	    </style>
	</head>
	<body>
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            <div class="css-ogtd7z">
	                <div class="css-jrzkh7">
	                    <div id="header_global_js_wxgy34nj" class="css-1aac2e"></div>
	                 </div>
	            </div>
	            <main class="css-1wr4jig" style="background: #fff;">
                    <div class="css-1odg5z2">
                        <div class="css-1xrgo9z" style="margin-left: -55%;margin-top: 30px;">
                            <div>
                                <p style="font-size: 40px;">IEO Launchpad</p>
                                <p style="font-size: 18px;">{:L('新币特价抢先购')}</p>
                            </div>
                        </div>
                    </div>
                    <div class="css-uliqdc" style="height: 102px;background: rgb(245, 245, 245);">
                        <div style="height:102px;background: linear-gradient(180deg,rgba(57,194,139,.1),hsla(0,0%,100%,0)),#fff;width: 70%;margin: 0px auto;margin-top: -51px;z-index: 99;border-radius: 10px;">
                            <div style="margin: 22px;vertical-align:middle">
                                <p class="fch" style="font-size: 18px;margin: 0px 0px 10px 0px;">IEO Launchpad</p>
                                <p style="font-size: 16px;margin: 0px;color: #97a1a9">{:L('为用户提供低成本抢首发项目的投资机会')}</p>
                            </div>

                        </div>
                    </div>
                    <div class="css-uliqdc">
                        <div style="min-height: 500px;background: rgb(245, 245, 245);padding: 0px 15%;display: block;">
                            <div style="height: 50px;">

                                <p style="font-weight: 700;font-size: 18px;line-height: 24px;color: #151617;margin-bottom: 0;">{:L('抢购区')}</p>
                            </div>

                            <empty name="list">
	                        <div style="width:100%;height:400px;line-height:400px;text-align:center;">
	                            <svg style="height:100px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 96" fill="none" class="mirror css-1lzksdc"><path fill-rule="evenodd" clip-rule="evenodd" d="M64 8H26v80h58V28L64 8zM36 37h38v4H36v-4zm0 9h38v4H36v-4zm38 9H36v4h38v-4z" fill="url(#not-found-data-light_svg__paint0_linear)"></path><path d="M62 71l4-4 4 4-4 4-4-4z" fill="#fff"></path><path d="M86 50l3-3 3 3-3 3-3-3zM47 21l3-3 3 3-3 3-3-3zM84 28H64V8l20 20z" fill="#E6E8EA"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.171 73.171l14.5-14.5 5.657 5.658-14.5 14.5-5.657-5.657z" fill="url(#not-found-data-light_svg__paint1_linear)"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M51 48c0-8.837-7.163-16-16-16s-16 7.163-16 16 7.163 16 16 16 16-7.163 16-16zm4 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z" fill="url(#not-found-data-light_svg__paint2_linear)"></path><defs><linearGradient id="not-found-data-light_svg__paint0_linear" x1="84" y1="10.162" x2="84" y2="88" gradientUnits="userSpaceOnUse"><stop stop-color="#F5F5F5"></stop><stop offset="1" stop-color="#E6E8EA"></stop></linearGradient><linearGradient id="not-found-data-light_svg__paint1_linear" x1="4.171" y1="68.75" x2="24.328" y2="68.75" gradientUnits="userSpaceOnUse"><stop stop-color="#929AA5"></stop><stop offset="1" stop-color="#76808F"></stop></linearGradient><linearGradient id="not-found-data-light_svg__paint2_linear" x1="15" y1="48" x2="55" y2="48" gradientUnits="userSpaceOnUse"><stop stop-color="#929AA5"></stop><stop offset="1" stop-color="#76808F"></stop></linearGradient></defs></svg>
	                         </div>
	                         <else />
                            <foreach name="list" item="vo">
                            <div style="width:100%;height:320px;background:#fff;margin-bottom:20px;box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);border-radius:5px;">
                                <div style="width:100%;height:240px;">
                                    <div style="width:50%;height:240px;float:left;padding:5px;">
                                        <div style="width:100%;height:240px;background:#fff;">
                                            <div style="width:100%;height:230px;">
                                                <div style="width:25%;heigth:230px;line-height:150px;text-align:center;float:left;">
                                                    <img src="/Upload/public/{$vo.imgs}" style="width:60%;">
                                                </div>
                                                <div style="width:75%;padding:10px 0px 10px 10px;float:right;">
                                                    <div style="width:100%;min-height:30px;">
                                                        <span class="f16 fch">{$vo.name}</span>
                                                    </div>
                                                    <div style="width:100%;height:30px;">
                                                        <span class="f14 fcc">{:L('认购币种')}：<?php echo strtoupper($vo['coinname']);?></span>
                                                    </div>
                                                    
                                                    <div style="width:100%;height:30px;">
                                                        <span class="f14 fcc">{:L('开始时间')}：{$vo.starttime}</span>
                                                    </div>
                                                    <div style="width:100%;height:30px;">
                                                        <span class="f14 fcc">{:L('结束时间')}：<?php echo strtoupper($vo['finishtime']);?></span>
                                                    </div>
                                                    <div style="width:100%;height:30px;">
                                                        <span class="f14 fcc">{:L('发行总量')}：{$vo.num} <?php echo strtoupper($vo['coinname']);?></span>
                                                    </div>
                                                    
                                                    <div style="width:100%;height:30px;">
                                                        <span class="f14 fcc">{:L('发行单价')}：{$vo.yuan_price}</span>
                                                    </div>
                                                    <div style="width:100%;height:30px;">
                                                        <span class="f14 fcc">{:L('参与数量')}：{$vo.ysnum} <?php echo strtoupper($vo['coinname']);?></span>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                            
                                        </div>
                                    </div>

                                
                                    <div style="width:50%;height:240px;float:left;padding:5px;">
                                        <div style="width:100%;height:240px;">
                                            <div style="width:100%;height:240px;padding:10px 0px 10px 10px;">
                                                <div style="width:100%;height:30px;">
                                                    <span class="f16 fch">{:L('项目说明')}</span>
                                                </div>
                                                <div style="width:100%;height:30px;">
                                                    <span class="f14 fcc">{:L('认购上限')} ：{$vo.allmax}</span>
                                                </div>
                                                 <div style="width:100%;height:30px;">
                                                    <span class="f14 fcc">{:L('认购单价')} ：{$vo.price}</span>
                                                </div> 
                                                <div style="width:100%;height:30px;">
                                                    <span class="f14 fcc">{:L('单次最低')} ：{$vo.ysnum}</span>
                                                </div>
                                                <div style="width:100%;height:30px;">
                                                    <span class="f14 fcc">{:L('认购上限')} ：{$vo.min}</span>
                                                </div>
                                                <div style="width:100%;min-height:30px;overflow: hidden; text-overflow: ellipsis;white-space: nowrap;">
                                                    <span class="f14 fcc">{$vo.content}</span>
                                                </div>
                                            
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="width:100%;height:50px;margin-top:40px;padding:0px 20px;">
                                    <div style="width:80%;height:40px;float:left;">
                                        <div class="progress">
					  			    		<?php if(strtotime($vo['starttime']) <= time()){?>
					                        <div class="progress-bar" role="progressbar" style="width:<?php echo ($vo['ysnum'] + $vo['sellnum']) / $vo['num'] * 100;?>%;" aria-valuenow="<?php echo ($vo['ysnum'] + $vo['sellnum']) / $vo['num'] * 100;?>" aria-valuemin="0" aria-valuemax="100"><?php echo ($vo['ysnum'] + $vo['sellnum']) / $vo['num'] * 100;?>%</div>
					                        <?php }elseif(strtotime($vo['starttime']) > time()){?>
					                        <div class="progress-bar" role="progressbar" style="width:0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
					                           <?php }?>
					                    </div>
                                    </div>
                                    <div style="width:20%;height:40px;float:left;">
                                        <div style="width:100%;height:40px;text-align:center;">
                                            <a href="{:U('Issue/details')}?id={$vo.id}" class="css-bhso1m" style="padding:5px 15px;color: #fff">{:L('立即参与')}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                   
                        </foreach>
                        </empty>
  
                        
                    </div>
	            </main>

	            <include file="Public:footer"/>
	        </div>   
	    </div>
	    
	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>

    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>

    
</html>