<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/huobi-buttons.css" />
    <title>{$Think.config.WEB_SITE_TITLE}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
    <style>
    /* 火币风格导航栏 */
    .huobi-header {
        background: #0B1426;
        border-bottom: 1px solid #1e2329;
        position: sticky;
        top: 0;
        z-index: 1000;
        padding: 0;
    }
    .huobi-nav-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 64px;
    }
    .huobi-logo {
        display: flex;
        align-items: center;
        color: #F0B90B !important;
        font-size: 24px;
        font-weight: bold;
        text-decoration: none;
    }
    .huobi-nav-menu {
        display: flex;
        align-items: center;
        gap: 32px;
        list-style: none;
        margin: 0;
        padding: 0;
    }
    .huobi-nav-item {
        position: relative;
    }
    .huobi-nav-link {
        color: #EAECEF;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        padding: 20px 0;
        display: block;
        transition: color 0.2s ease;
        border-bottom: 2px solid transparent;
    }
    .huobi-nav-link:hover {
        color: #F0B90B !important;
        border-bottom-color: #F0B90B !important;
    }
    .huobi-nav-link.active {
        color: #F0B90B !important;
        border-bottom-color: #F0B90B !important;
    }
    .huobi-user-area {
        display: flex;
        align-items: center;
        gap: 16px;
    }
    .huobi-btn {
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }
    .huobi-btn-outline, .huobi-user-area .huobi-btn-outline {
        color: #F0B90B !important;
        border-color: #F0B90B !important;
        background: transparent;
    }
    .huobi-btn-outline:hover {
        background: #F0B90B;
        color: #0B1426;
    }
    .huobi-btn-primary {
        background: #F0B90B;
        color: #0B1426;
    }
    .huobi-btn-primary:hover {
        background: #E6A800;
    }
    
    /* 移动端适配 */
    @media (max-width: 768px) {
        .huobi-nav-container {
            padding: 0 16px;
            height: 56px;
        }
        .huobi-nav-menu {
            gap: 20px;
        }
        .huobi-nav-link {
            font-size: 13px;
            padding: 16px 0;
        }
        .huobi-user-area {
            gap: 12px;
        }
        .huobi-btn {
            padding: 6px 12px;
            font-size: 13px;
        }
    }
    </style>
</head>
<body>
    <header class="huobi-header">
        <div class="huobi-nav-container">
            <a href="{:U('Index/index')}" class="huobi-logo">
                <img src="/Upload/public/{:get_config('waplogo')}" alt="Logo" style="height: 32px; margin-right: 8px;" onerror="this.style.display='none'">
                {:get_config('webname')}
            </a>
            
            <nav>
                <ul class="huobi-nav-menu">
                    <li class="huobi-nav-item">
                        <a href="{:U('Index/index')}" class="huobi-nav-link" data-nav="index">首页</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Contract/index')}" class="huobi-nav-link" data-nav="contract">秒合约</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Trade/index')}" class="huobi-nav-link" data-nav="trade">币币交易</a>
                    </li>
                    <li class="huobi-nav-item">
                        <if condition="$Think.session.uid"><a href="{:U('Finance/index')}" class="huobi-nav-link" data-nav="finance">钱包</a><else/><a href="{:U('Login/index')}" class="huobi-nav-link" data-nav="finance">钱包</a></if>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Issue/index')}" class="huobi-nav-link" data-nav="issue">IEO</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Orepool/index')}" class="huobi-nav-link" data-nav="defi">DeFi</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Index/gglist')}" class="huobi-nav-link" data-nav="notice">公告中心</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Login/lhelp')}" class="huobi-nav-link" data-nav="help">帮助中心</a>
                    </li>
                </ul>
            </nav>
            
            <div class="huobi-user-area">
                    <a href="{:U("Login/index")}" class="huobi-btn huobi-btn-outline">登录</a>
                    <a href="{:U("Login/register")}" class="huobi-btn huobi-btn-primary">注册</a>
            </div>
        </div>
    </header>

    <script>
    // 导航高亮逻辑
    (function() {
        var path = window.location.pathname.toLowerCase();
        var navMap = {
            'index': ['/index/index', '/mobile/index/index', '/'],
            'contract': ['/contract/index'],
            'trade': ['/trade/index', '/trade/ordinary', '/trade/trans'],
            'finance': ['/finance/index', '/user/czcoin', '/user/txcoin'],
            'issue': ['/issue/index', '/issue/details'],
            'defi': ['/orepool/index'],
            'notice': ['/index/gglist', '/index/gginfo'],
            'help': ['/login/lhelp']
        };
        
        var activeNav = 'index';
        Object.keys(navMap).forEach(function(key) {
            if (navMap[key].some(function(p) { return path.indexOf(p) !== -1; })) {
                activeNav = key;
            }
        });
        
        var activeLink = document.querySelector('[data-nav="' + activeNav + '"]');
        if (activeLink) {
            activeLink.classList.add('active');
        }
    })();
    
    // 图片404修复
    (function(){
        function fixExt(u){
            if(!u) return u;
            return u.replace(/\.png\.png$/i,'.png').replace(/\.jpg\.jpg$/i,'.jpg');
        }
        document.addEventListener('error', function(e){
            var el=e.target; if(!el||el.tagName!=='IMG')return;
            if(el.dataset && el.dataset.imgFixed==='1'){ 
                el.src='/Public/Home/static/imgs/empty-dark.png'; return; 
            }
            if(el.dataset) el.dataset.imgFixed='1';
            var s=el.getAttribute('src')||''; var f=fixExt(s);
            if(f!==s){ el.src=f; return; }
            if(!/\.(png|jpg|jpeg|gif|svg)([?#]|$)/i.test(s)){ el.src=s+'.png'; return; }
            el.src='/Public/Home/static/imgs/empty-dark.png';
        }, true);
    })();
    </script>
