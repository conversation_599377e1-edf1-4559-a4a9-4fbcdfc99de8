<link rel="stylesheet" type="text/css" href="/Public/Home/static/css/nav-force-fix.css" />
<link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
<script>window.gologin=window.gologin||function(){ window.location.href="{:U('Login/index')}"; };</script>
<script>
// 全站图片404自修复：处理 .png.png 等双后缀、缺少扩展名，最后兜底占位图
(function(){
  function fixExt(u){
    if(!u) return u;
    if(/\.png\.png$/i.test(u)) return u.replace(/\.png\.png$/i, '.png');
    if(/\.jpg\.jpg$/i.test(u)) return u.replace(/\.jpg\.jpg$/i, '.jpg');
    if(/\.jpeg\.jpeg$/i.test(u)) return u.replace(/\.jpeg\.jpeg$/i, '.jpeg');
    if(/\.gif\.gif$/i.test(u)) return u.replace(/\.gif\.gif$/i, '.gif');
    return u;
  }
  document.addEventListener('error', function(e){
    var el = e.target;
    if(!el || el.tagName !== 'IMG') return;
    if(el.dataset && el.dataset.imgFixed==='1') {
      el.src = '/Public/Home/static/imgs/empty-dark.png';
      return;
    }
    if(el.dataset) el.dataset.imgFixed='1';
    var src = el.getAttribute('src') || '';
    var fixed = fixExt(src);
    if(fixed !== src) { el.src = fixed; return; }
    if(!/\.(png|jpg|jpeg|gif|svg)([?#]|$)/i.test(src)) { el.src = src + '.png'; return; }
    el.src = '/Public/Home/static/imgs/empty-dark.png';
  }, true);
})();
</script>

<style>
/* Modern Header Styles */
.modern-header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0;
}
.navbar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    font-size: 1.5rem;
    color: #1e2329 !important;
    text-decoration: none;
}
.navbar-brand img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}
.navbar-nav .nav-link {
    color: #1e2329 !important;
    font-weight: 500;
    padding: 0.75rem 0.6rem !important;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
    font-size: 0.9rem;
}
.navbar-nav .nav-link:hover {
    color: #f0b90b !important;
}
.navbar-nav .nav-link.active {
    color: #f0b90b !important;
}
.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: #f0b90b;
}
.btn-header {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.9rem;
}
.btn-login {
    background: transparent;
    color: #1e2329;
    border: 1px solid #eaecef;
}
.btn-login:hover {
    background: #f8f9fa;
    color: #1e2329;
}
.btn-register {
    background: #f0b90b;
    color: #fff;
}
.btn-register:hover {
    background: #d4a309;
    color: #fff;
    transform: translateY(-1px);
}
.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
}
.dropdown-item {
    padding: 0.75rem 1.5rem;
    color: #1e2329;
    transition: all 0.3s ease;
}
.dropdown-item:hover {
    background: #f8f9fa;
    color: #f0b90b;
}
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f0b90b;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 600;
}
@media (max-width: 991px) {
    .navbar-collapse {
        background: #fff;
        padding: 1rem;
        border-radius: 8px;
        margin-top: 1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    .navbar-nav .nav-link {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.85rem;
    }
}
</style>
<header class="modern-header">
    <nav class="navbar navbar-expand-lg nav-aug-fixed">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand" href="{:U('Index/index')}">
                <img src="/Upload/public/{:get_config('waplogo')}" alt="Logo" />
                <span>{:get_config('webname')}</span>
            </a>
            <!-- Navigation (always visible, no collapse) -->
            <div class="navbar-collapse show" id="navbarNav">
                <ul class="navbar-nav me-auto main-nav-aug">
                    <li class="nav-item">
                        <a class="nav-link active" href="{:U('Index/index')}">{:L('首页')}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Contract/index')}">{:L('秒合约')}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Trade/index')}">{:L('币币交易')}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Finance/index')}">{:L('充币')}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Issue/index')}">IEO</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Orepool/index')}">DeFi</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Index/gglist')}">{:L('公告中心')}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Login/lhelp')}">{:L('帮助中心')}</a>
                    </li>
                </ul>
                <!-- User Actions -->
                <div class="d-flex align-items-center gap-3">
                    <!-- Language Selector -->
                    <div class="dropdown">
                        <button class="btn btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <?php if(LANG_SET=='zh-cn'){?>中文<?php }elseif(LANG_SET=='en-us'){?>EN<?php }else{?>中文<?php }?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/?Lang=zh-cn">简体中文</a></li>
                            <li><a class="dropdown-item" href="/?Lang=en-us">English</a></li>
                        </ul>
                    </div>
                    <!-- User Authentication -->
                    <if condition="$uid neq 0">
                        <!-- Logged in user -->
                        <div class="dropdown">
                            <button class="btn dropdown-toggle d-flex align-items-center gap-2" type="button" data-bs-toggle="dropdown">
                                <div class="user-avatar">
                                    <i class="bi bi-person"></i>
                                </div>
                                <span class="d-none d-md-inline">{$username}</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{:U('User/index')}">
                                    <i class="bi bi-person-circle me-2"></i>{:L('账户总览')}
                                </a></li>
                                <li><a class="dropdown-item" href="{:U('Finance/index')}">
                                    <i class="bi bi-wallet2 me-2"></i>{:L('钱包')}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{:U('Trade/bborder')}">
                                    <i class="bi bi-list-ul me-2"></i>{:L('币币交易订单')}
                                </a></li>
                                <li><a class="dropdown-item" href="{:U('Contract/contractjc')}">
                                    <i class="bi bi-graph-up me-2"></i>{:L('合约交易订单')}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{:U('User/respwd')}">
                                    <i class="bi bi-gear me-2"></i>{:L('安全设置')}
                                </a></li>
                                <li><a class="dropdown-item" href="{:U('Login/loginout')}">
                                    <i class="bi bi-box-arrow-right me-2"></i>{:L('退出账号')}
                                </a></li>
                            </ul>
                        </div>
                    <else />
                        <!-- Not logged in -->
                        <div class="d-flex gap-2">
                            <a href="{:U('Login/index')}" class="btn btn-header btn-login">{:L('登陆')}</a>
                            <a href="{:U('Login/register')}" class="btn btn-header btn-register">{:L('注册')}</a>
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </nav>
</header>
