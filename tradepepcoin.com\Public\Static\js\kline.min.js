!
    function(t, e) {
        "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (t = t || self).VyKline = e()
    } (this, (function() {
        "use strict";
        var t = function(t, e) {
            if (! (t instanceof e)) throw new TypeError("Cannot call a class as a function")
        };
        function e(t, e) {
            for (var i = 0; i < e.length; i++) {
                var a = e[i];
                a.enumerable = a.enumerable || !1,
                    a.configurable = !0,
                "value" in a && (a.writable = !0),
                    Object.defineProperty(t, a.key, a)
            }
        }
        var i = function(t, i, a) {
            return i && e(t.prototype, i),
            a && e(t, a),
                t
        };
        function a(t, e) {
            return t(e = {
                    exports: {}
                },
                e.exports),
                e.exports
        }
        var n = a((function(t) {
            function e(t) {
                return (e = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ?
                    function(t) {
                        return typeof t
                    }: function(t) {
                        return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol": typeof t
                    })(t)
            }
            function i(a) {
                return "function" == typeof Symbol && "symbol" === e(Symbol.iterator) ? t.exports = i = function(t) {
                    return e(t)
                }: t.exports = i = function(t) {
                    return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol": e(t)
                },
                    i(a)
            }
            t.exports = i
        }));
        var s = function(t) {
            if (void 0 === t) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            return t
        };
        var o = function(t, e) {
                return ! e || "object" !== n(e) && "function" != typeof e ? s(t) : e
            },
            r = a((function(t) {
                function e(i) {
                    return t.exports = e = Object.setPrototypeOf ? Object.getPrototypeOf: function(t) {
                        return t.__proto__ || Object.getPrototypeOf(t)
                    },
                        e(i)
                }
                t.exports = e
            })),
            l = a((function(t) {
                function e(i, a) {
                    return t.exports = e = Object.setPrototypeOf ||
                        function(t, e) {
                            return t.__proto__ = e,
                                t
                        },
                        e(i, a)
                }
                t.exports = e
            }));
        var c = function(t, e) {
                if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function");
                t.prototype = Object.create(e && e.prototype, {
                    constructor: {
                        value: t,
                        writable: !0,
                        configurable: !0
                    }
                }),
                e && l(t, e)
            },
            h = {
                $createElement: function(t) {
                    return document.createElement(t)
                },
                $setElementAttribute: function(t, e, i) {
                    if ("object" === n(i)) for (var a in i) t[e][a] = i[a];
                    else t.setAttribute(e, i)
                },
                $removeElementAttribute: function(t, e, i) {
                    t.removeAttribute(e, i)
                },
                $getElementAttribute: function(t, e) {
                    return t.getAttribute(e)
                }
            };
        function u(t, e) {
            var i = (t = t < 10 ? "0".concat(t) : t).length,
                a = e.length >= t.length ? i: e.length;
            return t.slice(i - a, i)
        }
        var d = {
                Y: function(t, e) {
                    return u(String(t.getFullYear()), e)
                },
                M: function(t, e) {
                    return u(String(t.getMonth() + 1), e)
                },
                D: function(t, e) {
                    return u(String(t.getDate()), e)
                },
                H: function(t, e) {
                    return u(String(t.getHours()), e)
                },
                m: function(t, e) {
                    return u(String(t.getMinutes()), e)
                },
                s: function(t, e) {
                    return u(String(t.getSeconds()), e)
                }
            },
            _ = new(function() {
                function e() {
                    t(this, e)
                }
                return i(e, [{
                    key: "format",
                    value: function(t, e) {
                        var i = t;
                        if (! (t instanceof Date) && (i = new Date(t), "Invalid Date" === String(i))) throw new Error("Invalid Date");
                        return e.replace(/Y{1,}|M{1,}|D{1,}|H{1,}|m{1,}|s{1,}/g, (function(t) {
                            return d[t[0]](i, t)
                        }))
                    }
                }]),
                    e
            } ()),
            m = {
                "+": function(t, e) {
                    return t + e
                },
                "-": function(t, e) {
                    return t - e
                },
                "*": function(t, e) {
                    return t * e
                },
                "/": function(t, e) {
                    return t / e
                }
            };
        function v(t) {
            for (var e, i = arguments.length,
                     a = 1; a < i; a++) e = 1 === a ? arguments[a] : m[t](e, arguments[a]);
            return e
        }
        var f = {},
            x = function() {
                function e() {
                    t(this, e)
                }
                return i(e, [{
                    key: "emit",
                    value: function(t) {
                        var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null;
                        f[t] && f[t].forEach((function(t) {
                            t(e)
                        }))
                    }
                },
                    {
                        key: "on",
                        value: function(t, e) {
                            f[t] || (f[t] = []),
                                f[t].push(e)
                        }
                    }]),
                    e
            } (),
            $ = function() {
                function e(i) {
                    var a = this,
                        n = i.state;
                    t(this, e),
                        this.state = {};
                    var s = this,
                        o = function(t) {
                            Object.defineProperty(a.state, t, {
                                configurable: !1,
                                enumerable: !0,
                                get: function() {
                                    return n[t]
                                },
                                set: function(e) {
                                    void 0 !== s[t] && (n[t] = e)
                                }
                            })
                        };
                    for (var r in n) o(r)
                }
                return i(e, [{
                    key: "commit",
                    value: function(t, e) {
                        this[t] = t,
                            this.state[t] = e
                    }
                }]),
                    e
            } (),
            p = {
                DEFAULT_COLUMN_WIDTH: 5,
                DEFAULT_COLUMN_SPACE: 1,
                VIEW_CHANGE_TRIGGER_WAY: {
                    SCALE: "SCALE"
                },
                CHART_TYPE: {
                    KLINE: "KLINE",
                    REAL_TIME: "REAL_TIME",
                    EMPTY_KLINE: "EMPTY_KLINE"
                },
                WATCHER_EVENT: {
                    DRAG_MOUSE_MOVING: "dragMouseMoving",
                    MOUSE_SCALING: "mouseScaling",
                    ONE_DATA_UPDATED: "onOneDataUpdated",
                    MOUSE_MOVING: "mouseMoving",
                    REAL_TIME_DATA: "onRealTimeData",
                    HISTORY_DATA_CHANGE: "historyDataChange",
                    SWITCHED_INTERVAL: "switchedInterval",
                    THEME_SWITCHED: "themeSwitched"
                }
            },
            g = window.devicePixelRatio || 1,
            y = {
                scale_step: .2,
                max_scale: 6,
                min_scale: 1,
                x_axis_height: 54,
                y_axis_width: 54,
                axis_segment: 3,
                default_rise_color: "#53b987",
                default_fall_color: "#eb4d5c",
                column_style: "solid",
                init_offset_x: 100,
                volume_height: 100,
                interval_tool_bar: "30px",
                theme: {
                    background_color: null,
                    axis_text_color: null,
                    axis_color: null,
                    background_line_color: null,
                    realtime_line_color: null,
                    realtime_area_color: null
                },
                precision: 4,
                chart_type: p.CHART_TYPE.EMPTY_KLINE
            },
            T = {
                user_config: null,
                el: null,
                loadingNode: null,
                chart_instance: null,
                vy_chart: null,
                DEFAULT_COLUMN_WIDTH: p.DEFAULT_COLUMN_WIDTH * g,
                DEFAULT_COLUMN_SPACE: p.DEFAULT_COLUMN_SPACE * g,
                column_width: p.DEFAULT_COLUMN_WIDTH * g,
                column_space: p.DEFAULT_COLUMN_SPACE * g,
                current_interval: null,
                kline_data: [],
                all_kline_data: [],
                current_data: null,
                min: null,
                max: null,
                kline_canvas: null,
                current_mouse_coordinates: {
                    x: null,
                    y: null
                },
                current_cross_dash_line: {
                    x: null,
                    y: null
                },
                cross_is_in_newest_data: null,
                deedfeeds: null,
                y_axis_scale_list: [],
                YTag: null,
                realtime_tagY: null,
                axis_font: "".concat(12 * g, "px Arial"),
                current_scale: 1,
                no_scale_offset_x: y.init_offset_x,
                limit_offset_x: .25,
                every_data_px: null,
                overflow_data_num: 0,
                ma_Lines: [{
                    range: 5,
                    lineColor: "#583d7a"
                },
                    {
                        range: 10,
                        lineColor: "#95a4c7"
                    }],
                chart_type: p.CHART_TYPE.KLINE
            },
            E = {
                dark: {
                    background_color: "#131722",
                    axis_text_color: "#ccc",
                    axis_color: "#ccc",
                    background_line_color: "#363c4e",
                    realtime_line_color: "#9397a4",
                    realtime_area_color: "rgba(70, 80, 120, 0.6)"
                },
                light: {
                    background_color: "#fff",
                    axis_text_color: "#50535e",
                    axis_color: "#50535e",
                    background_line_color: "#e1ecf2",
                    realtime_line_color: "#2196f3",
                    realtime_area_color: "rgba(30, 150, 240, 0.2)"
                }
            };
        function w(t) {
            return JSON.parse(JSON.stringify(t))
        }
        var b = new
            function e() {
                t(this, e),
                    this.CONSTANTS = p,
                    this.THEME = E,
                    this.$Od = h,
                    this.$time = _,
                    this.$decimal = v,
                    this.$copyObjectUnsafe = w,
                    this.$watcher = new x,
                    this.$store = new $({
                        state: Object.assign(T, y)
                    }),
                    this.$handleCanvasDrawValue = function(t) {
                        return Math.round(t) + .5
                    },
                    this.$devicePixelRatio = window.devicePixelRatio || 1
            },
            A = function() {
                function e() {
                    for (var i in t(this, e), b) this[i] = b[i]
                }
                return i(e, [{
                    key: "_customChart",
                    value: function(t) {
                        var e = Object.keys(y),
                            i = this.$store.state.user_config.theme,
                            a = t;
                        for (var n in a.chartType || (a.chartType = y.chart_type), E[i] || (i = a.theme || "light"), a = Object.assign({},
                            {
                                theme: E[i]
                            },
                            a)) e.includes(n) && this.$store.commit(n, a[n]);
                        a.init_offset_x && this.$store.commit("no_scale_offset_x", a.init_offset_x),
                            this.$store.commit("chart_type", a.chartType)
                    }
                },
                    {
                        key: "_clearScreen",
                        value: function(t, e, i) {
                            t.clearRect(0, 0, e, i)
                        }
                    },
                    {
                        key: "_drawBackground",
                        value: function(t, e, i) {
                            t.beginPath(),
                                t.globalAlpha = 1,
                                t.fillStyle = this.$store.state.theme.background_color,
                                t.fillRect(0, 0, e, i)
                        }
                    },
                    {
                        key: "_handleDevicePixelRatio",
                        value: function(t) {
                            var e = t.symbol,
                                i = void 0 === e ? "*": e,
                                a = t.value;
                            return this.$decimal(i, a, this.$devicePixelRatio)
                        }
                    }]),
                    e
            } (),
            D = new(function(e) {
                function a() {
                    return t(this, a),
                        o(this, r(a).apply(this, arguments))
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function(t) {
                            var e = this,
                                i = t.ctx;
                            this._ctx = i,
                                this.$store.state.kline_data.forEach((function(t) {
                                    var i = t.x,
                                        a = t.columnStartY,
                                        n = t.columnHeight,
                                        s = t.close,
                                        o = t.open,
                                        r = t.candleLineStartY,
                                        l = t.candleLineHeight,
                                        c = e.$store.state,
                                        h = c.column_width,
                                        u = c.default_rise_color,
                                        d = c.default_fall_color;
                                    e._ctx.strokeStyle = e._ctx.fillStyle = s >= o ? u: d,
                                        e._ctx.lineWidth = 1;
                                    var _ = e.$handleCanvasDrawValue(e.$decimal("+", i, Math.floor(h / 2)));
                                    e._ctx.beginPath(),
                                        e._ctx.moveTo(_, r),
                                        e._ctx.lineTo(_, r + l),
                                        e._ctx.lineWidth = 1,
                                        e._ctx.stroke(),
                                        e._ctx.fillRect(Math.round(i), a, h, n)
                                }))
                        }
                    }]),
                    a
            } (A)),
            C = new(function(e) {
                function a() {
                    return t(this, a),
                        o(this, r(a).apply(this, arguments))
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function(t) {
                            var e = this,
                                i = t.ctx,
                                a = t.c;
                            this._ctx = i,
                                this.c = a,
                                this._ctx.beginPath();
                            var n = this.$store.state,
                                s = n.kline_data,
                                o = n.column_width,
                                r = n.theme,
                                l = r.realtime_line_color,
                                c = r.realtime_area_color;
                            s.forEach((function(t, i) {
                                var a = t.x,
                                    n = t.columnStartY,
                                    s = t.columnHeight,
                                    r = t.isRise,
                                    l = e.$handleCanvasDrawValue(e.$decimal("+", a, Math.floor(o / 2))),
                                    c = r ? n: e.$decimal("+", n, s);
                                i ? e._ctx.lineTo(l, c) : e._ctx.moveTo(l, c)
                            }));
                            var h = s[0],
                                u = s[s.length - 1],
                                d = this.$handleCanvasDrawValue(this.$decimal("+", u.x, Math.floor(o / 2))),
                                _ = this.$handleCanvasDrawValue(this.$decimal("+", h.x, Math.floor(o / 2)));
                            this._ctx.lineTo(d, this.c.height + 1),
                                this._ctx.lineTo(_, this.c.height + 1),
                                this._ctx.lineWidth = 1,
                                this._ctx.strokeStyle = l,
                                this._ctx.fillStyle = c,
                                this._ctx.stroke(),
                                this._ctx.fill()
                        }
                    }]),
                    a
            } (A)),
            k = new(function(e) {
                function a() {
                    return t(this, a),
                        o(this, r(a).apply(this, arguments))
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function(t) {
                            var e = this,
                                i = t.ctx;
                            this._ctx = i,
                                this.$store.state.kline_data.forEach((function(t, i, a) {
                                    var n = t.x,
                                        s = t.columnStartY,
                                        o = t.columnHeight,
                                        r = t.close,
                                        l = (t.open, t.candleLineStartY),
                                        c = t.candleLineHeight,
                                        h = e.$store.state,
                                        u = h.column_width,
                                        d = h.default_rise_color,
                                        _ = h.default_fall_color,
                                        m = !a[i + 1] || r - a[i + 1].close >= 0;
                                    e._ctx.strokeStyle = e._ctx.fillStyle = m ? d: _,
                                        e._ctx.lineWidth = 1;
                                    var v = e.$devicePixelRatio % 2 == 0 ? .5 : 0,
                                        f = Math.round(n) + .5,
                                        x = Math.round(n) + v,
                                        $ = e.$handleCanvasDrawValue(e.$decimal("+", x, Math.floor(u / 2)));
                                    e._ctx.beginPath(),
                                        e._ctx.moveTo($, l),
                                        e._ctx.lineTo($, s),
                                        e._ctx.moveTo($, e.$decimal("+", s, o)),
                                        e._ctx.lineTo($, e.$decimal("+", l, c)),
                                        e._ctx.lineWidth = 1,
                                        e._ctx.stroke(),
                                        t.isRise ? e._ctx.strokeRect(f, s, u, o) : e._ctx.fillRect(x, s, u, o)
                                }))
                        }
                    }]),
                    a
            } (A));
        var M = function(t, e, i) {
            return e in t ? Object.defineProperty(t, e, {
                value: i,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : t[e] = i,
                t
        };
        function S(t, e) {
            var i = Object.keys(t);
            if (Object.getOwnPropertySymbols) {
                var a = Object.getOwnPropertySymbols(t);
                e && (a = a.filter((function(e) {
                    return Object.getOwnPropertyDescriptor(t, e).enumerable
                }))),
                    i.push.apply(i, a)
            }
            return i
        }
        var O = function(e) {
                function a(e) {
                    var i, n = e._ctx,
                        s = e.range;
                    return t(this, a),
                        (i = o(this, r(a).call(this)))._ctx = n,
                        i.range = s,
                        i
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function() {
                            var t = this._handleMALineData();
                            this._drawMALine(t)
                        }
                    },
                        {
                            key: "_handleMALineData",
                            value: function() {
                                var t = this,
                                    e = [],
                                    i = [];
                                return this.$store.state.all_kline_data.slice(this.$store.state.overflow_data_num, 2 * this.$store.state.kline_data.length + this.$store.state.overflow_data_num).forEach((function(a, n, s) {
                                    if (! (s.length - t.range - n < 0)) {
                                        i = s.slice(n, n + t.range);
                                        var o = function(t) {
                                                for (var e = 1; e < arguments.length; e++) {
                                                    var i = null != arguments[e] ? arguments[e] : {};
                                                    e % 2 ? S(i, !0).forEach((function(e) {
                                                        M(t, e, i[e])
                                                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(i)) : S(i).forEach((function(e) {
                                                        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(i, e))
                                                    }))
                                                }
                                                return t
                                            } ({},
                                            a, {
                                                avgClose: 0
                                            }),
                                            r = 0;
                                        i.forEach((function(e) {
                                            r = t.$decimal("+", r, e.close)
                                        })),
                                            o.avgClose = t.$decimal("/", r, t.range),
                                            e.push(o),
                                        t.$store.state.kline_data[n] && (t.$store.state.kline_data[n]["MAClose".concat(t.range)] = o.avgClose),
                                            i = []
                                    }
                                })),
                                    (e = e.slice(0, this.$store.state.kline_data.length)).forEach((function(e, i) {
                                        Object.assign(e, t.$store.state.kline_data[i])
                                    })),
                                    e
                            }
                        },
                        {
                            key: "_drawMALine",
                            value: function(t) {
                                var e = this;
                                this._ctx.beginPath();
                                var i = this.$store.state,
                                    a = i.el,
                                    n = i.x_axis_height,
                                    s = i.volume_height,
                                    o = i.min,
                                    r = i.every_data_px,
                                    l = i.column_width,
                                    c = a.offsetHeight,
                                    h = this.$decimal("-", c, n, s) * this.$devicePixelRatio;
                                t.forEach((function(t, i, a) {
                                    var n = e.$decimal("+", t.x, l / 2),
                                        s = e.$decimal("-", h, (t.avgClose - o) * r);
                                    i ? e._ctx.lineTo(n, s) : (e._ctx.beginPath(), e._ctx.moveTo(n, s))
                                })),
                                    this._ctx.stroke()
                            }
                        }]),
                    a
            } (A),
            R = function(e) {
                function a() {
                    var e;
                    return t(this, a),
                        (e = o(this, r(a).call(this))).c = e.$Od.$createElement("canvas"),
                        e.c.width = e._handleDevicePixelRatio({
                            value: e.$decimal("-", e.$store.state.el.offsetWidth, e.$store.state.y_axis_width)
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: e.$decimal("-", e.$store.state.el.offsetHeight, e.$store.state.x_axis_height, e.$store.state.volume_height)
                        }),
                        e.c.style.position = "absolute",
                        e.c.style.top = 0,
                        e.c.style.left = 0,

                        e.c.style.padding = "25px 0px 0px 0px ",
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height-22
                        }), "px"),
                        // console.log(e.c.height,'111111'),
                        e._ctx = e.c.getContext("2d"),
                        e.$watcher.on(p.WATCHER_EVENT.DRAG_MOUSE_MOVING, e.dragMouseMoving.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.MOUSE_SCALING, e.mouseScaling.bind(s(e))),
                        e.mALineInstanceList = [],
                        e.$store.state.ma_Lines.forEach((function(t) {
                            var i = new O({
                                _ctx: e._ctx,
                                range: t.range
                            });
                            e.mALineInstanceList.push(i)
                        })),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "drawHistory",
                        value: function() {
                            var t = this,
                                e = this.$store.state,
                                i = e.y_axis_scale_list,
                                a = e.chart_type,
                                n = e.theme.background_line_color;
                            switch (this._clearScreen(this._ctx, this.c.width, this.c.height), this._drawBackground(this._ctx, this.c.width, this.c.height), this._ctx.setLineDash([0]), i.forEach((function(e) {
                                // t._ctx.beginPath(),
                                //     t._ctx.strokeStyle = n,
                                //     t._ctx.lineWidth = 1;
                                // var i = Math.round(e) - .5;
                                // t._ctx.moveTo(0, i),
                                //     console.log('后面得线'),
                                //     t._ctx.lineTo(t.c.width, i),
                                //     t._ctx.stroke()
                            })), this._ctx.strokeStyle = this.$store.state.ma_Lines[0].lineColor, this.mALineInstanceList[0].draw(), this._ctx.strokeStyle = this.$store.state.ma_Lines[1].lineColor, this.mALineInstanceList[1].draw(), a) {
                                case this.CONSTANTS.CHART_TYPE.KLINE:
                                    D.draw({
                                        ctx:
                                        this._ctx
                                    });
                                    break;
                                case this.CONSTANTS.CHART_TYPE.REAL_TIME:
                                    C.draw({
                                        ctx:
                                        this._ctx,
                                        c: this.c
                                    });
                                    break;
                                case this.CONSTANTS.CHART_TYPE.EMPTY_KLINE:
                                    k.draw({
                                        ctx:
                                        this._ctx
                                    });
                                    break;
                                default:
                                    D.draw({
                                        ctx:
                                        this._ctx
                                    })
                            }
                            this.drawRealTimeLine()
                        }
                    },
                        {
                            key: "drawRealTimeLine",
                            value: function() {
                                var t = this.$store.state,
                                    e = t.max,
                                    i = t.min,
                                    a = t.all_kline_data,
                                    n = t.default_rise_color,
                                    s = t.default_fall_color,
                                    o = t.realtime_tagY,
                                    r = t.precision,
                                    l = a[0],
                                    c = l.close,
                                    h = l.open,
                                    u = (l.low, l.high, this.$decimal("-", e, i)),
                                    d = this.$decimal("/", this.c.height, u),
                                    _ = this.$decimal("*", d, Math.abs(this.$decimal("-", h, c))),
                                    m = Math.min(h, c),
                                    v = this.$decimal("-", this.c.height, (m - i) * d, _),
                                    f = c >= h,
                                    x = f ? n: s,
                                    $ = f ? v: this.$decimal("+", v, _);
                                this._ctx.strokeStyle = x,
                                    this._ctx.beginPath(),
                                    this._ctx.lineWidth = 1,
                                    this._ctx.setLineDash([2]),
                                    this._ctx.moveTo(0, $),
                                    this._ctx.lineTo(this.c.width, $),
                                    this._ctx.stroke(),
                                    o.innerText = c.toFixed(r),
                                    this.$Od.$setElementAttribute(o, "style", {
                                        top: "".concat(this._handleDevicePixelRatio({
                                            symbol: "/",
                                            value: $
                                        }) - 8, "px"),
                                        backgroundColor: x
                                    })
                            }
                        },
                        {
                            key: "dragMouseMoving",
                            value: function() {
                                this.drawHistory()
                            }
                        },
                        {
                            key: "mouseScaling",
                            value: function() {
                                this.drawHistory()
                            }
                        }]),
                    a
            } (A);
        var H = function(t) {
            if (Array.isArray(t)) {
                for (var e = 0,
                         i = new Array(t.length); e < t.length; e++) i[e] = t[e];
                return i
            }
        };
        var P = function(t) {
            if (Symbol.iterator in Object(t) || "[object Arguments]" === Object.prototype.toString.call(t)) return Array.from(t)
        };
        var N = function() {
            throw new TypeError("Invalid attempt to spread non-iterable instance")
        };
        var L = function(t) {
                return H(t) || P(t) || N()
            },
            I = function(e) {
                function a() {
                    return t(this, a),
                        o(this, r(a).apply(this, arguments))
                }
                return c(a, e),
                    i(a, [{
                        key: "dealCanDrawViewData",
                        value: function(t) {
                            var e, i = this,
                                a = this.$store.state,
                                n = a.el,
                                s = a.x_axis_height,
                                o = a.y_axis_width,
                                r = a.column_space,
                                l = a.column_width,
                                c = a.init_offset_x,
                                h = a.volume_height,
                                u = n.offsetWidth,
                                d = n.offsetHeight,
                                _ = this.$decimal("-", u, o) * this.$devicePixelRatio,
                                m = this.$decimal("-", d, s, h) * this.$devicePixelRatio,
                                v = this.$decimal("+", r, l),
                                f = Math.ceil(_ / v) + 1,
                                x = 0,
                                $ = 0;
                            if (c >= 0) e = this.$copyObjectUnsafe(t.slice(0, f));
                            else {
                                var p = Math.abs(c);
                                $ = Math.floor(this.$decimal("/", p, v)),
                                    x = this.$decimal("-", _, -p, this.$decimal("*", $, v), l),
                                    e = this.$copyObjectUnsafe(t.slice($, $ + f))
                            }
                            this.$store.commit("overflow_data_num", $);
                            var g = e.map((function(t) {
                                    return t.high
                                })),
                                y = e.map((function(t) {
                                    return t.low
                                })),
                                T = Math.max.apply(Math, L(g)),
                                E = Math.min.apply(Math, L(y)),
                                w = this.$decimal("-", T, E),
                                b = this.$decimal("/", m, w);
                            this.$store.commit("every_data_px", b);
                            var A = e.map((function(t, e) {
                                var a = i.$decimal("*", v, e);
                                t.x = c >= 0 ? i.$decimal("-", _, c, a, l) : i.$decimal("-", x, a),
                                    t.candleLineHeight = Math.round(i.$decimal("*", b, i.$decimal("-", t.high, t.low))),
                                    t.candleLineStartY = Math.round(i.$decimal("-", m, (t.low - E) * b, t.candleLineHeight));
                                var n = Math.min(t.open, t.close);
                                return t.columnHeight = Math.round(i.$decimal("*", b, Math.abs(i.$decimal("-", t.open, t.close)))),
                                    t.columnStartY = Math.round(i.$decimal("-", m, (n - E) * b, t.columnHeight)),
                                    t.isRise = t.close >= t.open,
                                    t
                            }));
                            this.$store.commit("kline_data", A),
                                this.$store.commit("max", T),
                                this.$store.commit("min", E)
                        }
                    }]),
                    a
            } (A),
            W = function(e) {
                function a() {
                    var e;
                    return t(this, a),
                        (e = o(this, r(a).call(this))).mousemoveThrottleTimer = null,
                        e.mousewheelThrottleTimer = null,
                        e.c = e.$Od.$createElement("canvas"),
                        e.c.style.position = "absolute",
                        e.c.style.zIndex = 100,
                        e.c.style.top = 0,
                        e.c.style.left = 0,
                        e.c.style.cursor = "crosshair",
                        e.c.width = e._handleDevicePixelRatio({
                            value: e.$decimal("-", e.$store.state.el.offsetWidth, e.$store.state.y_axis_width)
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: e.$decimal("-", e.$store.state.el.offsetHeight, e.$store.state.x_axis_height)
                        }),
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height
                        }), "px"),
                        // console.log(e.c.height,'22222'),
                        e._ctx = e.c.getContext("2d"),
                        e.c.addEventListener("mousedown", e.mousedown.bind(s(e))),
                        e.c.addEventListener("mouseup", e.mouseup.bind(s(e))),
                        e.c.addEventListener("mousemove", e.mousemove.bind(s(e))),
                        e.c.addEventListener("mouseleave", e.mouseleave.bind(s(e))),
                        e.c.addEventListener("mouseenter", e.mouseenter.bind(s(e))),
                        e.c.addEventListener("mousewheel", e.mousewheel.bind(s(e))),
                        e.c.addEventListener("DOMMouseScroll", e.mousewheel.bind(s(e))),
                        e.c.addEventListener("contextmenu", e.contextmenu.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.ONE_DATA_UPDATED, e.onOneDataUpdated.bind(s(e))),
                        e.tagY = null,
                        e.tagX = null,
                        e.realTimeTagY = null,
                        e.createXTag(),
                        e.createYTag(),
                        e.createRealTimeYTag(),
                        e.INIT_OFFSET_X = e.$store.state.init_offset_x,
                        e.limitOffsetX = e.$decimal("*", e.c.width, 1 - e.$store.state.limit_offset_x),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "drawCrossDottedLine",
                        value: function(t, e) {
                            var i = e.x,
                                a = e.y;
                            this.$store.commit("current_cross_dash_line", {
                                x: i,
                                y: a
                            });
                            var n = this.$store.state,
                                s = n.max,
                                o = n.min,
                                r = n.kline_data,
                                l = n.column_width,
                                c = n.volume_height,
                                h = r[0].x,
                                u = h <= i && i <= this.$decimal("+", h, l);
                            this.$store.commit("cross_is_in_newest_data", u);
                            var d = this.$store.state.current_data.time,
                                _ = this._handleDevicePixelRatio({
                                    value: c
                                }),
                                m = this.$decimal("-", s, o),
                                v = this.$decimal("/", m, this.c.height - _),
                                f = this.$decimal("-", this.c.height, _, a),
                                x = this.$decimal("+", this.$decimal("*", v, f), o);
                            a > this.c.height - _ ? this.tagY.style.display = "none": this.tagY.style.display = "block",
                                this.tagY.innerText = x.toFixed(6),
                                this.tagY.style.top = "".concat(this._handleDevicePixelRatio({
                                    symbol: "/",
                                    value: a
                                }) - 8, "px"),
                                this.tagX.innerText = this.$time.format(d, "YYYY-MM-DD HH:mm"),
                                this.tagX.style.left = "".concat(i / this.$devicePixelRatio, "px"),
                                this._ctx.setLineDash([5]),
                                this._ctx.strokeStyle = "#758696",
                                this._ctx.beginPath(),
                                this._ctx.moveTo(0, a - .5),
                                this._ctx.lineTo(this.c.width, a - .5),
                                this._ctx.stroke(),
                                this._ctx.beginPath();
                            var $ = this.$decimal("+", i, Math.ceil(this.$store.state.column_width / 2)) - .5;
                            this._ctx.moveTo($, 0),
                                this._ctx.lineTo($, this.c.height),
                                this._ctx.stroke()
                        }
                    },
                        {
                            key: "dragMousemove",
                            value: function(t) {
                                var e = this.$store.state,
                                    i = e.all_kline_data,
                                    a = e.current_scale,
                                    n = e.init_offset_x;
                                this.$store.commit("no_scale_offset_x", this.$decimal("/", n, a));
                                var s = this.$decimal("*", this.$devicePixelRatio, this.$decimal("-", this.startDragX, t.layerX)),
                                    o = this.$decimal("+", this.INIT_OFFSET_X, s);
                                o > this.limitOffsetX && (o = this.limitOffsetX),
                                    this.$store.commit("init_offset_x", o),
                                    this.dealCanDrawViewData(i),
                                    this.$watcher.emit(p.WATCHER_EVENT.DRAG_MOUSE_MOVING)
                            }
                        },
                        {
                            key: "clearScreen",
                            value: function() {
                                this._clearScreen(this._ctx, this.c.width, this.c.height)
                            }
                        },
                        {
                            key: "mousemove",
                            value: function(t) {
                                var e = this;
                                null === this.mousemoveThrottleTimer && (this.mousemoveThrottleTimer = window.requestAnimationFrame((function() {
                                    e.$store.commit("current_mouse_coordinates", {
                                        x: t.layerX * e.$devicePixelRatio,
                                        y: t.layerY * e.$devicePixelRatio
                                    }),
                                        e.canDragable ? e.dragMousemove(t) : e.$store.state.kline_data.every((function(i) {
                                            var a = e.$decimal("+", i.x, e.$store.state.column_width);
                                            return ! (i.x <= t.layerX * e.$devicePixelRatio && i.x <= a) || (e.$store.commit("current_data", i), e.clearScreen(), e.drawCrossDottedLine(t, {
                                                x: i.x,
                                                y: t.layerY * e.$devicePixelRatio
                                            }), !1)
                                        })),
                                        e.$watcher.emit(p.WATCHER_EVENT.MOUSE_MOVING),
                                        window.cancelAnimationFrame(e.mousemoveThrottleTimer),
                                        e.mousemoveThrottleTimer = null
                                })))
                            }
                        },
                        {
                            key: "mousewheel",
                            value: function(t) {
                                var e = this;
                                null === this.mousewheelThrottleTimer && (this.mousewheelThrottleTimer = window.requestAnimationFrame((function() {
                                    var i = e.$store.state,
                                        a = i.scale_step,
                                        n = i.max_scale,
                                        s = i.min_scale,
                                        o = i.current_scale,
                                        r = i.all_kline_data,
                                        l = i.DEFAULT_COLUMN_WIDTH,
                                        c = i.DEFAULT_COLUMN_SPACE,
                                        h = i.no_scale_offset_x,
                                        u = o;
                                    t.deltaY < 0 || t.detail < 0 ? e.$decimal("*", h, u) <= e.limitOffsetX && o < n && (u = e.$decimal("+", o, a)) : o > s && (u = e.$decimal("-", o, a)),
                                        e.$store.commit("current_scale", u);
                                    var d = e.$store.state.current_scale;
                                    e.$store.commit("init_offset_x", e.$decimal("*", h, d)),
                                        e.INIT_OFFSET_X = e.$store.state.init_offset_x,
                                        e.$store.commit("column_width", e.$decimal("*", l, d)),
                                        e.$store.commit("column_space", e.$decimal("*", c, d)),
                                        e.dealCanDrawViewData(r),
                                        e.$watcher.emit(p.WATCHER_EVENT.MOUSE_SCALING),
                                        window.cancelAnimationFrame(e.mousewheelThrottleTimer),
                                        e.mousewheelThrottleTimer = null
                                })))
                            }
                        },
                        {
                            key: "mouseenter",
                            value: function() {
                                var t = this,
                                    e = setTimeout((function() {
                                        t.tagX.style.display = "block",
                                            t.tagY.style.display = "block",
                                            clearTimeout(e)
                                    }), 50);
                                e = null
                            }
                        },
                        {
                            key: "mouseleave",
                            value: function() {
                                var t = this;
                                this.canDragable = !1,
                                    this.INIT_OFFSET_X = this.$store.state.init_offset_x;
                                var e = setTimeout((function() {
                                    t.tagX.style.display = "none",
                                        t.tagY.style.display = "none",
                                        t.$store.commit("cross_is_in_newest_data", null),
                                        t.clearScreen(),
                                        clearTimeout(e)
                                }), 50);
                                e = null
                            }
                        },
                        {
                            key: "mousedown",
                            value: function(t) {
                                t.button || (this.startDragX = t.layerX, this.canDragable = !0, this.$Od.$setElementAttribute(this.c, "class", "is-grabbing"))
                            }
                        },
                        {
                            key: "mouseup",
                            value: function(t) {
                                t.button || (this.canDragable = !1, this.INIT_OFFSET_X = this.$store.state.init_offset_x, this.$Od.$removeElementAttribute(this.c, "class", "is-grabbing"))
                            }
                        },
                        {
                            key: "contextmenu",
                            value: function(t) {
                                t.preventDefault()
                            }
                        },
                        {
                            key: "createRealTimeYTag",
                            value: function() {
                                this.realTimeTagY = this.$Od.$createElement("div"),
                                    this.$Od.$setElementAttribute(this.realTimeTagY, "id", "real_time_volume"),
                                    this.$Od.$setElementAttribute(this.realTimeTagY, "style", {
                                        position: "absolute",
                                        zIndex: "80",
                                        right: 0,
                                        width: "".concat(this.$store.state.y_axis_width, "px"),
                                        fontSize: "12px",
                                        textAlign: "center",
                                        color: "#fff",
                                        userSelect: "none"
                                    }),
                                    this.$store.state.vy_chart.appendChild(this.realTimeTagY),
                                    this.$store.commit("realtime_tagY", this.realTimeTagY)
                            }
                        },
                        {
                            key: "createYTag",
                            value: function() {
                                this.tagY = this.$Od.$createElement("div"),
                                    this.$Od.$setElementAttribute(this.tagY, "id", "volume"),
                                    this.$Od.$setElementAttribute(this.tagY, "style", {
                                        position: "absolute",
                                        zIndex: "100",
                                        right: 0,
                                        width: "".concat(this.$store.state.y_axis_width, "px"),
                                        fontSize: "12px",
                                        textAlign: "center",
                                        color: "#fff",
                                        backgroundColor: "#585858",
                                        userSelect: "none"
                                    }),
                                    this.$store.state.vy_chart.appendChild(this.tagY),
                                    this.$store.commit("YTag", this.tagY)
                            }
                        },
                        {
                            key: "createXTag",
                            value: function() {
                                this.tagX = this.$Od.$createElement("div"),
                                    this.$Od.$setElementAttribute(this.tagX, "id", "date-time"),
                                    this.$Od.$setElementAttribute(this.tagX, "style", {
                                        position: "absolute",
                                        zIndex: "100",
                                        bottom: "".concat(this.$store.state.x_axis_height - 44, "px"),
                                        fontSize: "12px",
                                        color: "#fff",
                                        backgroundColor: "#585858",
                                        transform: "translate(-50%, 0)",
                                        whiteSpace: "noWrap",
                                        userSelect: "none"
                                    }),
                                    this.$store.state.vy_chart.appendChild(this.tagX)
                            }
                        },
                        {
                            key: "onOneDataUpdated",
                            value: function(t) {
                                this.$store.state.cross_is_in_newest_data && (this.tagX.innerText = this.$time.format(t.time, "YYYY-MM-DD HH:mm"))
                            }
                        }]),
                    a
            } (I),
            V = function(e) {
                function a() {
                    var e;
                    t(this, a);
                    var i = (e = o(this, r(a).call(this))).$store.state,
                        n = i.el,
                        l = i.x_axis_height,
                        c = i.y_axis_width,
                        h = i.volume_height;
                    return e.c = e.$Od.$createElement("canvas"),
                        e.c.width = e._handleDevicePixelRatio({
                            value: c
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: e.$decimal("-", n.offsetHeight, l, h)
                        }),
                        e.c.style.position = "absolute",
                        e.c.style.top = 0,
                        e.c.style.right = 0,
                        e.c.style.background = e.$store.state.background_color,
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height
                        }), "px"),
                        // console.log(e.c.height,'44444'),
                        e._ctx = e.c.getContext("2d"),
                        e.$watcher.on(p.WATCHER_EVENT.DRAG_MOUSE_MOVING, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.MOUSE_SCALING, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.REAL_TIME_DATA, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.HISTORY_DATA_CHANGE, e.reDraw.bind(s(e))),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function() {
                            this._drawBackground(this._ctx, this.c.width, this.c.height);
                            var t = this.$store.state,
                                e = t.theme,
                                i = t.min,
                                a = t.max,
                                n = t.axis_segment,
                                s = t.axis_font,
                                o = e.axis_color,
                                r = e.axis_text_color;
                            this._ctx.moveTo(.5, 0),
                                this._ctx.lineTo(.5, this.c.height),
                                this._ctx.lineWidth = 1,
                                this._ctx.strokeStyle = o,
                                this._ctx.stroke();
                            for (var l = v("/", this.c.height, v("-", a, i)), c = 0, h = Math.trunc(Math.log10(i)) - 1, u = Math.trunc(i / Math.pow(10, h)) * Math.pow(10, h), d = Math.ceil(Math.log10(a - i)) - 2, _ = 5 * Math.pow(10, d), m = 5 * Math.pow(10, d); _ * l < 40;) _ = this.$decimal("+", _, m);
                            this._ctx.strokeStyle = o,
                                this._ctx.fillStyle = r,
                                this._ctx.font = s;
                            for (var f = []; u + _ * c < a;) {
                                var x = u + _ * c;
                                x = x.toFixed(6);
                                var $ = this.c.height - l * (x - i),
                                    p = $ - .5;
                                f.push(p),
                                    this._ctx.beginPath(),
                                    this._ctx.moveTo(0, p),
                                    this._ctx.lineTo(n, p),
                                    this._ctx.lineWidth = 1,
                                    this._ctx.stroke(),
                                    this._ctx.fillText(x, 5, $ + 4),
                                    c++
                            }
                            this.$store.commit("y_axis_scale_list", f)
                        }
                    },
                        {
                            key: "reDraw",
                            value: function() {
                                this._ctx.clearRect(0, 0, this.c.width, this.c.height),
                                    this.draw()
                            }
                        }]),
                    a
            } (A),
            Y = {
                m: function(t) {
                    return {
                        time: Math.trunc(t.time / 6e4),
                        format: "HH:mm"
                    }
                },
                h: function(t) {
                    return {
                        time: Math.trunc(t.time / 36e5),
                        format: "DD HH:mm"
                    }
                },
                d: function(t) {
                    return {
                        time: Math.trunc(t.time / 864e5),
                        format: "MM-DD"
                    }
                },
                w: function(t) {
                    return {
                        time: Math.trunc(t.time / 6048e5),
                        format: "MM-DD"
                    }
                },
                M: function(t) {
                    return {
                        time: Math.trunc(t.time / 2592e6),
                        format: "YYYY-MM"
                    }
                }
            },
            U = function(e) {
                function a() {
                    var e;
                    t(this, a);
                    var i = (e = o(this, r(a).call(this))).$store.state,
                        n = i.el,
                        l = i.x_axis_height,
                        c = i.y_axis_width;
                    return e.c = e.$Od.$createElement("canvas"),
                        e.c.width = e._handleDevicePixelRatio({
                            value: e.$decimal("-", n.offsetWidth, c)
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: l
                        }),
                        e.c.style.position = "absolute",
                        e.c.style.bottom = 0,
                        e.c.style.left = 0,
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height
                        }), "px"),
                        e.c.style.background = e.$store.state.background_color,
                        e._ctx = e.c.getContext("2d"),
                        e.$watcher.on(p.WATCHER_EVENT.DRAG_MOUSE_MOVING, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.MOUSE_SCALING, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.REAL_TIME_DATA, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.HISTORY_DATA_CHANGE, e.reDraw.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.THEME_SWITCHED, (function(t) {
                            e.draw()
                        })),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function() {
                            var t = this;
                            this._drawBackground(this._ctx, this.c.width, this.c.height);
                            var e = this.$store.state,
                                i = e.theme,
                                a = e.axis_font,
                                n = e.kline_data,
                                s = e.current_scale,
                                o = e.current_interval,
                                r = e.column_width,
                                l = e.axis_segment,
                                c = i.axis_text_color,
                                h = i.axis_color;
                            if (n) {
                                this._ctx.moveTo(0, .5),
                                    this._ctx.lineTo(this.c.width, .5),
                                    this._ctx.lineWidth = 1,
                                    this._ctx.strokeStyle = h,
                                    this._ctx.stroke(),
                                    this._ctx.textAlign = "center";
                                var u = o.match(/[0-9]*$/),
                                    d = o[0];
                                this._ctx.strokeStyle = h,
                                    this._ctx.fillStyle = c,
                                    this._ctx.font = a,
                                    n.forEach((function(e, i) {
                                        var a = Y[d](e),
                                            n = a.time,
                                            o = a.format,
                                            c = Math.trunc(10 * u / s);
                                        if (! (n % (c = 5 * Math.ceil(c / 5)))) {
                                            var h = t.$decimal("+", e.x, Math.ceil(r / 2));
                                            t._ctx.beginPath(),
                                                t._ctx.moveTo(h - .5, 0),
                                                t._ctx.lineTo(h - .5, l * t.$devicePixelRatio),
                                                t._ctx.stroke();
                                            var _ = t.$time.format(e.time, o);
                                            t._ctx.fillText(_, h, 15 * t.$devicePixelRatio)
                                        }
                                    }))
                            }
                        }
                    },
                        {
                            key: "reDraw",
                            value: function() {
                                this._ctx.clearRect(0, 0, this.c.width, this.c.height),
                                    this.draw()
                            }
                        }]),
                    a
            } (A),
            X = function(e) {
                function a() {
                    var e;
                    t(this, a);
                    var i = (e = o(this, r(a).call(this))).$store.state,
                        n = i.el,
                        l = i.x_axis_height,
                        c = i.y_axis_width,
                        h = i.volume_height;
                    return e.c = e.$Od.$createElement("canvas"),
                        e.c.width = e._handleDevicePixelRatio({
                            value: e.$decimal("-", n.offsetWidth, c)
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: h
                        }),

                        e.c.style.position = "absolute",
                        e.c.style.bottom = "".concat(l, "px"),
                        e.c.style.left = 0,
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height
                        }), "px"),
                        e._ctx = e.c.getContext("2d"),
                        e.maxVolume = null,
                        e.minVolume = null,
                        e.dataGap = null,
                        e.everyDataPX = null,
                        e.YAxisStartX = e.$decimal("-", e._handleDevicePixelRatio({
                            value: e.c.width
                        }), c),
                        e.$watcher.on(p.WATCHER_EVENT.DRAG_MOUSE_MOVING, e.dragMouseMoving.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.MOUSE_SCALING, e.mouseScaling.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.REAL_TIME_DATA, e.onRealTimeData.bind(s(e))),
                        // console.log(e.c.height,'下面得柱形图'),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "drawTopLine",
                        value: function() {
                            var t = this.$store.state.theme.axis_color;
                            this._ctx.strokeStyle = t,
                                this._ctx.beginPath(),
                                this._ctx.moveTo(0, .5),
                                this._ctx.lineTo(this.c.width, .5),
                                this._ctx.lineWidth = 1,
                                this._ctx.stroke()
                        }
                    },
                        {
                            key: "draw",
                            value: function() {
                                var t = this;
                                this._clearScreen(this._ctx, this.c.width, this.c.height),
                                    this._drawBackground(this._ctx, this.c.width, this.c.height),
                                    this.drawTopLine();
                                var e = this.$store.state,
                                    i = e.kline_data,
                                    a = (e.volume_height, e.current_scale, e.y_axis_width, e.column_width),
                                    n = e.default_rise_color,
                                    s = e.default_fall_color,
                                    o = i.map((function(t) {
                                        return t.volume
                                    }));
                                this.maxVolume = Math.max.apply(Math, L(o)),
                                    this.minVolume = Math.min.apply(Math, L(o)),
                                    this.dataGap = this.$decimal("-", this.maxVolume, this.minVolume),
                                    this.everyDataPX = this.$decimal("/", this.c.height, this.dataGap),
                                    i.forEach((function(e) {
                                        t._ctx.beginPath(),
                                            t._ctx.strokeStyle = t._ctx.fillStyle = e.close >= e.open ? n: s;
                                        var i = t.$decimal("-", e.volume, t.minVolume),
                                            o = t.$decimal("*", t.everyDataPX, i)+8,
                                            r = t.$decimal("-", t.c.height, o),
                                            l = e.x - .5;
                                        t._ctx.strokeRect(l, r, a, o),
                                            t._ctx.globalAlpha = .6,
                                            t._ctx.fillRect(l, r, a, o)
                                    }))
                            }
                        },
                        {
                            key: "dragMouseMoving",
                            value: function() {
                                this._clearScreen(this._ctx, this.c.width, this.c.height),
                                    this.draw()
                            }
                        },
                        {
                            key: "mouseScaling",
                            value: function() {
                                this._clearScreen(this._ctx, this.c.width, this.c.height),
                                    this.draw()
                            }
                        },
                        {
                            key: "onRealTimeData",
                            value: function() {
                                this._clearScreen(this._ctx, this.c.width, this.c.height),
                                    this.draw()
                            }
                        }]),
                    a
            } (A),
            G = function() {
                function e(i, a) {
                    var n = a.data,
                        s = void 0 === n ? 0 : n,
                        o = a.html;
                    t(this, e),
                        this.span = i.$createElement("span"),
                        this.html = o,
                        this.data = s,
                        this.span.innerHTML = "".concat(o, '=<i style="color: ').concat(this.color, ';">').concat(this.data, "</i>")
                }
                return i(e, [{
                    key: "setData",
                    value: function(t, e, i) {
                        this.data = t,
                            this.span.innerHTML = "".concat(this.html, '=<i style="color: ').concat(e, ';">').concat(this.data, "</i>"),
                            this.span.style.color = i,
                            this.span.style.margin = '0px 10px'
                    }
                }]),
                    e
            } (),
            F = function(e) {
                function a() {
                    var e;
                    return t(this, a),
                        (e = o(this, r(a).call(this))).dashboardWrapper = e.$Od.$createElement("div"),
                        e.$Od.$setElementAttribute(e.dashboardWrapper, "id", "dashboard-wrapper"),
                        e.$Od.$setElementAttribute(e.dashboardWrapper, "style", {
                            position: "absolute",
                            zIndex: "50",
                            left: 0,
                            top: 0,
                            width:"100%",
                            height:"20px",
                            // background:"rgb(19, 23, 34)"
                            background:"rgb(255, 255, 255)"
                        }),
                        e.spanWrapper = e.$Od.$createElement("div"),
                        e.$Od.$setElementAttribute(e.spanWrapper, "id", "span-wrapper"),
                        e.dashboardWrapper.appendChild(e.spanWrapper),
                        e.openSpan = new G(e.$Od, {
                            html: window.kai
                        }),
                        e.highSpan = new G(e.$Od, {
                            html: window.gao
                        }),
                        e.lowSpan = new G(e.$Od, {
                            html: window.di
                        }),
                        e.closeSpan = new G(e.$Od, {
                            html: window.shou
                        }),
                        e.volSpan = new G(e.$Od, {
                            html: "Vol"
                        }),
                        e.MA5Span = new G(e.$Od, {
                            html: "MA5"
                        }),
                        e.MA10Span = new G(e.$Od, {
                            html: "MA10"
                        }),
                        e.spanWrapper.appendChild(e.openSpan.span),
                        e.spanWrapper.appendChild(e.highSpan.span),
                        e.spanWrapper.appendChild(e.lowSpan.span),
                        e.spanWrapper.appendChild(e.closeSpan.span),
                        e.spanWrapper.appendChild(e.volSpan.span),
                        e.spanWrapper.appendChild(e.MA5Span.span),
                        e.spanWrapper.appendChild(e.MA10Span.span),
                        e.$watcher.on(p.WATCHER_EVENT.MOUSE_MOVING, e.mouseMoving.bind(s(e))),

                        e.$watcher.on(p.WATCHER_EVENT.REAL_TIME_DATA, e.onRealTimeData.bind(s(e))),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "setSpanInnerText",
                        value: function(t) {
                            var e = t.open,
                                i = t.high,
                                a = t.low,
                                n = t.close,
                                s = t.volume,
                                o = t.MAClose5,
                                r = void 0 === o ? 0 : o,
                                l = t.MAClose10,
                                c = void 0 === l ? 0 : l,
                                h = this.$store.state,
                                u = h.default_rise_color,
                                d = h.default_fall_color,
                                _ = h.axis_text_color,
                                m = h.ma_Lines,
                                v = h.precision,
                                f = n >= e ? u: d;
                            this.openSpan.setData(e.toFixed(v), f, _),
                                this.highSpan.setData(i.toFixed(v), f, _),
                                this.lowSpan.setData(a.toFixed(v), f, _),
                                this.closeSpan.setData(n.toFixed(v), f, _),
                                this.volSpan.setData(s, f, _),
                                this.MA5Span.setData(r.toFixed(v), m[0].lineColor, _),
                                this.MA10Span.setData(c.toFixed(v), m[1].lineColor, _)
                        }
                    },
                        {
                            key: "mouseMoving",
                            value: function() {
                                if (this.$store.state.current_data) {
                                    // console.log(this.$store.state.current_data);
                                    var t = this.$store.state.current_data,
                                        e = t.open,
                                        i = t.high,
                                        a = t.low,
                                        n = t.close,
                                        s = t.volume,
                                        o = t.MAClose5,
                                        r = t.MAClose10;
                                    this.setSpanInnerText({
                                        open: e,
                                        high: i,
                                        low: a,
                                        close: n,
                                        volume: s,
                                        MAClose5: o,
                                        MAClose10: r
                                    })
                                }
                            }
                        },
                        {
                            key: "onRealTimeData",
                            value: function() {
                                var t = this.$store.state,
                                    e = t.cross_is_in_newest_data,
                                    i = t.kline_data;
                                if (e || null === e) {
                                    // console.log(i[0]);
                                    var a = i[0],
                                        n = a.open,
                                        s = a.high,
                                        o = a.low,
                                        r = a.close,
                                        l = a.volume,
                                        c = a.MAClose5,
                                        h = a.MAClose10;
                                    this.setSpanInnerText({
                                        open: n,
                                        high: s,
                                        low: o,
                                        close: r,
                                        volume: l,
                                        MAClose5: c,
                                        MAClose10: h
                                    })
                                }
                            }
                        }]),
                    a
            } (A),
            j = /0{3}$/,
            B = function(e) {
                function a() {
                    var e;
                    t(this, a);
                    var i = (e = o(this, r(a).call(this))).$store.state,
                        n = (i.el, i.x_axis_height),
                        l = i.y_axis_width,
                        c = i.volume_height;
                    return e.c = e.$Od.$createElement("canvas"),
                        e.c.width = e._handleDevicePixelRatio({
                            value: l
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: e.$decimal("-", c)
                        }),

                        e.c.style.position = "absolute",
                        e.c.style.bottom = "".concat(e.$decimal("-", n), "px"),
                        e.c.style.right = 0,
                        e.c.style.background = e.$store.state.background_color,
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height
                        }), "px"),
                        e._ctx = e.c.getContext("2d"),
                        e.$watcher.on(p.WATCHER_EVENT.DRAG_MOUSE_MOVING, e.dragMouseMoving.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.MOUSE_SCALING, e.mouseScaling.bind(s(e))),
                        e.$watcher.on(p.WATCHER_EVENT.REAL_TIME_DATA, e.onRealTimeData.bind(s(e))),
                        // console.log(e.c.height,'右边得一条柱形'),
                        e.$watcher.on(p.WATCHER_EVENT.THEME_SWITCHED, (function(t) {
                            e.draw()
                        })),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function() {
                            this._drawBackground(this._ctx, this.c.width, this.c.height);
                            var t = this.$store.state.theme.axis_color;
                            this._ctx.strokeStyle = t,
                                this._ctx.beginPath(),
                                this._ctx.moveTo(.5, 0),
                                this._ctx.lineTo(.5, this.c.height),
                                this._ctx.lineWidth = 1,
                                this._ctx.stroke(),
                                this.drawVolYAxis()
                        }
                    },
                        {
                            key: "drawVolYAxis",
                            value: function() {
                                var t = this.$store.state,
                                    e = t.theme,
                                    i = t.kline_data,
                                    a = t.volume_height,
                                    n = t.axis_segment,
                                    s = t.axis_font,
                                    o = e.axis_text_color,
                                    r = i.map((function(t) {
                                        return t.volume
                                    }));
                                this.maxVolume = Math.max.apply(Math, L(r)),
                                    this.minVolume = Math.min.apply(Math, L(r)),
                                    this.dataGap = this.$decimal("-", this.maxVolume, this.minVolume);
                                var l = this._handleDevicePixelRatio({
                                    value: a
                                });
                                this.everyDataPX = this.$decimal("/", l, this.dataGap);
                                for (var c = Math.trunc(Math.log10(this.dataGap)) - 1, h = Math.trunc(this.minVolume), u = 5 * Math.pow(10, c), d = u; u * this.everyDataPX < 20;) u = this.$decimal("+", u, d);
                                this._ctx.fillStyle = o,
                                    this._ctx.font = s;
                                for (var _ = 1; h + u * _ < this.maxVolume;) {
                                    var m = String(u * _);
                                    j.test(m) && (m = m.replace(j, "k"));
                                    var v = this.c.height - this.everyDataPX * u * _;
                                    this._ctx.beginPath(),
                                        this._ctx.moveTo(0, v - .5),
                                        this._ctx.lineTo(n, v - .5),
                                        this._ctx.lineWidth = 1,
                                        this._ctx.stroke(),
                                        this._ctx.fillText(m, 5, v + this._handleDevicePixelRatio({
                                            value: 4
                                        })),
                                        _++
                                }
                            }
                        },
                        {
                            key: "dragMouseMoving",
                            value: function() {
                                this._ctx.clearRect(0, 0, this.c.width, this.c.height),
                                    this.draw()
                            }
                        },
                        {
                            key: "mouseScaling",
                            value: function() {
                                this._ctx.clearRect(0, 0, this.c.width, this.c.height),
                                    this.draw()
                            }
                        },
                        {
                            key: "onRealTimeData",
                            value: function() {
                                this._ctx.clearRect(0, 0, this.c.width, this.c.height),
                                    this.draw()
                            }
                        }]),
                    a
            } (A),
            K = {
                m: {
                    text: window.fen
                },
                h: {
                    text: window.xiaoshi
                },
                d: {
                    text: window.tian
                },
                w: {
                    text: window.zhou
                },
                M: {
                    text: "Mon"
                }
            },
            z = function(e) {
                function a() {
                    var e;
                    return t(this, a),
                        (e = o(this, r(a).call(this))).c = e.$Od.$createElement("div"),
                        e.throttleTimer = null,
                        e.$Od.$setElementAttribute(e.c, "id", "interval_tool_bar"),
                        e.$Od.$setElementAttribute(e.c, "style", {
                            height: e.$store.state.interval_tool_bar,
                            lineHeight: e.$store.state.interval_tool_bar,
                            backgroundColor: e.$store.state.background_color
                        }),
                        // console.log(e.$store.state.interval_tool_bar,'时间 '),
                    e.$store.state.user_config.theme && e.$Od.$setElementAttribute(e.c, "class", e.$store.state.user_config.theme),
                        e.createTimeShareBtn(),
                        e.createIntervalBtn(),
                        e.$watcher.on(p.WATCHER_EVENT.THEME_SWITCHED, (function(t) {
                            e.$Od.$setElementAttribute(e.c, "style", {
                                backgroundColor: e.$store.state.theme.background_color
                            }),
                                e.$Od.$setElementAttribute(e.c, "class", t)
                        })),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "createIntervalBtn",
                        value: function() {
                            var t = this,
                                e = this.$store.state,
                                i = e.user_config,
                                a = e.chart_instance,
                                n = i.interval,
                                s = i.deedfeeds,
                                o = i.onTimeIntervalChanged;
                            n.forEach((function(e) {
                                var i = t.$Od.$createElement("button"),
                                    n = e.slice(1);
                                i.innerText = "".concat(n, " ").concat(K[e[0]].text),
                                    t.$Od.$setElementAttribute(i, "class", "interval-item"),
                                    t.$Od.$setElementAttribute(i, "data-interval-item", e);
                                var r = t.$store.state,
                                    l = r.current_interval,
                                    c = r.loadingNode;
                                l === e && t.$Od.$setElementAttribute(i, "class", "interval-item active"),
                                    i.onclick = function() {
                                        if (!t.throttleTimer) {
                                            var n = t.$store.state,
                                                r = n.current_interval,
                                                l = n.chart_type;
                                            if (r === e && l === t.CONSTANTS.CHART_TYPE.KLINE) return;
                                            t.throttleTimer = setTimeout((function() {
                                                var kuang = document.body.clientWidth-document.getElementById('real_time_volume').clientWidth;
                                                var kuang2 = kuang/100
                                                if (e == 'w1' &&  window.SYMBOL == 'vatusdt'){


                                                    t.$store.commit("init_offset_x",kuang-(2*6)) ;
                                                    console.log(kuang2,'eeee');
                                                }else if (e == 'd1'  &&  window.SYMBOL == 'vatusdt'){

                                                    t.$store.commit("init_offset_x", kuang-(17*6)) ;
                                                }else if (e == 'h4' &&  window.SYMBOL == 'vatusdt' ){

                                                    t.$store.commit("init_offset_x", kuang-(107*6)) ;
                                                    console.log(kuang2,'eeee');
                                                }else{
                                                    t.$store.commit("init_offset_x", window.init_offset) ;

                                                }
                                                "function" == typeof o && o(e),
                                                    c.style.display = "block",
                                                    t.$store.commit("chart_type", l),
                                                    t.$store.commit("all_kline_data", []),


                                                    t.$store.commit("kline_data", []),
                                                    t.$store.commit("current_interval", e),
                                                    t.$Od.$setElementAttribute(document.getElementsByClassName("interval-item active")[0], "class", "interval-item"),
                                                    t.$Od.$setElementAttribute(i, "class", "interval-item active"),
                                                    s.intervalChanged({
                                                        interval: e,
                                                        setHistoryData: a.initHistoryData,
                                                        subscribeData: a.getRealTimeData
                                                    }),
                                                    t.throttleTimer = null;

                                            }), 200)
                                        }
                                    },
                                    t.c.appendChild(i)
                            }))
                        }
                    },
                        {
                            key: "createTimeShareBtn",
                            value: function() {
                                var t = this,
                                    e = this.$store.state,
                                    i = e.user_config,
                                    a = e.chart_instance,
                                    n = e.loadingNode,
                                    s = i.deedfeeds,
                                    o = this.$Od.$createElement("button");
                                o.innerText = window.fenshi,
                                    this.$Od.$setElementAttribute(o, "class", "interval-item"),
                                    this.$Od.$setElementAttribute(o, "data-interval-item", "time-sharing"),
                                    o.onclick = function() {
                                        n.style.display = "block",

                                            t.$store.commit("chart_type", t.CONSTANTS.CHART_TYPE.REAL_TIME),
                                            t.$store.commit("all_kline_data", []),
                                            t.$store.commit("current_interval", "m1"),
                                            t.$Od.$setElementAttribute(document.getElementsByClassName("interval-item active")[0], "class", "interval-item"),
                                            t.$Od.$setElementAttribute(o, "class", "interval-item active"),
                                            s.intervalChanged({
                                                interval: "m1",
                                                setHistoryData: a.initHistoryData,
                                                subscribeData: a.getRealTimeData
                                            })
                                    },
                                    this.c.appendChild(o)
                            }
                        }]),
                    a
            } (A),
            q = function(e) {
                function a() {
                    var e;
                    return t(this, a),
                        (e = o(this, r(a).call(this))).c = e.$Od.$createElement("canvas"),
                        e.c.width = e._handleDevicePixelRatio({
                            value: e.$store.state.y_axis_width
                        }),
                        e.c.height = e._handleDevicePixelRatio({
                            value: e.$store.state.x_axis_height
                        }),
                        e.c.style.width = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.width
                        }), "px"),
                        e.c.style.height = "".concat(e._handleDevicePixelRatio({
                            symbol: "/",
                            value: e.c.height
                        }), "px"),
                        // console.log(e.c.height,'右边得一条柱形'),
                        e._ctx = e.c.getContext("2d"),
                        e.$Od.$setElementAttribute(e.c, "style", {
                            position: "absolute",
                            bottom: 0,
                            right: 0,
                            background: e.$store.state.theme.background_color
                        }),
                        e.draw(),
                        e.$watcher.on(p.WATCHER_EVENT.THEME_SWITCHED, (function() {
                            e.$Od.$setElementAttribute(e.c, "style", {
                                background: e.$store.state.theme.background_color
                            }),
                                e.draw()
                        })),
                        e
                }
                return c(a, e),
                    i(a, [{
                        key: "draw",
                        value: function() {
                            var t = this.$store.state.theme.axis_color;
                            this._ctx.moveTo(0, .5),
                                this._ctx.lineTo(this.c.width, .5),
                                this._ctx.moveTo(.5, 0),
                                this._ctx.lineTo(.5, this.c.height),
                                this._ctx.lineWidth = 1,
                                this._ctx.strokeStyle = t,
                                this._ctx.stroke()
                        }
                    }]),
                    a
            } (A);
        var J = new(function(e) {
            function a() {
                var e;
                return t(this, a),
                    (e = o(this, r(a).call(this))).customChart = null,
                    e
            }
            return c(a, e),
                i(a, [{
                    key: "bootstrap",
                    value: function(t) {
                        var e = t.container,
                            i = t.deedfeeds,
                            a = t.defaultInterval,
                            n = t.customChart,
                            s = t.showIntervalToolbar,
                            o = void 0 === s || s,
                            r = t.customLoadingEl;
                        this.$store.commit("user_config", t),
                            this.customChart = n,
                            this._customChart(n),
                            this.$store.commit("deedfeeds", i),
                            this.$store.commit("current_interval", a),
                            r ? r instanceof HTMLElement && (this.loading = r, this.$store.commit("loadingNode", this.loading), e.appendChild(this.loading)) : (this.loading = function(t, e, i) {
                                var a = t.$createElement("div"),
                                    n = t.$createElement("div");
                                return t.$setElementAttribute(a, "id", "loading"),
                                    t.$setElementAttribute(n, "class", "spin"),
                                    n.innerText = "loading...",
                                    // console.log(111111),
                                    t.$setElementAttribute(a, "style", {
                                        display: "block",
                                        position: "absolute",
                                        zIndex: "9527",
                                        top: 0,
                                        left: 0,
                                        width: "100%",
                                        height: "100%",
                                        backgroundColor: "rgba(255,255,255, 0.6)"
                                    }),
                                    a.appendChild(n),
                                    e.appendChild(a),
                                    a
                            } (this.$Od, e, this.$store.state), this.$store.commit("loadingNode", this.loading)),
                            this.intervalToolBarInstance = new z,
                            e.appendChild(this.intervalToolBarInstance.c),
                            this.intervalToolBarInstance.c.style.display = o ? "block": "none",
                            this.vyChart = function(t, e, i) {
                                var a = t.$createElement("div");
                                return t.$setElementAttribute(a, "id", "vy-chart"),
                                    t.$setElementAttribute(a, "style", {
                                        position: "absolute",
                                        bottom: 0,
                                        width: "100%",
                                        height: "calc(100% - ".concat(i.interval_tool_bar, ")")
                                    }),
                                    // console.log(a),
                                    e.appendChild(a),
                                    a
                            } (this.$Od, e, this.$store.state),
                            this.$store.commit("el", this.vyChart),
                            this.$store.commit("vy_chart", this.vyChart),
                            e.style.position = "relative",
                            this.container = e,
                            this.deedfeeds = i,
                            this.KlineCanvas = null,
                            this.YAxis = null,
                            this.XAxis = null,
                            this.Dashboard = null,
                            this.VolumeCanvas = null,
                            this.BottomRightBlock = null,
                            this.initHistoryData = this.initHistoryData.bind(this),
                            this.getRealTimeData = this.getRealTimeData.bind(this),
                            i.setHistoryData({
                                interval: a,
                                setHistoryData: this.initHistoryData,
                                subscribeData: this.getRealTimeData
                            })
                    }
                },
                    {
                        key: "initHistoryData",
                        value: function(t) {
                            this.$store.commit("all_kline_data", t),
                                this.dealCanDrawViewData(t),
                            this.KlineCanvas || this.initViewOnce(),
                                this.KlineCanvas.drawHistory(),
                                this.VolumeCanvas.draw(),
                                this.$watcher.emit(p.WATCHER_EVENT.HISTORY_DATA_CHANGE),
                                this.loading.style.display = "none"
                        }
                    },
                    {
                        key: "getRealTimeData",
                        value: function(t) {
                            if (t.time) {
                                var e = this.$store.state.all_kline_data;
                                e[0] && (e[0].time === t.time ? e[0] = t: (e.unshift(t), this.$watcher.emit(p.WATCHER_EVENT.ONE_DATA_UPDATED, t)), this.$store.commit("all_kline_data", e), this.initHistoryData(this.$store.state.all_kline_data), this.$watcher.emit(p.WATCHER_EVENT.REAL_TIME_DATA))
                            }
                        }
                    },
                    {
                        key: "initViewOnce",
                        value: function() {
                            this.KlineCanvas = new R({
                                backgroundColor: "#fff"
                            }),
                                this.$store.commit("kline_canvas", this.KlineCanvas),
                                this.KlineMaskCanvas = new W,
                                this.XAxis = new U,
                                this.YAxis = new V,
                                this.VolYAxis = new B,
                                this.VolumeCanvas = new X,
                                this.Dashboard = new F,
                                this.BottomRightBlock = new q,
                                this.intervalToolBarInstance.c.style.backgroundColor = this.$store.state.background_color,
                                this.XAxis.draw(),
                                this.YAxis.draw(),
                                this.VolYAxis.draw(),
                                this.BottomRightBlock.draw(),
                                this.vyChart.appendChild(this.KlineCanvas.c),
                                this.vyChart.appendChild(this.KlineMaskCanvas.c),
                                this.vyChart.appendChild(this.VolumeCanvas.c),
                                this.vyChart.appendChild(this.XAxis.c),
                                this.vyChart.appendChild(this.YAxis.c),
                                this.vyChart.appendChild(this.VolYAxis.c),
                                this.vyChart.appendChild(this.Dashboard.dashboardWrapper),
                                this.vyChart.appendChild(this.BottomRightBlock.c)
                        }
                    },
                    {
                        key: "switchChartType",
                        value: function(t) {
                            this.$store.commit("chart_type", t),
                                this.initHistoryData(this.$store.state.all_kline_data)
                        }
                    },
                    {
                        key: "switchTheme",
                        value: function(t) {
                            this.$store.commit("theme", this.THEME[t]),
                                this.$watcher.emit(p.WATCHER_EVENT.THEME_SWITCHED, t),
                                this.initHistoryData(this.$store.state.all_kline_data)
                        }
                    }]),
                a
        } (I));
        return J.$store.commit("chart_instance", J),
            window.vyChart = J,
            J
    }));