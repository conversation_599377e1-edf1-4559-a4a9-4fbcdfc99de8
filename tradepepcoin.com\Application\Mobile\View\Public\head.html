<link rel="stylesheet" href="{:stamp('__PUBLIC__/Mobile/ecshe_css/mui.min.css')}" />
<link rel="stylesheet" href="{:stamp('__PUBLIC__/Mobile/ecshe_css/wapmain.css')}" />
<link rel="stylesheet" href="{:stamp('__PUBLIC__/Mobile/ecshe_css/iconfont.css')}" />
<link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
<script>
// 移动端图片404与双后缀修复
(function(){
  function fixExt(u){ if(!u) return u; return u.replace(/\.png\.png$/i,'.png').replace(/\.jpg\.jpg$/i,'.jpg').replace(/\.jpeg\.jpeg$/i,'.jpeg').replace(/\.gif\.gif$/i,'.gif'); }
  document.addEventListener('error', function(e){
    var el=e.target; if(!el||el.tagName!=='IMG')return;
    if(el.dataset && el.dataset.imgFixed==='1'){ el.src='/Public/Home/static/imgs/empty-dark.png'; return; }
    if(el.dataset) el.dataset.imgFixed='1';
    var s=el.getAttribute('src')||''; var f=fixExt(s);
    if(f!==s){ el.src=f; return; }
    if(!/\.(png|jpg|jpeg|gif|svg)([?#]|$)/i.test(s)){ el.src=s+'.png'; return; }
    el.src='/Public/Home/static/imgs/empty-dark.png';
  }, true);
})();
</script>

<script type="text/javascript" src="{:stamp('__PUBLIC__/Mobile/js/jquery-1.11.3.min.js')}"></script>
<script type="text/javascript" src="{:stamp('__PUBLIC__/layer/layer.js')}"></script>
<script src="/Public/Home/static/js/jump-fix.js"></script>
<style>
.van-overlay, .mui-backdrop, [class*="mask"], [class*="overlay"], [class*="shade"] {
  pointer-events: none !important;
  background: transparent !important;
}
a { pointer-events: auto !important; cursor: pointer !important; }
</style>

<script>window.gologin=window.gologin||function(){ window.location.href="{:U('Login/index')}"; };</script>
