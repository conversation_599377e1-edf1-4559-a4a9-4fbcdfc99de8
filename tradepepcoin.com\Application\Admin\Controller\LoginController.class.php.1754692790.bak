<?php
namespace Admin\Controller;
class LoginController extends \Think\Controller {
  public function index(){
    if(IS_POST){
      $username = I('post.username','','trim');
      $password = I('post.password','','trim');
      $verify = I('post.verify','','trim');

      // 详细调试信息
      error_log("Login attempt - Username: " . $username . ", Password: " . $password);
      
      $admin = M('Admin')->where(array('username'=>$username))->find();
      error_log("Query result: " . json_encode($admin));
      
      if(!$admin){
        error_log("Admin not found for username: " . $username);
        $this->ajaxReturn(array('status'=>0, 'info'=>'用户不存在', 'debug'=>'username='.$username));
      }

      if($admin['password'] != md5($password)){
        error_log("Password mismatch - DB: " . $admin['password'] . ", Input MD5: " . md5($password));
        $this->ajaxReturn(array('status'=>0, 'info'=>'密码错误'));
      }

      // 更新登录时间
      M('Admin')->where(array('id'=>$admin['id']))->save(array(
        'last_login_time' => time(),
        'last_login_ip' => get_client_ip()
      ));
      
      session('admin_id',$admin['id']);
      session('admin_username',$admin['username']);
      $this->ajaxReturn(array('status'=>1, 'info'=>'登录成功', 'url'=>'/index.php?s=Admin/Index/index'));
    }else{
      $this->display();
    }
  }

  public function logout(){
    session(null);
    $this->redirect('Login/index');
  }
}
