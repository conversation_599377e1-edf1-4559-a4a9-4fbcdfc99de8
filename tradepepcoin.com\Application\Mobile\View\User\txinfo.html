<!DOCTYPE html>
<html lang="zh-CN" style="background:#fff;">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base2.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<link rel="stylesheet" href="/Public/Static/Icoinfont/iconfont.css">
	<script src="/Public/Static/Icoinfont/iconfont.js"></script>
	<title>{$webname}</title>
	<style>
	    ::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	    ::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	    input:focus{background:#f5f5f5;outline: 1px solid #f5f5f5;}
	    a:hover,a:link,a:visited,a:active{color:#000000;text-decoration:none;}
	    .no_header{position: fixed;z-index: 9999;padding:0px 10px;top:0px;line-height: 50px;background:#fff;}
	    .lbox{width:100%;height:50px;border-bottom:1px solid #f5f5f5;}
	    .lbox_l{width:50%;height:50px;line-height:50px;float:left;text-align:left;}
	    .lbox_r{width:50%;height:50px;line-height:50px;float:right;text-align:right;}
	    .infobox{width:100%;height:400px;padding:5px 20px;margin-top:50px;}
	    .infotbox{width:100%;height:50px;line-height:50px;border-bottom:1px solid #f5f5f5;}
	    .overflow{overflow: hidden;text-overflow:ellipsis;white-space: nowrap;width:100%;height:35px;line-height:35px;}
	</style>
</head>
<body style="background:#fff;">
	<div class="container-fluid " style="padding:0px;width:100vw;">
        <div class="no_header">
			<div class="fl allhg txtl">
				<i class="bi bi-arrow-left fcc fw" onclick="goback()" style="font-size: 24px;"></i>
			</div>
			<div class="fr allhg txtr" style="line-height:50px;width:10%;"></div>
		</div>
		
		<div class="infobox">
		    <div class="infotbox">
		        <span class="fcy" style="font-size:24px;font-weight:bold;">-{$info.num} USDT</span>
		    </div>
		    <div class="lbox">
		        <div class="lbox_l">
		            <span class="fcc fzmm">{:L('类型')}</span>
		        </div>
		        <div class="lbox_r">
		            <span class="fch fzmm">{:L('普通提币')}</span>
		        </div>
		    </div>
		    <div class="lbox">
		        <div class="lbox_l">
		            <span class="fcc fzmm">{:L('手续费')}</span>
		        </div>
		        <div class="lbox_r">
		            <span class="fch fzmm">{$info.fee}</span>
		        </div>
		    </div>
		    
		    <div class="lbox">
		        <div class="lbox_l">
		            <span class="fcc fzmm">{:L('实际到账')}</span>
		        </div>
		        <div class="lbox_r">
		            <span class="fch fzmm">{$info.mum}</span>
		        </div>
		    </div>
		    
		    <div class="lbox">
		        <div class="lbox_l">
		            <span class="fcc fzmm">{:L('提币地址')}</span>
		        </div>
		        <div class="lbox_r" style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
		            <span class="fch fzmm">{$info.address}</span>
		        </div>
		    </div>
		    
		    <div class="lbox">
		        <div class="lbox_l">
		            <span class="fcc fzmm">{:L('状态')}</span>
		        </div>
		        <div class="lbox_r">
		            <?php if($info['status'] == 2){?>
		                <span class="fch fzmm"><?php echo L('已完成');?></span>
		            <?php }elseif($info['status'] == 3){?>
		                <span class="fch fzmm"><?php echo L('未通知');?></span>
		            <?php }elseif($info['status'] == 1){?>
		                <span class="fch fzmm"><?php echo L('确认中');?></span>
		            <?php }?>
		        </div>
		    </div>
		    <div class="lbox">
		        <div class="lbox_l">
		            <span class="fcc fzmm">{:L('时间')}</span>
		        </div>
		        <div class="lbox_r">
		            <span class="fch fzmm">{$info.addtime}</span>
		        </div>
		    </div>
		    
		    
		    
		    
		</div>

	</div>	
</body>
<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>

<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
</script>
</html>



