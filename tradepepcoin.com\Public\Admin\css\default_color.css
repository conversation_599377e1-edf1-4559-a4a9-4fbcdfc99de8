/********************************************
 * base.css
 * 描述：重置页面元素并设置布局样式
 * 作用范围： 全局
 *******************************************/
body{
	background-color: #f1f2f7!important;
}
html, body, input, textarea, select, legend {
    color: #797979;
    font-family: 'Open Sans', sans-serif;
    /*全局默认文字颜色*/
}
a {
    color: #2d7200;
    /*全局默认链接颜色*/
}
a:hover {
    color: #8f9fe2;
    /*全局默认链接悬停颜色*/
}
/*************************************************
 * common.css
 * 描述：定义公共类名
 * 作用范围: 公共，独立样式
 *************************************************/

/* 文章搜索/用户搜素 下拉弹出按钮 实心小箭头 */
.arrow-down {
    border-top-color: #000;
}
.arrow-up {
    border-bottom-color: #000;
}
.arrow-left {
    border-right-color: #000;
}
.arrow-right {
    border-left-color: #000;
}
/**********************************************
 * module.css
 * 描述：定义常用组件样式
 * 作用范围: 公共，组件模块
 ***********************************************/

/* 文章新增编辑页,权限授权页,分类编辑页 tab标签 */
.tab-nav {
    border-bottom-color: #e0e0e0;
}
.tab-nav .current a,
.tab-nav .current a:hover {
    border-color: #34b4e0 #e0e0e0 #f6f6f6;
}
/* (暂未使用) 面包屑导航 */
.breadcrumb {
    color: #999;
}
.tags-crumb li {
    background-color: #e0e0e0;
}
.tags-crumb .current {
    background-color: #f60;
}
.tags-crumb .current a {
    color: #fff;
}
.tags-crumb li i {
    border-left-color: #fff
}
.tags-crumb li b {
    border-left-color: #e0e0e0
}
.tags-crumb .current b {
    border-left-color: #f60;
}
/* 列表分页 */
.page a,
.page span {
    color: #686868;
    border: 1px solid #CCCCCC;
}
.page a:hover {
    background: #5ac712;
}
.page .current {
    border-top: 3px solid #4BBD00;
    height: 28px;
    line-height: 26px;
}
/* uploadify插件上传按钮 */
.uploadify-button {
    color: #fff;
    background-color: #27ae60;
}
/* 分类管理/分类授权 树形菜单（目前只支持3级）
------------------------------------------ */
.category .hd {
    border-bottom-color: #d4d4d4;
    color: #fff;
    background-color: #34495e;
}
.category .cate-item dt {
    border-bottom-color: #E7E7E7;
}
.category .name .error {
    color: #B94A48;
}
.category .name .success {
    color: #468847;
}
.close {
    color: #000000;
    text-shadow: 0 1px 0 #ffffff;
    opacity: 0.2;
    filter: alpha(opacity=20);
}
/********************************************
 * form.css
 * 描述：定义表单样式
 * 作用范围: 公共，表单模块，表单布局
 *********************************************/

/* 表单组件
------------------------------------------ */
.text, .textarea {
    border-color: #eeeeee;
    background-color: #fff;
}
select {
    border-color: #ccc;
}
.must {
    color: #f00; /* 表单必填项标识字符*/
}
/* 多行两列表单
------------------------------------------ */
.form-horizontal .item-label .check-tips {
    color: #aaa; /* 表单label后面的提示文字 */
}
.form-horizontal .controls .check-tips {
    color: #999; /*表单ajax操作完成后返回的提示文字*/
}
/* 搜索表单
------------------------------------------ */
.search-form .sleft { /* 高级搜索左边的搜索框 */

}
/* 状态搜索下拉选项 */
.search-form .drop-down ul {
    background-color: #fff;
    border-color: #ebebeb;
}
.search-form .drop-down ul li {
    border-top-color: #ebebeb;
}
.search-form .drop-down ul a {
    color: #404040;
}
.search-form .drop-down ul a:hover {
    background-color: #f0f0f0;
}
.search-form .sch-btn:hover {
    /* 搜索提交按钮 */
    color: #fff;
}
.focus {
    /*border: 1px solid #d1d1d1;
    box-shadow: 0 0 12px #ECECEC;*/
    border-color: #A6E1F3;
    box-shadow: 0 0 12px #BAF7FF;
}
/*********************************************
 * table.css
 * 描述：定义数据表格样式
 * 作用范围: 公共，表格模块
 **********************************************/

/* 内容区 行默认颜色*/
.data-table tbody tr {
    background-color: #ffffff;
    color: #686868;
}
.data-table thead th,
.data-table tbody td {
    /*非隔行变色时 用于行区分的单元格底线颜色 #f4f4f4 */
    border-bottom-color: #f0f0f0;
}
/* 表头配色 */
.data-table thead th {
    color: #646464;
	font-size: 15px;
	font-weight: 700;
    background-color: #fff;
}
/* 隔行变色 行默认颜色 */
.table-striped tbody tr {
    background-color: #fefefe;
    color: #686868;
}
/* 隔行变色 偶数行配色 #EFF2F5 
	nth-child(even)
*/
.table-striped tbody tr:nth-child(odd) {
    background-color: #f9f9f9;

}
.table-striped tbody tr:hover {
    background-color: #fffbef;

}
/*************************************
 * button.css
 * 描述：定义按钮样式
 * 作用范围: 公共，按钮组，单独按钮
 *************************************/

/* 普通按钮 */
.btn {
    color: #fff;
    background-color: #32cc98;
}
.btn:hover {
    color: #fff;
    background-color: #37CEB0;
}
/* 表单返回按钮 */
.btn-return {
    color: #fff;
}
/* 表单提交按钮(确定,保存,提交) */
.submit-btn {
    color: #fff;
    background-color: #464b80;
}
.submit-btn:hover {
    color: #fff;
    background-color: #535996;
}
/* 下拉弹出按钮 */
.btn-group .dropdown,
.btn-group-click .dropdown {
    border-color: #ccc;
    background-color: #fff;
}
.btn-group .dropdown a:hover,
.btn-group-click .dropdown a:hover {
    background-color: #eee;
}
/* 被禁用的按钮 */
.btn.disabled {
    background-color: #8d8d8d;
    cursor: not-allowed;
}
/*************************************
 * style.css
 *************************************/

/* 头部Logo,主导航*/
.header {
    background-color: #1487e6;
}
/* 主导航 */
.main-nav a {
    color: #FFF;
}
.main-nav a:hover {
    background-color: #0a6ec8;
}
.main-nav .current {
    background-color: #656565;
}
.main-nav .current a {
    background-color: #0a6ec8;
}
/* 顶部右侧 用户bar */
.header .user-menu {
    border-color: #ddd;
    background-color: #fff;
}
.header .user-menu li {
    border-bottom-color: #ddd;
}
.header .user-menu a:hover {
    color: #424242;
    background-color: #f5f5f5;
}
/* 边栏导航样式
------------------------------------------ */
.sidebar {
    /* 左侧栏背景 */
    /*background: url("../images/bg.png") repeat #6b6b6b;*/
	background-color: #21202a;
}
/* 子导航Group标题栏 */
.subnav h3 {
    color: #fff;
}
.subnav h3 a {
    color: #fff;
}
/* 左侧二级菜单 */
.side-sub-menu > li > .item {
    color: #f1f1f1;
}
.side-sub-menu > li > .item:hover {
    color: #ffffff;
    background: #7c7c7c;
}
.side-sub-menu > li > .item:hover {
    color: #414141;
    background: url(../images/subnav_current.png) no-repeat 165px 12px #f6f6f6;

}
.side-sub-menu > .current > .item,
.side-sub-menu > .hover > .item {
    color: #414141;
}
.side-sub-menu > .current > .item,
.side-sub-menu > .current > .item:hover {
    /* 高亮导航 */
    color: #414141;
    background: url(../images/subnav_current.png) no-repeat 165px 12px #f6f6f6;
}
/* windows开始菜单式子菜单 */
.side-sub-menu li .subitem {
    border-color: #ccc;
    background-color: #fff;
}
.side-sub-menu .subitem .item:hover {
    background: url(../images/subnav_current.png) no-repeat 130px 12px #f5f5f5;
}
/* 后台主内容区域
------------------------------------------------- */
.main {
}
.main-title h2 {
    color: #445566;
}
.main-title .ca {
    /* 内容列表 面包屑 */
    background: url(../images/subnav_current.png) no-repeat center center;
}
/* 访问授权页面 大类区块 */
.checkmod {
    border-color: #ebebeb;
}
.checkmod dt {
    border-bottom-color: #ebebeb;
    background-color: #ECECEC;
}
/* 版权信息 */
.cont-ft {
    background-color: #f6f6f6;
}
.copyright {
    border-top-color: #ccc;
}
/*************************************
 *  登陆页面css
 ***********************************/

/* body 配色*/
#login-page {
    color: #000;
    background-color: #1667b4;
}
#login-page .login-form {
    background-color: #fff;
    box-shadow: 0 0 26px #041A36;
    border-top-color: #2979b4;
}
#login-page .login-form h3 {
    color: #8D9CAA;
}
#login-page .login-form .check-tips {
    color: #ff0000;
}
#login-page .login-form .item {
    border-color: #ececec;
}
#login-page .login-form .focus {
    border-color: #A6E1F3;
    box-shadow: 0 0 12px #BAF7FF;
}
#login-page .login-form .icon-login-user,
#login-page .login-form .icon-login-pwd,
#login-page .login-form .icon-login-verifycode {
    background: url("../images/icon24_login.png") no-repeat;
}
#login-page .login-form .icon-login-user {
    background-position: 0 0;
}
#login-page .login-form .icon-login-pwd {
    background-position: -48px 0;
}
#login-page .login-form .icon-login-verifycode {
    background-position: -24px 0;
}
#login-page .login-form input {
    background-color: #fff;
    color: #686868;
}
#login-page .login-form .login-btn {
    color: #FFFFFF;
    background-color: #2979b4;
}
#login-page .login-form .login-btn:hover {
    background-color: #2e68c3;
}
#login-page .login-form .login-btn[disabled] {
    opacity: 0.6;
    filter: alpha(opacity=60);
    cursor: default;
    box-shadow: none;
}
#login-page .icon-loading {
    background: url(../images/loading_icon.gif) no-repeat 0 0;
}
#login-page input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px white inset;
}
#login-page .welcome {
    color: #ddd;
}
#login-page .reloadverify {
    color: #69C3FF;
}
#login-page img.verifyimg {
    border-color: #ececec;
}
