<include file="Public:header"/>
<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>{:L('代理中心')}</title>
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/vendor/bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/base.css" media="all">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/common.css" media="all">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/module.css">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/style.css" media="all">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/default_color.css" media="all">
	<script type="text/javascript" src="__PUBLIC__/Admin/js/jquery.min.js"></script>
	<script type="text/javascript" src="__PUBLIC__/layer/layer.js"></script>
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/flat-ui.css">
	<script src="__PUBLIC__/Admin/js/flat-ui.min.js"></script>
	<script src="__PUBLIC__/Admin/js/application.js"></script>
</head>
<body style="margin:0px;padding:0px; margin-top:100px;">
<div class="navbar navbar-inverse navbar-fixed-top" role="navigation">
	<div class="navbar-header" style="background-color:#3c434d;">
		<a class="navbar-brand" style="width:200px;text-align:center;background-color:#3c434d;" href="{:U('Agent/Index/index')}">
		    <span>{:L('代理系统')}</span>	
		</a>
	</div>
	<div class="navbar-collapse collapse">
		<ul class="nav navbar-nav">
			<li> 
			    <a href="{:U('Agent/Index/index')}">{:L('会员列表')}</a>
			</li>
			
			<li> 
				<a href="{:U('Agent/Index/jclist')}">{:L('合约建仓订单')}</a>
			</li>
			
			<li>
			    <a href="{:U('Agent/Index/pclist')}">{:L('合约平仓订单')}</a>
			</li>
				<li>
			     <a href="{:U('Agent/Index/online')}">{:L('在线客服')}</a>
			</li>
				<li>
			    <a href="{:U('Agent/Index/log')}">{:L('新币认购记录')}</a>
			</li>
				<li class="active">
			    <a href="{:U('Agent/Index/myzr')}">{:L('充值记录')}</a>
			</li>
				<li>
			    <a href="{:U('Agent/Index/myzc')}">{:L('提现记录')}</a>
			</li>
		</ul>
		
		<ul class="nav navbar-nav navbar-rights" style="margin-right:10px;">
		    	<li> <a href="{:U('?Lang=zh-cn')}">简体中文</a>	</li>
			<li> <a href="{:U('?Lang=en-us')}">English</a>	</li>
			<li>
				<a class="dropdown-toggle" title="{:L('退出')}" href="{:U('Agent/Login/loginout')}" target="_blank">
					<span class="glyphicon glyphicon-share" aria-hidden="true"></span>
				</a>
			</li>
		</ul>
	</div>

</div>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">充币列表</span>
			<div class="fr">
			    <button class="btn btn-warning" onClick="location.href='{:U('Finance/myzr')}'">初始化搜索</button>
		    </div>
		</div>
		
		<div class="cf">

			<div class="search-form fl cf" style="float: none !important;">
				<div class="sleft">
					<form name="formSearch" id="formSearch" method="get" name="form1">
						<select style=" width: 100px; float: left; margin-right: 10px;" name="field" class="form-control">
							<option value="username" <eq name="Think.get.field" value="username">selected</eq>>用户名</option>
						</select>
							<select style=" width: 100px; float: left; margin-right: 10px;" name="atype" id="atype" class="form-control">
							<option value="0" <eq name="Think.get.atype" value="0">selected</eq>>用户充值</option>
							<option value="1" <eq name="Think.get.atype" value="1">selected</eq>>管理员充值</option>
						</select>
						           								    
<label for="statrtime"  style=" width: 250px; float: left; margin-right: 10px;" >开始时间：<input id="statrtime" name="statrtime" type="date" value="<?php if($_GET['statrtime']){echo $_GET['statrtime'];}else{ ?>date<?php } ?>"/></label>
<label for="endtime"  style=" width: 250px; float: left; margin-right: 10px;" >结束时间：<input id="endtime" name="endtime" type="date" value="<?php if($_GET['endtime']){echo $_GET['endtime'];}else{ ?>date<?php } ?>"/></label>
						<input type="text" name="name" class="search-input form-control  " value="{$Think.get.name}" placeholder="请输入查询内容" style="">
						<a class="sch-btn" href="javascript:;" id="search"> <i class="btn-search"></i> </a>
					</form>
					<script>
						//搜索功能
						$(function () {
							$('#search').click(function () {
								$('#formSearch').submit();
							});
						});
						//回车搜索
						$(".search-input").keyup(function (e) {
							if (e.keyCode === 13) {
								$("#search").click();
								return false;
							}
						});
					</script>
				</div>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
				<tr>
					<th class="">ID</th>
					<th class="">用户名</th>
					<th class="">充值币种</th>
					<th class="">充值网络</th>
					<th width="">充值时间</th>
					<th width="">处理时间</th>
					<th width="">充值数量</th>
					<th width="">实际到账</th>
					<th width="">转账凭证</th>
					<th width="">状态</th>
					<th width="">操作</th>
				</tr>
				</thead>
				<tbody>
				<notempty name="list">
					<volist name="list" id="vo">
						<tr>
							<td>{$vo.id}</td>
							<td>{$vo.username}</td>
							<td><?php echo strtoupper($vo['coin']);?></td>
							<td><?php echo strtoupper($vo['czline']);?><br />{$vo.address} </td>
							<td>{$vo.addtime}</td>
							<td>{$vo.updatetime}</td>
							<td>{$vo['num']*1}</td>
							<td>{$vo['num']*1}</td>
							<td>
							    <img src="/Public/Static/payimgs/{$vo.payimg}" style="height:60px;"/>
							</td>
							
							<td>
								<eq name="vo.status" value="1">
								    <span style="color:blue;">等待审核</span>
								</eq>
								<eq name="vo.status" value="2">
								    <span style="color:green;">审核通过</span>
								</eq>
								<eq name="vo.status" value="3">
								    <span style="color:red;">驳回充值</span>
								</eq>
								</br>
							    	<eq name="vo.atype" value="1">
								    <span style="color:blue;">管理员充值</span>
								</eq>
									<eq name="vo.atype" value="0">
								    <span style="color:green;">用户充值</span>
								</eq>
							</td>
							<td>
                                <eq name="vo.status" value="2"><span style="color:blue;">已处理</span></eq>
                                <eq name="vo.status" value="3"><span style="color:blue;">已处理</span></eq>
                            </td>
						</tr>
					</volist>
					<else/>
					<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
				</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<script type="text/javascript">
    function Upbhzr(id) {
        var zcid = parseInt(id);
        if (zcid == "" || zcid == null || zcid <=0) {
            layer.alert('参数错误！');
            return false;
        }
        layer.load(0, {shade: [0.5,'#8F8F8F']});
        $.post("{:U('Finance/rejectzr')}", {
            id: zcid
        }, function (data) {
            setTimeout("closetanchu()",2000);
            if (data.status == 1) {
                layer.msg(data.info, {
                    icon: 1
                });
                setTimeout("shuaxin()",1000);
            } else {
                layer.msg(data.info, {
                    icon: 2
                });
            }
        }, "json");
    }
</script>
<script type="text/javascript">
    function Upzr(id) {
        var zcid = parseInt(id);
        if (zcid == "" || zcid == null || zcid <=0) {
            layer.alert('参数错误！');
            return false;
        }
        layer.load(0, {shade: [0.5,'#8F8F8F']});
        $.post("{:U('Finance/adoptzr')}", {
            id: zcid
        }, function (data) {
            setTimeout("closetanchu()",2000);
            if (data.status == 1) {
                layer.msg(data.info, {
                    icon: 1
                });
                setTimeout("shuaxin()",1000);
            } else {
                layer.msg(data.info, {
                    icon: 2
                });
            }
        }, "json");
    }
</script>
<script type="text/javascript">
    function closetanchu(){
        layer.closeAll('loading');
    }
    function shuaxin(){
        window.location.href=window.location.href;
    }
</script>
</script>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Finance/myzr')}");
	</script>
</block>