<?php
namespace Agent\Controller;
class LoginController extends \Think\Controller {
  public function index(){
    if(IS_POST){
      $username = I('post.username','','trim');
      $password = I('post.password','','trim');
      $vcode    = I('post.verify','','trim');

      // 校验图形验证码（Agent 模块验证码ID为 'agent'），并兼容 Home 自定义验证码
      $codeInput = strtoupper($vcode);
      $v = new \Think\Verify();
      $ok = $v->check($codeInput, 'agent');
      if(!$ok){
        $sessionCode = session('verify_code');
        if(!empty($sessionCode) && $codeInput === $sessionCode){
          $ok = true;
          session('verify_code', null);
        }
      }
      if(!$ok){
        $this->ajaxReturn(['status'=>0,'info'=>'验证码错误']);
      }

      $agent = M('Agent')->where(['username'=>$username])->find();
      if(!$agent || $agent['password'] != md5($password)){
        $this->ajaxReturn(['status'=>0,'info'=>'账号或密码错误']);
      }
      session('agent_id',$agent['id']);
      session('agent_username',$agent['username']);
      $this->ajaxReturn(['status'=>1,'info'=>'登录成功','url'=>U('Index/index')]);
    }else{
      $this->display();
    }
  }
}
