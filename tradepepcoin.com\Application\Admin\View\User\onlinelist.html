<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">在线客服</span>
		</div>

		<div class="data-table table-striped">
			<form id="form"  method="post" class="form-horizontal">
				<table class="">
					<thead>
					<tr>
						<th class="row-selected row-selected">
							<input class="check-all" type="checkbox"/>
						</th>
						<th class="">ID</th>
						<th class="">会员账号</th>
						<th class="">内容</th>
						<th class="">操作</th>
					</tr>
					</thead>
					<tbody>
					<notempty name="list">
						<volist name="list" id="vo">
							<tr>
								<td>
									<input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/>
								</td>
								<td>{$vo.id}</td>
								<td>{$vo.username}</td>
								<td>{$vo.content}</td>
                                <td>
						            <a class="btn btn-primary btn-xs" href="{:U('User/sendonline')}?id={$vo.id}">回复</a>
						        </td>
							</tr>
						</volist>
						<else/>
						<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
					</notempty>
					</tbody>
				</table>
			</form>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
     
	</div>
   
	
</div>

<script type="text/javascript">
	//提交表单
	$('#submit').click(function () {
		$('#form').submit();
	});
</script>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('User/online')}");
	</script>
</block>
