<?php
namespace Home\Controller;

class VerifyController extends HomeController
{
	protected function _initialize()
	{
		parent::_initialize();
		$allow_action = array("code","findpaypwd");
		if(!in_array(ACTION_NAME,$allow_action)){
			$this->error("非法操作！");
		}
	}

    //验证码
	public function code()
	{
		// 设置响应头
		header('Content-Type: image/png');
		header('Cache-Control: no-cache, must-revalidate');
		header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
		
		// 清除输出缓冲
		if (function_exists('ob_clean')) {
			ob_clean();
		}
		
		// 生成验证码
		$code = '';
		for ($i = 0; $i < 4; $i++) {
			$code .= rand(1, 9);
		}
		
		// 保存到session
		session('verify_code', strtoupper($code));
		
		// 创建图片
		$width = 120;
		$height = 40;
		$image = imagecreate($width, $height);
		
		// 设置颜色
		$bg_color = imagecolorallocate($image, 255, 255, 255);
		$text_color = imagecolorallocate($image, 0, 0, 0);
		$noise_color = imagecolorallocate($image, 200, 200, 200);
		
		// 填充背景
		imagefill($image, 0, 0, $bg_color);
		
		// 添加干扰线
		for ($i = 0; $i < 3; $i++) {
			imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $noise_color);
		}
		
		// 添加干扰点
		for ($i = 0; $i < 50; $i++) {
			imagesetpixel($image, rand(0, $width), rand(0, $height), $noise_color);
		}
		
		// 写入验证码文字
		imagestring($image, 5, 30, 10, $code, $text_color);
		
		// 输出图片
		imagepng($image);
		imagedestroy($image);
		exit;
	}

    public function findpaypwd(){ 
		$this->ajaxReturn(array("status"=>0,"info"=>L("功能暂未开通"))); 
	}
}
?>