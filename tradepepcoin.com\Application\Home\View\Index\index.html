<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球领先的数字资产交易平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/modern-homepage.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/mobile-optimized.css" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/Public/Home/static/css/modern-homepage.css" as="style">
    <link rel="preload" href="/Public/Home/static/js/modern-homepage-safe.js" as="script">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- SEO Meta Tags -->
    <meta name="description" content="全球领先的数字资产交易平台，提供安全、便捷的数字货币交易服务">
    <meta name="keywords" content="数字货币,比特币,以太坊,交易所,区块链,加密货币">
    <meta name="author" content="数字资产交易平台">
    <meta name="robots" content="index,follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="全球领先的数字资产交易平台">
    <meta property="og:description" content="安全、便捷的数字货币交易服务，支持比特币、以太坊等主流数字资产交易">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="/Upload/public/logo.png">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="全球领先的数字资产交易平台">
    <meta name="twitter:description" content="安全、便捷的数字货币交易服务">
    <meta name="twitter:image" content="/Upload/public/logo.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/Upload/public/logo.png">
    <link rel="apple-touch-icon" href="/Upload/public/logo.png">

    <!-- Theme Color -->
    <meta name="theme-color" content="#f0b90b">
    <meta name="msapplication-TileColor" content="#f0b90b">

    <!-- Security -->
    <meta name="csrf-token" content="">
    
    <script src="/Public/Home/js/pako.min.js"></script>
    <script>if(typeof window.pako==='undefined'){document.write('<script src="https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js"></'+'script>');}</script>

<style>
/* 首页右侧注册按钮与市场操作按钮，强制显示文字且可点 */
.register-form button[type="submit"]{
    color:#fff!important;
    text-indent:0!important;
    font-weight:600!important;
    visibility:visible!important;
    opacity:1!important;
}
.action-buttons a.btn{
    color:#0d6efd!important;
    pointer-events:auto!important;
    text-indent:0!important;
    visibility:visible!important;
    opacity:1!important;
    display:inline-block!important;
}
/* 兜底：任何按钮文字都可见 */
.btn,a.btn{
    visibility:visible!important;
    opacity:1!important;
    text-indent:0!important;
    letter-spacing:normal!important;
    font-size:inherit!important;
    line-height:normal!important;
}
/* 确保按钮内的文字和图标都可见 */
.btn *,.btn::before,.btn::after,a.btn *,a.btn::before,a.btn::after{
    visibility:visible!important;
    opacity:1!important;
    text-indent:0!important;
}
/* 火币导航栏按钮样式强化 */
.huobi-btn{
    visibility:visible!important;
    opacity:1!important;
    text-indent:0!important;
    display:inline-block!important;
}
</style>

</head>

<body>
    <div class="App">
        <!-- Header Include -->
        <header>
            <!-- 这里应该包含您的header模板 -->
 


<style>
.btn-outline-primary {
    color: #007bff !important;
    border-color: #007bff !important;
    background: transparent !important;
    text-decoration: none !important;
}
.btn-outline-primary:hover {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
}
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
    border: 1px solid;
}
</style>
<style>
/* 修复按钮样式 */
.btn-outline-primary {
    color: #007bff !important;
    border-color: #007bff !important;
    background: transparent !important;
    text-decoration: none !important;
}
.btn-outline-primary:hover {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
}
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
    border: 1px solid;
}
.action-buttons {
    display: flex;
    gap: 5px;
}
</style>
<include file="Public:header"/>

        <main>
            <!-- Notice Section -->
            <section class="notice-section">
                <div class="container">
                    <div class="notice-container">
                        <div class="notice-label">
                            <i class="bi bi-megaphone"></i> 公告
                        </div>
                        <div class="notice-content">
                            <div class="notice-marquee" id="noticeMarquee">
                                欢迎来到数字资产交易平台，全球领先的数字资产交易平台
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Hero Section -->
            <section class="hero-section">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="hero-content">
                                <h1 class="hero-title">
                                    发现您的下一个投资机会
                                </h1>
                                <p class="hero-subtitle">
                                    全球领先的数字资产交易平台，为您提供安全、便捷的交易体验
                                </p>
                                <div class="hero-stats">
                                    <div class="stat-item">
                                        <span class="stat-value">$289,789,518</span>
                                        <span class="stat-label">24小时交易量</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">200+</span>
                                        <span class="stat-label">交易对</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">1M+</span>
                                        <span class="stat-label">用户信赖</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="register-form">
                                <h3>立即开始交易</h3>
                                <form action="/index.php?s=/Login/register" method="get">
                                    <div class="mb-3">
                                        <input type="email" class="form-control" name="email" placeholder="请输入邮箱或手机" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-person-plus"></i> 立即注册
                                    </button>
                                </form>
                                <div class="text-center mt-3">
                                    <small class="text-muted">
                                        已有账户？<a href="/index.php?s=/Login/index" class="text-decoration-none">立即登录</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Market Section -->
            <section class="market-section">
                <div class="container">
                    <h2 class="section-title">热门交易对</h2>
                    <div class="market-table">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>价格</th>
                                        <th>涨跌幅</th>
                                        <th>最高/最低</th>
                                        <th>24H量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态显示ctmarket表中的币种 -->
                                    <volist name="market" id="vo">
                                    <tr class="market-row" data-coin="{$vo.coinname}">
                                        <td>
                                            <div class="coin-info">
                                                <?php
                                                    // 改进的图标加载逻辑
                                                    $logo = $vo['logo'] ? $vo['logo'] : $vo['coinname'];
                                                    
                                                    // 确保图标有正确的扩展名
                                                    if (!preg_match('/\.(png|jpg|jpeg|gif|svg)$/i', $logo)) { 
                                                        $logo .= '.png'; 
                                                    }
                                    
                                                    // 尝试多个可能的路径
                                                    $possible_paths = array(
                                                        '/xm/' . $logo,
                                                        '/Public/Home/static/imgs/coins/' . $logo,
                                                        '/Public/Home/static/imgs/' . $logo,
                                                        '/Upload/coins/' . $logo
                                                    );
                                    
                                                    $final_path = '/Public/Home/static/imgs/empty-dark.png'; // 默认备用图标
                                    
                                                    foreach ($possible_paths as $path) {
                                                        $abs_path = $_SERVER['DOCUMENT_ROOT'] . $path;
                                                        if (is_file($abs_path)) {
                                                            $final_path = $path;
                                                            break;
                                                        }
                                                    }
                                                ?>
                                                <img src="<?php echo $final_path; ?>" class="coin-logo" alt="{$vo.coinname|strtoupper}" onerror="this.src='/Public/Home/static/imgs/empty-dark.png'; this.onerror=null;">
                                                <div>
                                                    <div class="coin-name">{$vo.coinname|strtoupper}</div>
                                                    <div class="coin-symbol">{$vo.coinname|strtoupper}/USDT</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="coin-price">
                                            <span class="loading"></span>
                                        </td>
                                        <td class="coin-change">
                                            <span class="change-badge">0.00%</span>
                                        </td>
                                        <td class="coin-highlow">--.--/--.--</td>
                                        <td class="coin-volume">--.--</td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="/index.php?s=/Trade/index&symbol={$vo.coinname|strtoupper}" class="huobi-trade-btn">交易</a>
                                            </div>
                                        </td>
                                    </tr>
                                                                        </if>
                                    </volist>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="features-section">
                <div class="container">
                    <h2 class="section-title">为什么选择我们</h2>
                    <div class="row g-4">
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-shield-check"></i>
                                </div>
                                <h3 class="feature-title">安全储存</h3>
                                <p class="feature-description">
                                    客户资金存放在专用的多签名冷钱包中，24/7全天候安全监控，专用20,000 BTC安全储备金
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-credit-card"></i>
                                </div>
                                <h3 class="feature-title">信用卡付款</h3>
                                <p class="feature-description">
                                    通过我们的合作伙伴用您的信用卡购买加密货币，支持多种法币充值方式
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <h3 class="feature-title">管理您的资产</h3>
                                <p class="feature-description">
                                    以高达5倍的杠杆率进行现货交易，专业的交易工具助您把握每一个投资机会
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-phone"></i>
                                </div>
                                <h3 class="feature-title">随处访问</h3>
                                <p class="feature-description">
                                    在我们用于Android和iOS的移动应用上进行24/7全天候存款，取款和交易
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-lightning"></i>
                                </div>
                                <h3 class="feature-title">高性能交易</h3>
                                <p class="feature-description">
                                    毫秒级撮合引擎，支持每秒百万级交易处理，为您提供极速交易体验
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-headset"></i>
                                </div>
                                <h3 class="feature-title">24/7客服支持</h3>
                                <p class="feature-description">
                                    专业客服团队全天候在线，多语言支持，随时为您解答疑问
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- About Section -->
            <section class="py-5 bg-light">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto text-center">
                            <h2 class="section-title">关于我们</h2>
                            <div class="text-muted">
                                <p>
                                    GVD数字货币交易平台（冠军宇宙全球数字货币交易平台）是目前唯一真正中心化的冠军宇宙项目。
                                    它是一个全球性的综合性交易平台，提供一站式交易、期货合约、融资融券、轻松外交、全网交易、
                                    去中心化交易等系统功能，满足各类投资者的交易需求。
                                </p>
                                <p>
                                    它力求将元宇宙概念与全球币本位经济相融合，有效对接全球各大交易所的市场深度，
                                    并提供更高的流动性。元宇宙代币将作为交易媒介，引领人们探索和理解元宇宙。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer Include -->
        <footer>
            <!-- 这里应该包含您的footer模板 -->
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Modern Homepage JS - 安全简化版 -->
    <script src="/Public/Home/static/js/modern-homepage-safe.js"></script>
    <!-- 币种图标修复脚本 -->
    <script src="/Public/Home/static/js/coin-icons.js"></script>
    <!-- 强制保障导航跳转兜底（捕获阶段执行） -->
    <script src="/Public/Home/static/js/force-nav.js"></script>

    <script>
        $(document).ready(function() {
            // 加载公告信息
            loadNotices();

            // 加载市场数据
            loadMarketData();

            // 每30秒更新一次市场数据
            setInterval(loadMarketData, 30000);
        });

        function loadNotices() {
            $.post("/ajax/getNotices", function(data) {
                if (data.code == 1 && data.notices && data.notices.length > 0) {
                    let noticeText = data.notices.map(notice => notice.title).join(' | ');
                    $('#noticeMarquee').text(noticeText);
                }
            }, 'json').fail(function() {
                // 如果AJAX失败，显示默认公告
                $('#noticeMarquee').text('欢迎来到数字资产交易平台，全球领先的数字资产交易平台');
            });
        }

        function loadMarketData() {
            // 加载各个币种的市场数据
            $('.market-row').each(function() {
                let coin = $(this).data('coin');
                let row = $(this);

                // 根据币种加载对应的数据
                if (coin) {
                    // 构建API URL
                    let apiUrl = "/ajaxtrade/obtain_" + coin.toLowerCase();
                    loadCoinData(coin, row, apiUrl);
                } else {
                    // 对于没有coin数据的行，显示默认数据
                    row.find('.coin-price').html('<span class="text-muted">--</span>');
                    row.find('.coin-change').html('<span class="change-badge">0.00%</span>');
                    row.find('.coin-highlow').text('--.--/--.--');
                    row.find('.coin-volume').text('--.--');
                }
            });
        }

        function loadCoinData(coin, row, url) {
            $.post(url, {'coin': coin}, function(data) {
                if (data.code == 1) {
                    // 更新价格
                    row.find('.coin-price').html('<span class="price-positive">$' + data.open + '</span>');

                    // 更新涨跌幅
                    let changeClass = data.change.includes('+') ? 'change-positive' : 'change-negative';
                    row.find('.coin-change').html('<span class="change-badge ' + changeClass + '">' + data.change + '</span>');

                    // 更新最高最低价
                    row.find('.coin-highlow').text(data.highlow);

                    // 更新交易量
                    row.find('.coin-volume').text(data.amount);
                }
            }).fail(function() {
                // 如果请求失败，显示默认数据
                row.find('.coin-price').html('<span class="text-muted">--</span>');
                row.find('.coin-change').html('<span class="change-badge">0.00%</span>');
                row.find('.coin-highlow').text('--.--/--.--');
                row.find('.coin-volume').text('--.--');
            });
        }
    </script>

    <!-- 强制保障导航跳转兜底 -->
    <script>
    (function(){
      document.addEventListener("click",function(e){
        var a = e.target.closest && e.target.closest("a");
        if(!a) return;
        var h = a.getAttribute("href")||"";
        if(!h || h=="#" || /^javascript:/i.test(h) || h.indexOf("/index.php") !== -1) return;

        // 修复相对路径链接
        if(/^\/(trade|issue|contract|finance|user|login)/i.test(h)){
          e.preventDefault();
          window.location.href = "/index.php?s=" + h;
        }
      }, true);
    })();
    </script>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FinancialService",
        "name": "数字资产交易平台",
        "description": "全球领先的数字资产交易平台",
        "url": "",
        "logo": "/Upload/public/logo.png",
        "sameAs": [
            "https://twitter.com/yourhandle",
            "https://facebook.com/yourpage"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Chinese", "English"]
        },
        "offers": {
            "@type": "Offer",
            "description": "数字货币交易服务",
            "category": "Financial Services"
        }
    }
    </script>

    <!-- Performance Monitoring -->
    <script>
        // Critical performance metrics
        window.addEventListener('load', function() {
            if ('performance' in window) {
                setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page Load Time:', perfData.loadEventEnd - perfData.fetchStart, 'ms');
                }, 0);
            }
        });

        // Error tracking
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
        });

        // Unhandled promise rejection tracking
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    </script>
</body>
</html>