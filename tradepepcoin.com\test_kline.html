<!DOCTYPE html>
<html>
<head>
    <title>K线图测试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #1a1a1a; color: #fff; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin-bottom: 30px; padding: 20px; background-color: #2c2c2c; border-radius: 8px; }
        .chart-container { width: 100%; height: 400px; background-color: #1a1a1a; border: 1px solid #444; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>K线图功能测试</h1>
        
        <div class="test-section">
            <h3>1. 测试数据API</h3>
            <button onclick="testDataAPI()">测试数据获取</button>
            <div id="api-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试K线图初始化</h3>
            <button onclick="testChartInit()">初始化K线图</button>
            <button onclick="clearChart()">清除图表</button>
            <div id="chart-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. K线图显示区域</h3>
            <div id="chart_container" class="chart-container"></div>
        </div>
    </div>

    <!-- 加载必要的JavaScript文件 -->
    <script src="/Public/Home/js/jquery-1.8.3.min.js"></script>
    <script src="/Public/Static/js/pako.min.js"></script>
    <script src="/Public/Static/js/klinecharts.min.js"></script>
    <script src="/Public/Static/js/ws-deedfeeds.js"></script>

    <script>
        // 设置全局变量
        window.SYMBOL = "btcusdt";
        
        function showResult(elementId, message, type) {
            var resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = '<div class="' + type + '">' + message + '</div>';
        }
        
        function testDataAPI() {
            showResult('api-result', '正在测试数据API...', 'info');
            
            fetch('/Home/Chart/getMarketSpecialtyJsonAitc.html?market=btcusdt&step=300')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && Array.isArray(data) && data.length > 0) {
                        showResult('api-result', 'API测试成功！获取到 ' + data.length + ' 条数据', 'success');
                        console.log('API数据:', data);
                    } else {
                        showResult('api-result', 'API返回空数据', 'error');
                    }
                })
                .catch(error => {
                    showResult('api-result', 'API测试失败：' + error.message, 'error');
                    console.error('API错误:', error);
                });
        }
        
        function testChartInit() {
            showResult('chart-result', '正在初始化K线图...', 'info');
            
            try {
                // 检查必要的库是否加载
                if (typeof klinecharts === 'undefined') {
                    showResult('chart-result', 'klinecharts库未加载', 'error');
                    return;
                }
                
                // 初始化图表
                const chart = klinecharts.init('chart_container');
                
                // 生成测试数据
                const testData = [];
                const currentTime = Date.now();
                for (let i = 0; i < 50; i++) {
                    const basePrice = 50000 + Math.random() * 2000 - 1000;
                    testData.push({
                        timestamp: currentTime - (i * 300 * 1000), // 5分钟间隔
                        open: basePrice,
                        high: basePrice + Math.random() * 100,
                        low: basePrice - Math.random() * 100,
                        close: basePrice + Math.random() * 200 - 100,
                        volume: Math.random() * 1000 + 100
                    });
                }
                
                // 应用数据
                chart.applyNewData(testData);
                
                // 创建技术指标
                chart.createTechnicalIndicator('MA', false, { id: 'candle_pane' });
                chart.createTechnicalIndicator('VOL');
                
                showResult('chart-result', 'K线图初始化成功！', 'success');
                
            } catch (error) {
                showResult('chart-result', 'K线图初始化失败：' + error.message, 'error');
                console.error('图表错误:', error);
            }
        }
        
        function clearChart() {
            const container = document.getElementById('chart_container');
            container.innerHTML = '';
            showResult('chart-result', '图表已清除', 'info');
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            console.log('页面加载完成，开始测试...');
            testDataAPI();
        });
    </script>
</body>
</html> 