html._modal {
    overflow: hidden;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    width: 100%;
    height: 100%;
    visibility: hidden;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: 10px;
    transition: 0.6s linear;
}

.modal._active {
    visibility: visible;
}

.modal._active .modal-content {
    opacity: 1;
    transform: scale(1);
}

.modal-body {
    min-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #f00;
}

.modal-content {
    position: relative;
    max-width: 60em;
    padding: 3em 2em;
    margin: 3em 0;
    background-color: #fff;
    border-radius: 1em;
    opacity: 0;
    transform: scale(0.5);
    transition: 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-close {
    position: absolute;
    top: 0.2em;
    right: 0.5em;
    font-size: 2em;
    cursor: pointer;
    transition: 0.2s linear;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    visibility: hidden;
    transition: 0.4s ease-in-out;
}

.modal-overlay._active {
    background-color: rgba(0, 0, 0, 0.6);
    visibility: visible;
}
