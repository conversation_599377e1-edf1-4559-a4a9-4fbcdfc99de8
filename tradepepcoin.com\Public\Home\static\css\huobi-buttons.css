/* 火币风格按钮样式 */
.huobi-trade-btn {
    background: linear-gradient(135deg, #F0B90B 0%, #E6A800 100%);
    color: #0B1426;
    border: none;
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(240, 185, 11, 0.2);
}

.huobi-trade-btn:hover {
    background: linear-gradient(135deg, #E6A800 0%, #D49700 100%);
    color: #0B1426;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(240, 185, 11, 0.3);
}

.huobi-detail-btn {
    background: transparent;
    color: #F0B90B;
    border: 1px solid #F0B90B;
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
}

.huobi-detail-btn:hover {
    background: #F0B90B;
    color: #0B1426;
    transform: translateY(-1px);
}

/* 确保按钮在表格中正确显示 */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
}

.market-row .action-buttons {
    min-height: 32px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .huobi-trade-btn,
    .huobi-detail-btn {
        padding: 4px 12px;
        font-size: 11px;
        min-width: 50px;
    }

    .action-buttons {
        gap: 6px;
    }
}

/* 语言切换按钮样式 */
.huobi-nav-link[data-nav="language"] {
    display: flex;
    align-items: center;
    gap: 5px;
}

.huobi-nav-link[data-nav="language"] .bi-globe {
    margin-right: 3px;
}

.lang-text {
    font-size: 14px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .lang-text {
        display: none;
    }
    
    .huobi-nav-link[data-nav="language"] {
        justify-content: center;
        min-width: 40px;
    }
}

/* 语言切换按钮悬停效果 */
.huobi-nav-link[data-nav="language"]:hover {
    color: #f0b90b;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}
