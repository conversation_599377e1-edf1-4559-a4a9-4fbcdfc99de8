<?php
namespace Agent\Controller;
class VerifyController extends \Think\Controller {
    // 为避免任何输出干扰验证码图片，这里统一走 code()
    public function index(){ $this->code(); }

    public function code(){
        // 清空输出缓冲，防止BOM或调试输出破坏图片字节流
        if (function_exists('ob_get_level')) {
            while (ob_get_level() > 0) { ob_end_clean(); }
        } else {
            @ob_clean();
        }
        // 禁止缓存，确保每次点击都刷新
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: 0');

        $config = array(
            'useNoise' => true,
            'length'   => 4,
            'codeSet'  => '1234567890'
        );
        $verify = new \Think\Verify($config);
        // 使用独立的验证ID，便于后续如需 check_verify 对应
        $verify->entry('agent');
        exit;
    }

    public function test(){
        header('Content-Type:text/plain; charset=utf-8');
        echo 'verify test ok';
        exit;
    }
}
?>
