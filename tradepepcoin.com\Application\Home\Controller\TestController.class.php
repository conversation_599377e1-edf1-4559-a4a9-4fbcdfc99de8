<?php
namespace Home\Controller;

class TestController extends HomeController
{
    protected function _initialize()
    {
        parent::_initialize();
        $allow_action = array("index", "simple", "json");
        if(!in_array(ACTION_NAME,$allow_action)){
            $this->error("非法操作！");
        }
    }

    // 简单测试页面
    public function index()
    {
        echo "✅ TestController::index() 工作正常！<br>";
        echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
        echo "PHP版本: " . PHP_VERSION . "<br>";
        echo "ThinkPHP版本: " . THINK_VERSION . "<br>";
        exit;
    }

    // 简单JSON测试
    public function json()
    {
        header('Content-Type: application/json');
        echo json_encode(array(
            'status' => 'success',
            'message' => 'TestController::json() 工作正常！',
            'time' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION
        ));
        exit;
    }

    // 简单图片测试
    public function simple()
    {
        // 检查GD库
        if (!extension_loaded('gd')) {
            header('Content-Type: text/plain');
            echo "GD库未启用";
            exit;
        }
        
        // 清除输出缓冲
        if (function_exists('ob_clean')) {
            ob_clean();
        }
        
        header('Content-Type: image/png');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        
        try {
            $image = imagecreate(100, 30);
            if (!$image) {
                throw new Exception('无法创建图片');
            }
            
            $bg_color = imagecolorallocate($image, 255, 255, 255);
            $text_color = imagecolorallocate($image, 0, 0, 0);
            
            imagestring($image, 5, 10, 5, 'TEST', $text_color);
            imagepng($image);
            imagedestroy($image);
        } catch (Exception $e) {
            header('Content-Type: text/plain');
            echo "图片生成失败: " . $e->getMessage();
        }
        exit;
    }
}
?> 