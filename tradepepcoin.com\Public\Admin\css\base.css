/** 
 * 描述：重置页面元素并基础样式
 * 作用范围： 全局
 */

html,body {
	font-family: "微软雅黑", "Microsoft YaHei";
	color:#404040;
	min-width:1200px ;
}
body,p,pre,blockquote,
h1,h2,h3,h4,h5,h6,			
dl,dt,dd,ul,ol,li,			
form,select,input,textarea,button,		
table,caption,tr,th,td {
	margin:0;
	padding:0;
}
ol,ul {
	list-style:none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
img {
	max-width: 100%;
	border:0 none;
}
a {
	color: #2d7200;
	text-decoration:none;
}
a:hover{
	color: #8f9fe2;
	border-bottom: 1px solid;
}
input,button,textarea,select,option {
	font-family:inherit;
	font-size:100%;	
	outline: 0;
}
textarea {
	overflow: auto;
	resize: none;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

/* 布局样式 */
.cf,.nav,.container-span {
	*zoom: 1;
}
.cf:before, .cf:after,
.nav:before, .nav:after,
.container-span:before,
.container-span:after {
	display: table;
	content: "";
}
.cf .msg-time,
.cf .msg-status{
	padding-left: 5%;
}
.cf .msg{
	/* background-color:#FFFFFF; */
	margin-left: 50px;
	text-align:right;
	width: 60%;
}
.cf .msg .msg-content{
	border: 1px solid #e0e0e0; 
	padding: 5px;
	text-align:left;
	border-radius: 5px;
	height: 150px;	
}
.cf:after,.nav:after,
.container-span:after {
	clear: both;
}
.fl{
	float: left!important;
}
.fr{
	float: right!important;
}
.wrapper {
	margin-left: auto;
	margin-right: auto;
	width: 1000px;
}
.hidden {
	display: none;
}
.block {
	display: block!important;
}
.fixed{
    position: fixed!important;
}
.container-span [class^="span"] {
	margin: 0 1% 20px;
}
.container-span .span1,
.container-span .span2,
.container-span .span3,
.container-span .span4 {
    float: left;
}
.container-span .span1 {
    width: 23%;
}
.container-span .span2 {
    width: 48%;
}
.container-span .span4 {
    width: 98%;
}

/* 排版样式 */
.text-center{
	text-align: center!important;
}

.text-right{
	text-align: right!important;
}

.text-left{
	text-align: left!important;
}

/* 导航列表 */
.nav li {
	float: left;
}
.nav li a,
.nav li a:hover,
.nav-list li a,
.nav-list li a:hover {
	display: block;
	text-decoration: none;
	outline: none;
	border-bottom: 0 none;
}

.ajax-get,.ajax-post{
	cursor: pointer;
}
