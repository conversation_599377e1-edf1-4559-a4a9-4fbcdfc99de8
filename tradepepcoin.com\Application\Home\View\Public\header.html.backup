<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/huobi-buttons.css" />
    .lang-btn {
        background: white !important;
        border: 1px solid #e6e6e6 !important;
        color: #666 !important;
        transition: all 0.3s ease !important;
    }
    <style>    /* 语言切换下拉菜单悬停效果 */    .lang-btn:hover {        border-color: #f0b90b !important;        color: #f0b90b !important;    }    .lang-dropdown-menu a:hover {        background-color: #f8f9fa !important;        color: #f0b90b !important;    }    @media (max-width: 768px) {        .language-dropdown {            margin-right: 10px !important;        }        .lang-btn {            padding: 6px 8px !important;            min-width: 60px !important;            font-size: 12px !important;        }        .current-lang {            display: none !important;        }        .lang-dropdown-menu {            right: -20px !important;            min-width: 140px !important;        }    }    </style>
    <title>{$Think.config.WEB_SITE_TITLE}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
    <style>
    /* 火币风格导航栏 */
    .huobi-header {
        background: #0B1426;
        border-bottom: 1px solid #1e2329;
        position: sticky;
        top: 0;
        z-index: 1000;
        padding: 0;
    }
    .huobi-nav-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 64px;
    }
    .huobi-logo {
        display: flex;
        align-items: center;
        color: #F0B90B !important;
        font-size: 24px;
        font-weight: bold;
        text-decoration: none;
    }
    .huobi-nav-menu {
        display: flex;
        align-items: center;
        gap: 32px;
        list-style: none;
        margin: 0;
        padding: 0;
    }
    .huobi-nav-item {
        position: relative;
    }
    .huobi-nav-link {
        color: #EAECEF;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        padding: 20px 0;
        display: block;
        transition: color 0.2s ease;
        border-bottom: 2px solid transparent;
    }
    .huobi-nav-link:hover {
        color: #F0B90B !important;
        border-bottom-color: #F0B90B !important;
    }
    .huobi-nav-link.active {
        color: #F0B90B !important;
        border-bottom-color: #F0B90B !important;
    }
    .huobi-user-area {
        display: flex;
        align-items: center;
        gap: 16px;
    }
    .huobi-btn {
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }
    .huobi-btn-outline, .huobi-user-area .huobi-btn-outline {
        color: #F0B90B !important;
        border-color: #F0B90B !important;
        background: white;
    }
    .huobi-btn-outline:hover {
        background: #F0B90B;
        color: #0B1426;
    }
    .huobi-btn-primary {
        background: #F0B90B;
        color: #0B1426;
    }
    .huobi-btn-primary:hover {
        background: #E6A800;
    }
    
    /* 移动端适配 */
    @media (max-width: 768px) {
        .huobi-nav-container {
            padding: 0 16px;
            height: 56px;
        }
        .huobi-nav-menu {
            gap: 20px;
        }
        .huobi-nav-link {
            font-size: 13px;
            padding: 16px 0;
        }
        .huobi-user-area {
            gap: 12px;
        }
        .huobi-btn {
            padding: 6px 12px;
            font-size: 13px;
        }
    }
    </style>
    <script src="/Public/Home/static/js/language-dropdown.js"></script>
</head>
<body>
    <header class="huobi-header">
        <div class="huobi-nav-container">
            <a href="{:U('Index/index')}" class="huobi-logo">
                <img src="/Upload/public/{:get_config('waplogo')}" alt="Logo" style="height: 32px; margin-right: 8px;" onerror="this.style.display='none'">
                {:get_config('webname')}
            </a>
            
            <nav>
                <ul class="huobi-nav-menu">
                    <li class="huobi-nav-item">
                        <a href="{:U('Index/index')}" class="huobi-nav-link" data-nav="index">首页</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Contract/index')}" class="huobi-nav-link" data-nav="contract">秒合约</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Trade/index')}" class="huobi-nav-link" data-nav="trade">币币交易</a>
                    </li>
                    <li class="huobi-nav-item">
                        <if condition="$Think.session.uid"><a href="{:U('Finance/index')}" class="huobi-nav-link" data-nav="finance">钱包</a><else/><a href="{:U('Login/index')}" class="huobi-nav-link" data-nav="finance">钱包</a></if>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Issue/index')}" class="huobi-nav-link" data-nav="issue">IEO</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Orepool/index')}" class="huobi-nav-link" data-nav="defi">DeFi</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Index/gglist')}" class="huobi-nav-link" data-nav="notice">公告中心</a>
                    </li>
                    <li class="huobi-nav-item">
                        <a href="{:U('Login/lhelp')}" class="huobi-nav-link" data-nav="help">帮助中心</a>
                    </li>
                </ul>
            </nav>
            
            <div class="huobi-user-area">
                <!-- 语言切换下拉菜单 -->                <div class="language-dropdown" style="position: relative; display: inline-block; margin-right: 15px;">                    <button class="lang-btn" onclick="toggleLanguageDropdown()" style="background: white; border: 1px solid #d8d8d8; border-radius: 4px; padding: 8px 12px; cursor: pointer; display: flex; align-items: center; gap: 6px; color: #666; font-size: 14px; min-width: 80px; justify-content: space-between;">                        <i class="bi bi-globe" style="font-size: 16px;"></i>                        <span class="current-lang" style="font-weight: 500;"><?php if(LANG_SET=='zh-cn') echo '中文'; elseif(LANG_SET=='en-us') echo 'EN'; elseif(LANG_SET=='fr-fr') echo 'FR'; elseif(LANG_SET=='de-de') echo 'DE'; elseif(LANG_SET=='it-it') echo 'IT'; elseif(LANG_SET=='ja-jp') echo 'JP'; elseif(LANG_SET=='ko-kr') echo 'KR'; elseif(LANG_SET=='tr-tr') echo 'TR'; else echo '语言'; ?></span>                        <i class="bi bi-chevron-down" style="font-size: 12px;"></i>                    </button>                    <div class="lang-dropdown-menu" id="langDropdown" style="position: absolute; top: 100%; right: 0; background: white; border: 1px solid #e6e6e6; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); min-width: 160px; z-index: 1000; display: none; overflow: hidden;">                        <a href="?Lang=zh-cn" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/zh.png" alt="中文" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">中文简体</a>                        <a href="?Lang=en-us" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/en.png" alt="English" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">English</a>                        <a href="?Lang=fr-fr" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/fr.png" alt="Français" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">Français</a>                        <a href="?Lang=de-de" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/de.png" alt="Deutsch" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">Deutsch</a>                        <a href="?Lang=it-it" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/it.png" alt="Italiano" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">Italiano</a>                        <a href="?Lang=ja-jp" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/jp.png" alt="日本語" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">日本語</a>                        <a href="?Lang=ko-kr" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/kr.png" alt="한국어" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">한국어</a>                        <a href="?Lang=tr-tr" style="display: flex; align-items: center; padding: 10px 15px; color: #666; text-decoration: none; gap: 10px;"><img src="/Public/Static/img/flag/tr.png" alt="Türk" style="width: 20px; height: 15px; object-fit: cover; border-radius: 2px;">Türk</a>                    </div>                </div>
                    <a href="{:U("Login/index")}" class="huobi-btn huobi-btn-outline">登录</a>
                    <a href="{:U("Login/register")}" class="huobi-btn huobi-btn-primary">注册</a>
            </div>
        </div>
    </header>

    <script>
        
    
    // 图片404修复
    (function(){
        function fixExt(u){
            if(!u) return u;
            return u.replace(/\.png\.png$/i,'.png').replace(/\.jpg\.jpg$/i,'.jpg');
        }
        document.addEventListener('error', function(e){
            var el=e.target; if(!el||el.tagName!=='IMG')return;
            if(el.dataset && el.dataset.imgFixed==='1'){ 
                el.src='/Public/Home/static/imgs/empty-dark.png'; return; 
            }
            if(el.dataset) el.dataset.imgFixed='1';
            var s=el.getAttribute('src')||''; var f=fixExt(s);
            if(f!==s){ el.src=f; return; }
            if(!/\.(png|jpg|jpeg|gif|svg)([?#]|$)/i.test(s)){ el.src=s+'.png'; return; }
            el.src='/Public/Home/static/imgs/empty-dark.png';
        }, true);
    })();
    </script>
