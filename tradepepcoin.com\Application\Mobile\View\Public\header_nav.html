<style>
/* Mobile unified top nav */
.m-nav {position: sticky; top: 0; z-index: 9999; background:#121420; border-bottom: 1px solid rgba(255,255,255,0.06);}
.m-nav .m-wrap {display:flex; overflow-x:auto; gap: 12px; padding:10px 12px; scrollbar-width:none;}
.m-nav .m-wrap::-webkit-scrollbar{display:none;}
.m-nav a {color:#d1d5db; font-size:14px; white-space:nowrap; padding:6px 10px; border-radius:6px; text-decoration:none; display:inline-block;}
.m-nav a.active {background:#1e2329; color:#f0b90b; font-weight:600;}
@media (min-width:768px){ .m-nav a{font-size:15px;} }
/* Ensure buttons/text visible */
.m-nav a, .m-nav span {visibility: visible !important; opacity: 1 !important; text-indent:0 !important; letter-spacing:normal !important;}
</style>
<div class="m-nav">
  <div class="m-wrap" id="mNavWrap">
    <a href="{:U('Index/index')}" data-key="index">{:L('首页')}</a>
    <a href="{:U('Contract/index')}" data-key="contract">{:L('秒合约')}</a>
    <a href="{:U('Trade/index')}" data-key="trade">{:L('币币交易')}</a>
    <a href="{:U('Finance/index')}" data-key="finance">{:L('充币')}</a>
    <a href="{:U('Issue/index')}" data-key="issue">IEO</a>
    <a href="{:U('Orepool/index')}" data-key="defi">DeFi</a>
    <a href="{:U('Index/gglist')}" data-key="notice">{:L('公告中心')}</a>
    <a href="{:U('Login/lhelp')}" data-key="help">{:L('帮助中心')}</a>
  </div>
</div>
<script>
(function(){
  try{
    var href = (location.pathname + location.search).toLowerCase();
    var map = {
      index: ['/index/index','/mobile/index/index'],
      contract: ['/contract/index'],
      trade: ['/trade/index','/trade/ordinary','/trade/trans','/trade/transinfo','/trade/tradelist'],
      finance: ['/finance/index','/user/czcoin','/user/txcoin','/user/txpage','/user/czpage'],
      issue: ['/issue/index','/issue/details'],
      defi: ['/orepool/index','/orepool/kjinfo','/orepool/kjshare','/orepool/profitlist'],
      notice: ['/index/gglist','/index/gginfo','/index/notice','/index/noticeinfo'],
      help: ['/login/lhelp']
    };
    var activeKey = 'index';
    Object.keys(map).forEach(function(k){
      if(map[k].some(function(p){return href.indexOf(p)!==-1;})) activeKey=k;
    });
    var links = document.querySelectorAll('#mNavWrap a');
    links.forEach(function(a){ if(a.getAttribute('data-key')===activeKey){ a.classList.add('active'); }});
  }catch(e){}
})();
</script>

