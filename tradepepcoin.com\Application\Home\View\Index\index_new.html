<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$webname} - 全球领先的数字资产交易平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/override.css" />
    
    <style>
        :root {
            --primary-color: #f0b90b;
            --secondary-color: #1e2329;
            --success-color: #02c076;
            --danger-color: #f84960;
            --text-primary: #1e2329;
            --text-secondary: #707a8a;
            --bg-primary: #ffffff;
            --bg-secondary: #f5f5f5;
            --border-color: #eaecef;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #f0b90b 0%, #f8d12f 100%);
            padding: 80px 0 60px;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #fff;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Registration Form */
        .register-form {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .register-form h3 {
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .form-control {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #d4a309;
            border-color: #d4a309;
            transform: translateY(-1px);
        }

        /* Notice Section */
        .notice-section {
            background: #fff;
            padding: 20px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .notice-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notice-label {
            background: var(--primary-color);
            color: #fff;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .notice-content {
            flex: 1;
            overflow: hidden;
        }

        .notice-marquee {
            animation: marquee 30s linear infinite;
            white-space: nowrap;
        }

        @keyframes marquee {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }

        /* Market Section */
        .market-section {
            padding: 60px 0;
            background-color: var(--bg-primary);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--text-primary);
        }

        .market-table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .table tbody td {
            padding: 1rem;
            border-color: #f1f3f4;
            vertical-align: middle;
        }

        .coin-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .coin-logo {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }

        .coin-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .coin-symbol {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .price-positive {
            color: var(--success-color);
        }

        .price-negative {
            color: var(--danger-color);
        }

        .change-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .change-positive {
            background-color: rgba(2, 192, 118, 0.1);
            color: var(--success-color);
        }

        .change-negative {
            background-color: rgba(248, 73, 96, 0.1);
            color: var(--danger-color);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 6px;
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Features Section */
        .features-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .feature-card {
            background: #fff;
            padding: 2.5rem;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), #f8d12f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: #fff;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }
            
            .register-form {
                margin-top: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .table-responsive {
                font-size: 0.9rem;
            }
        }
    </style>
    
    <script src="/Public/Home/js/pako.min.js"></script>
    <script>if(typeof window.pako==='undefined'){document.write('<script src="https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js"></'+'script>');}</script>
</head>

<body>
    <div class="App">
        <include file="Public:header"/>

        <main>
            <!-- Notice Section -->
            <section class="notice-section">
                <div class="container">
                    <div class="notice-container">
                        <div class="notice-label">
                            <i class="bi bi-megaphone"></i> {:L('公告')}
                        </div>
                        <div class="notice-content">
                            <div class="notice-marquee" id="noticeMarquee">
                                欢迎来到{:get_config('webname')}，全球领先的数字资产交易平台
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Hero Section -->
            <section class="hero-section">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="hero-content">
                                <h1 class="hero-title">
                                    发现您的下一个投资机会
                                </h1>
                                <p class="hero-subtitle">
                                    全球领先的数字资产交易平台，为您提供安全、便捷的交易体验
                                </p>
                                <div class="hero-stats">
                                    <div class="stat-item">
                                        <span class="stat-value">$289,789,518</span>
                                        <span class="stat-label">{:L('24小时交易量')}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">200+</span>
                                        <span class="stat-label">交易对</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">1M+</span>
                                        <span class="stat-label">用户信赖</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <if condition="$uid elt 0">
                            <div class="register-form">
                                <h3>立即开始交易</h3>
                                <form action="{:U('Login/register')}" method="get">
                                    <div class="mb-3">
                                        <input type="email" class="form-control" name="email" placeholder="{:L('请输入邮箱或手机')}" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-person-plus"></i> {:L('立即注册')}
                                    </button>
                                </form>
                                <div class="text-center mt-3">
                                    <small class="text-muted">
                                        已有账户？<a href="{:U('Login/index')}" class="text-decoration-none">立即登录</a>
                                    </small>
                                </div>
                            </div>
                            </if>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Market Section -->
            <section class="market-section">
                <div class="container">
                    <h2 class="section-title">热门交易对</h2>
                    <div class="market-table">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>{:L('名称')}</th>
                                        <th>{:L('价格')}</th>
                                        <th>{:L('涨跌幅')}</th>
                                        <th>{:L('最高')}/{:L('最低')}</th>
                                        <th>24H{:L('量')}</th>
                                        <th>{:L('操作')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $GVD1= M("issue")->where(["id"=>3])->find(); ?>
                                    <tr>
                                        <td>
                                            <div class="coin-info">
                                                <div>
                                                    <div class="coin-symbol">MBN/USDT</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="price-positive">${$GVD1.price}</td>
                                        <td>
                                            <span class="change-badge change-positive">+{$GVD1.lixi}%</span>
                                        </td>
                                        <td>{$GVD1.price}/{$GVD1.yuan_price}</td>
                                        <td>{$GVD1.sellnum}</td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="/Issue/details?id=3" class="btn btn-outline-primary btn-sm">详情</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <foreach name="market" item="vo">
                                    <tr class="market-row" data-coin="{$vo.coinname}">
                                        <td>
                                            <div class="coin-info">
                                                <img src="{$vo.logo}" class="coin-logo" alt="{$vo.coinname}">
                                                <div>
                                                    <div class="coin-name"><?php echo strtoupper($vo['coinname']);?></div>
                                                    <div class="coin-symbol"><?php echo strtoupper($vo['coinname']);?>/USDT</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="coin-price">
                                            <span class="loading"></span>
                                        </td>
                                        <td class="coin-change">
                                            <span class="change-badge">0.00%</span>
                                        </td>
                                        <td class="coin-highlow">--.--/--.--</td>
                                        <td class="coin-volume">--.--</td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="{:U('Trade/index')}?type=buy&symbol=<?php echo strtoupper($vo['coinname']);?>" class="btn btn-outline-primary btn-sm">{:L('交易')}</a>
                                                <a href="{:U('Contract/index')}?coin=<?php echo strtoupper($vo['coinname']);?>" class="btn btn-outline-primary btn-sm">{:L('合约')}</a>
                                            </div>
                                        </td>
                                    </tr>
                                    </foreach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="features-section">
                <div class="container">
                    <h2 class="section-title">为什么选择我们</h2>
                    <div class="row g-4">
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-shield-check"></i>
                                </div>
                                <h3 class="feature-title">{:L('安全储存')}</h3>
                                <p class="feature-description">
                                    客户资金存放在专用的多签名冷钱包中，24/7全天候安全监控，专用20,000 BTC安全储备金
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-credit-card"></i>
                                </div>
                                <h3 class="feature-title">{:L('信用卡付款')}</h3>
                                <p class="feature-description">
                                    通过我们的合作伙伴用您的信用卡购买加密货币，支持多种法币充值方式
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <h3 class="feature-title">{:L('管理您的资产')}</h3>
                                <p class="feature-description">
                                    以高达5倍的杠杆率进行现货交易，专业的交易工具助您把握每一个投资机会
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-phone"></i>
                                </div>
                                <h3 class="feature-title">{:L('随处访问')}</h3>
                                <p class="feature-description">
                                    在我们用于Android和iOS的移动应用上进行24/7全天候存款，取款和交易
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-lightning"></i>
                                </div>
                                <h3 class="feature-title">高性能交易</h3>
                                <p class="feature-description">
                                    毫秒级撮合引擎，支持每秒百万级交易处理，为您提供极速交易体验
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-headset"></i>
                                </div>
                                <h3 class="feature-title">24/7客服支持</h3>
                                <p class="feature-description">
                                    专业客服团队全天候在线，多语言支持，随时为您解答疑问
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- About Section -->
            <section class="py-5 bg-light">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto text-center">
                            <h2 class="section-title">{:L('关于我们')}</h2>
                            <div class="text-muted">
                                <?php $about_cn=get_config('about_cn'); $about_en=get_config('about_en'); ?>
                                <?php if(LANG_SET=='zh-cn' && $about_cn){ ?>
                                    <div style="color:#444;line-height:1.8;font-size:15px;white-space:pre-line;">{$about_cn}</div>
                                <?php } elseif(LANG_SET!='zh-cn' && $about_en){ ?>
                                    <div style="color:#444;line-height:1.8;font-size:15px;white-space:pre-line;">{$about_en}</div>
                                <?php } else { ?>
                                    <p>
                                        GVD数字货币交易平台（冠军宇宙全球数字货币交易平台）是目前唯一真正中心化的冠军宇宙项目。
                                        它是一个全球性的综合性交易平台，提供一站式交易、期货合约、融资融券、轻松外交、全网交易、
                                        去中心化交易等系统功能，满足各类投资者的交易需求。
                                    </p>
                                    <p>
                                        它力求将元宇宙概念与全球币本位经济相融合，有效对接全球各大交易所的市场深度，
                                        并提供更高的流动性。元宇宙代币将作为交易媒介，引领人们探索和理解元宇宙。
                                    </p>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <include file="Public:footer"/>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            // 加载公告信息
            loadNotices();

            // 加载市场数据
            loadMarketData();

            // 每30秒更新一次市场数据
            setInterval(loadMarketData, 30000);
        });

        function loadNotices() {
            $.post("{:U('Ajax/getNotices')}", function(data) {
                if (data.code == 1 && data.notices.length > 0) {
                    let noticeText = data.notices.map(notice => notice.title).join(' | ');
                    $('#noticeMarquee').text(noticeText);
                }
            }).fail(function() {
                // 如果AJAX失败，显示默认公告
                $('#noticeMarquee').text('欢迎来到{:get_config("webname")}，全球领先的数字资产交易平台');
            });
        }

        function loadMarketData() {
            // 加载各个币种的市场数据
            $('.market-row').each(function() {
                let coin = $(this).data('coin');
                let row = $(this);

                // 根据币种加载对应的数据
                switch(coin) {
                    case 'btc':
                        loadCoinData(coin, row, "{:U('Ajaxtrade/obtain_btc')}");
                        break;
                    case 'eth':
                        loadCoinData(coin, row, "{:U('Ajaxtrade/obtain_eth')}");
                        break;
                    case 'eos':
                        loadCoinData(coin, row, "{:U('Ajaxtrade/obtain_eos')}");
                        break;
                    case 'doge':
                        loadCoinData(coin, row, "{:U('Ajaxtrade/obtain_doge')}");
                        break;
                    case 'bch':
                        loadCoinData(coin, row, "{:U('Ajaxtrade/obtain_bch')}");
                        break;
                    case 'ltc':
                        loadCoinData(coin, row, "{:U('Ajaxtrade/obtain_ltc')}");
                        break;
                    default:
                        // 对于其他币种，显示默认数据
                        row.find('.coin-price').html('<span class="text-muted">--</span>');
                        row.find('.coin-change').html('<span class="change-badge">0.00%</span>');
                        row.find('.coin-highlow').text('--.--/--.--');
                        row.find('.coin-volume').text('--.--');
                }
            });
        }

        function loadCoinData(coin, row, url) {
            $.post(url, {'coin': coin}, function(data) {
                if (data.code == 1) {
                    // 更新价格
                    row.find('.coin-price').html('<span class="price-positive">$' + data.open + '</span>');

                    // 更新涨跌幅
                    let changeClass = data.change.includes('+') ? 'change-positive' : 'change-negative';
                    row.find('.coin-change').html('<span class="change-badge ' + changeClass + '">' + data.change + '</span>');

                    // 更新最高最低价
                    row.find('.coin-highlow').text(data.highlow);

                    // 更新交易量
                    row.find('.coin-volume').text(data.amount);
                }
            }).fail(function() {
                // 如果请求失败，显示默认数据
                row.find('.coin-price').html('<span class="text-muted">--</span>');
                row.find('.coin-change').html('<span class="change-badge">0.00%</span>');
                row.find('.coin-highlow').text('--.--/--.--');
                row.find('.coin-volume').text('--.--');
            });
        }
    </script>
</body>
</html>
