/* Modern Homepage Styles - Optimized for Performance */
:root {
    --primary-color: #f0b90b;
    --secondary-color: #1e2329;
    --success-color: #02c076;
    --danger-color: #f84960;
    --text-primary: #1e2329;
    --text-secondary: #707a8a;
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --border-color: #eaecef;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.15);
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --border-radius-sm: 6px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Performance Optimizations */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-load.loaded {
    opacity: 1;
}

.preload-hidden {
    visibility: hidden;
}

/* Modern Header Styles */
.modern-header {
    background: #fff;
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0;
    will-change: transform;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-primary) !important;
    text-decoration: none;
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    transform: scale(1.02);
}

.navbar-brand img {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    object-fit: cover;
}

.navbar-nav .nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    transition: var(--transition-normal);
    position: relative;
    border-radius: var(--border-radius-sm);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(240, 185, 11, 0.1);
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-heavy);
    border-radius: var(--border-radius-md);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    animation: dropdownFadeIn 0.2s ease;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    color: var(--text-primary);
    transition: var(--transition-fast);
    border-radius: var(--border-radius-sm);
    margin: 0 0.5rem;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
    transform: translateX(5px);
}

.btn-header {
    padding: 8px 20px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
    border: none;
    font-size: 0.9rem;
    cursor: pointer;
}

.btn-login {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-login:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.btn-register {
    background: var(--primary-color);
    color: #fff;
    border: 1px solid var(--primary-color);
}

.btn-register:hover {
    background: #d4a309;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 185, 11, 0.3);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Notice Section */
.notice-section {
    background: #fff;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    overflow: hidden;
}

.notice-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notice-label {
    background: var(--primary-color);
    color: #fff;
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notice-content {
    flex: 1;
    overflow: hidden;
}

.notice-marquee {
    animation: marquee 30s linear infinite;
    white-space: nowrap;
    will-change: transform;
}

@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0 60px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 700;
    color: #fff;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-weight: 400;
    max-width: 600px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    min-width: 120px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #fff;
    display: block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.25rem;
}

/* Registration Form */
.register-form {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.register-form h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-align: center;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 12px 16px;
    font-size: 1rem;
    transition: var(--transition-normal);
    background: #fff;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    border-radius: var(--border-radius-md);
    padding: 12px 24px;
    font-weight: 600;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background-color: #d4a309;
    border-color: #d4a309;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 185, 11, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(240, 185, 11, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 991px) {
    .navbar-collapse {
        background: #fff;
        padding: 1rem;
        border-radius: var(--border-radius-md);
        margin-top: 1rem;
        box-shadow: var(--shadow-medium);
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .stat-item {
        min-width: 100px;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .register-form {
        margin-top: 2rem;
        padding: 1.5rem;
    }
    
    .notice-container {
        gap: 15px;
    }
    
    .notice-label {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 60px 0 40px;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .register-form {
        padding: 1.25rem;
    }
    
    .btn-header {
        padding: 6px 16px;
        font-size: 0.85rem;
    }
}

/* Market Section */
.market-section {
    padding: 60px 0;
    background-color: var(--bg-primary);
}

.section-title {
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-primary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.market-table {
    background: #fff;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.table {
    margin-bottom: 0;
    font-size: 0.95rem;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody td {
    padding: 1rem;
    border-color: #f1f3f4;
    vertical-align: middle;
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: rgba(240, 185, 11, 0.05);
    transform: scale(1.01);
}

.coin-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.coin-logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
    transition: var(--transition-fast);
}

.coin-logo:hover {
    transform: scale(1.1);
    border-color: var(--primary-color);
}

.coin-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.coin-symbol {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: 2px;
}

.price-positive {
    color: var(--success-color);
    font-weight: 600;
}

.price-negative {
    color: var(--danger-color);
    font-weight: 600;
}

.change-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
    display: inline-block;
}

.change-positive {
    background-color: rgba(2, 192, 118, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(2, 192, 118, 0.2);
}

.change-negative {
    background-color: rgba(248, 73, 96, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(248, 73, 96, 0.2);
}

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: var(--transition-normal);
    text-decoration: none;
    border: 1px solid;
    cursor: pointer;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(240, 185, 11, 0.3);
}

/* Features Section */
.features-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(240,185,11,0.1)"/></pattern></defs><rect width="60" height="60" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
}

.feature-card {
    background: #fff;
    padding: 2.5rem;
    border-radius: var(--border-radius-xl);
    text-align: center;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-normal);
    height: 100%;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.1), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #fff;
    transition: var(--transition-normal);
    position: relative;
    z-index: 1;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 20px rgba(240, 185, 11, 0.4);
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    position: relative;
    z-index: 1;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

/* About Section */
.about-section {
    padding: 80px 0;
    background: #fff;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

/* 火币风格导航栏样式 */
.huobi-header {
    background: #fff;
    border-bottom: 1px solid #eaecef;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.huobi-navbar {
    padding: 0;
}

.huobi-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.huobi-nav-left {
    display: flex;
    align-items: center;
    gap: 40px;
}

.huobi-logo {
    font-size: 24px;
    font-weight: 700;
    color: #1e2329;
    text-decoration: none;
    transition: var(--transition-fast);
}

.huobi-logo:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.huobi-nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;
}

.huobi-nav-item {
    color: #1e2329;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    padding: 8px 0;
    position: relative;
    transition: var(--transition-fast);
}

.huobi-nav-item:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.huobi-nav-item::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.huobi-nav-item:hover::after {
    width: 100%;
}

.huobi-nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.huobi-btn {
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-normal);
    border: 1px solid;
    cursor: pointer;
    display: inline-block;
}

.huobi-btn-login {
    background: transparent;
    color: #1e2329;
    border-color: #d1d5db;
}

.huobi-btn-login:hover {
    background: #f5f5f5;
    color: #1e2329;
    text-decoration: none;
    border-color: var(--primary-color);
}

.huobi-btn-register {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.huobi-btn-register:hover {
    background: #d4a309;
    color: #fff;
    text-decoration: none;
    border-color: #d4a309;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 185, 11, 0.3);
}

/* 移动端导航栏适配 */
@media (max-width: 768px) {
    .huobi-container {
        padding: 0 15px;
        height: 56px;
    }

    .huobi-nav-menu {
        display: none;
    }

    .huobi-logo {
        font-size: 20px;
    }

    .huobi-btn {
        padding: 6px 16px;
        font-size: 13px;
    }

    .huobi-nav-right {
        gap: 12px;
    }
}

/* Print Styles */
@media print {
    .modern-header,
    .huobi-header,
    .notice-section,
    .register-form,
    .action-buttons {
        display: none;
    }

    .hero-section,
    .features-section {
        background: none !important;
        color: #000 !important;
    }

    .hero-title,
    .hero-subtitle,
    .stat-value,
    .stat-label,
    .feature-title,
    .feature-description {
        color: #000 !important;
    }

    .feature-icon {
        background: #ccc !important;
    }
}
