/**
 * Modern Homepage JavaScript - 安全简化版
 * 只包含核心功能，避免复杂特性
 */

class ModernHomepage {
    constructor() {
        this.cache = new Map();
        this.init();
    }

    init() {
        this.setupBasicEventListeners();
        this.loadNotices();
        this.loadMarketData();

        // 每30秒更新一次市场数据
        setInterval(() => this.loadMarketData(), 30000);
    }

    setupBasicEventListeners() {
        // 简单的滚动处理
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // 表单提交处理
        document.addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });

        // 平滑滚动
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                const href = link.getAttribute('href') || '';
                // 仅当 href 类似于 #section 且不为单独的 '#' 时才平滑滚动
                if (href.length > 1) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            }
        });
    }

    async loadNotices() {
        try {
            // 使用jQuery AJAX来避免路径问题
            if (window.$ && window.$.post) {
                window.$.post((window.APP_URLS && window.APP_URLS.getNotices) || '/Home/Ajax/getNotices', {}, (data) => {
                    if (data.code === 1 && data.notices && data.notices.length > 0) {
                        this.displayNotices(data.notices);
                    } else {
                        this.displayDefaultNotice();
                    }
                }, 'json').fail(() => {
                    this.displayDefaultNotice();
                });
            } else {
                this.displayDefaultNotice();
            }
        } catch (error) {
            console.warn('Failed to load notices:', error);
            this.displayDefaultNotice();
        }
    }

    displayNotices(notices) {
        const marquee = document.getElementById('noticeMarquee');
        if (marquee && notices.length > 0) {
            const noticeText = notices.map(notice => notice.title).join(' | ');
            marquee.textContent = noticeText;
        }
    }

    displayDefaultNotice() {
        const marquee = document.getElementById('noticeMarquee');
        if (marquee) {
            marquee.textContent = '欢迎来到交易平台，全球领先的数字资产交易平台';
        }
    }

    async loadMarketData() {
        const rows = document.querySelectorAll('.market-row');

        for (const row of rows) {
            await this.loadCoinData(row);
        }
    }

    async loadCoinData(row) {
        const coin = row.dataset.coin;
        if (!coin) return;

        try {
            const url = this.getCoinDataUrl(coin);
            if (!url) {
                this.showCoinDefault(row);
                return;
            }

            // 使用jQuery AJAX来避免路径问题
            if (window.$ && window.$.post) {
                window.$.post(url, { coin: coin }, (data) => {
                    if (data.code === 1) {
                        this.updateCoinDisplay(row, data);
                    } else {
                        this.showCoinDefault(row);
                    }
                }, 'json').fail(() => {
                    this.showCoinDefault(row);
                });
            } else {
                this.showCoinDefault(row);
            }
        } catch (error) {
            console.warn('Failed to load data for ' + coin + ':', error);
            this.showCoinDefault(row);
        }
    }

    getCoinDataUrl(coin) {
        const lower = coin.toLowerCase();
        const urlMap = (window.APP_URLS) ? window.APP_URLS : null;
        if (urlMap && urlMap[lower]) return urlMap[lower];
        const fallback = {
            'btc': '/Home/Ajaxtrade/obtain_btc',
            'eth': '/Home/Ajaxtrade/obtain_eth',
            'eos': '/Home/Ajaxtrade/obtain_eos',
            'doge': '/Home/Ajaxtrade/obtain_doge',
            'bch': '/Home/Ajaxtrade/obtain_bch',
            'ltc': '/Home/Ajaxtrade/obtain_ltc'
        };
        return fallback[lower];
    }

    showCoinDefault(row) {
        const priceCell = row.querySelector('.coin-price');
        const changeCell = row.querySelector('.coin-change');
        const highlowCell = row.querySelector('.coin-highlow');
        const volumeCell = row.querySelector('.coin-volume');

        if (priceCell) priceCell.innerHTML = '<span class="text-muted">--</span>';
        if (changeCell) changeCell.innerHTML = '<span class="change-badge">0.00%</span>';
        if (highlowCell) highlowCell.textContent = '--.--/--.--';
        if (volumeCell) volumeCell.textContent = '--.--';
    }

    updateCoinDisplay(row, data) {
        const priceCell = row.querySelector('.coin-price');
        const changeCell = row.querySelector('.coin-change');
        const highlowCell = row.querySelector('.coin-highlow');
        const volumeCell = row.querySelector('.coin-volume');

        if (priceCell && data.open) {
            priceCell.innerHTML = '<span class="price-positive">$' + data.open + '</span>';
        }

        if (changeCell && data.change) {
            const changeClass = data.change.includes('+') ? 'change-positive' : 'change-negative';
            changeCell.innerHTML = '<span class="change-badge ' + changeClass + '">' + data.change + '</span>';
        }

        if (highlowCell && data.highlow) {
            highlowCell.textContent = data.highlow;
        }

        if (volumeCell && data.amount) {
            volumeCell.textContent = data.amount;
        }
    }

    handleFormSubmit(e) {
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');

        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '处理中...';
            submitBtn.disabled = true;

            // 3秒后恢复
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        }
    }

    handleScroll() {
        const scrollY = window.scrollY;
        const header = document.querySelector('.modern-header');

        if (header) {
            if (scrollY > 100) {
                header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
            } else {
                header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            }
        }
    }

    // 公共API
    refresh() {
        this.loadNotices();
        this.loadMarketData();
    }

    clearCache() {
        this.cache.clear();
    }
}

// 初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        window.modernHomepage = new ModernHomepage();
    });
} else {
    window.modernHomepage = new ModernHomepage();
}

// 添加基础样式
const style = document.createElement('style');
style.textContent = '.loading { display: inline-block; width: 16px; height: 16px; border: 2px solid #f3f3f3; border-top: 2px solid #f0b90b; border-radius: 50%; animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
document.head.appendChild(style);
