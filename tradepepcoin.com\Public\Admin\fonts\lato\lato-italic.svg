<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="latoitalic" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1649" descent="-399" />
<missing-glyph horiz-adv-x="395" />
<glyph unicode="&#xfb01;" horiz-adv-x="1080" d="M974 1017l-124 -1017h-180l109 885h-396l-104 -852l-48 -212q-17 -77 -94 -77h-71l140 1139l-102 12q-41 5 -41 41l8 81h151l7 57q11 95 48.5 174t100 135.5t148.5 87.5t196 31q18 0 38 -1.5t40 -4t38 -6t31 -8.5l-17 -92q-3 -12 -14 -16t-28 -4q-11 0 -24.5 0.5 t-29.5 0.5q-85 0 -147.5 -18.5t-106 -55t-68.5 -93t-35 -133.5l-6 -54h581z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1121" d="M384 886l-105 -853l-48 -212q-17 -77 -94 -77h-71l139 1139l-101 12q-41 5 -41 39l9 83h149l7 56q10 86 44 163t90.5 134t134 90.5t177.5 33.5q73 0 144.5 -6.5t125.5 -6.5h122l-181 -1481h-177l163 1348q-46 4 -94.5 9t-92.5 5q-120 0 -194 -75.5t-90 -213.5l-6 -56h244 l-15 -131h-239z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="395" />
<glyph unicode="&#x09;" horiz-adv-x="395" />
<glyph unicode="&#xa0;" horiz-adv-x="395" />
<glyph unicode="!" horiz-adv-x="650" d="M530 1467l-71 -587q-6 -46 -13.5 -90t-15.5 -88t-18.5 -91t-22.5 -100h-120q1 53 2.5 100t4.5 91t6.5 88t9.5 90l72 587h166zM163 113q0 27 9.5 50t26.5 40.5t40 28t51 10.5q27 0 50.5 -10.5t40.5 -28t27 -41t10 -49.5q0 -28 -10 -51t-27 -40.5t-41 -27t-50 -9.5 q-28 0 -51 9.5t-40 27t-26.5 40.5t-9.5 51z" />
<glyph unicode="&#x22;" horiz-adv-x="753" d="M413 1465l-37 -296l-35 -158q-7 -32 -23 -49.5t-46 -17.5q-27 0 -38 17.5t-11 49.5v158l38 296h152zM732 1465l-37 -296l-35 -158q-7 -32 -22.5 -49.5t-45.5 -17.5q-27 0 -38 17.5t-11 49.5v158l38 296h151z" />
<glyph unicode="#" d="M799 434l-141 -434h-79q-20 0 -33.5 14t-13.5 36q0 14 5 27l115 357h-256l-118 -366q-11 -36 -36 -52t-54 -16h-78l140 434h-151q-44 0 -44 41q0 7 1.5 15t3.5 18l16 56h210l108 339h-238l21 73q8 29 28 42t59 13h165l121 372q10 30 33 46t53 16h80l-142 -434h256 l142 434h79q23 0 37 -12t14 -33q0 -11 -4 -20l-121 -369h219l-22 -73q-8 -29 -28 -42t-57 -13h-147l-108 -339h186q20 0 32.5 -9.5t12.5 -31.5q0 -7 -1 -14.5t-4 -18.5l-17 -56h-244zM431 564h257l109 339h-256z" />
<glyph unicode="$" d="M451 -12q-122 11 -218 60.5t-160 126.5l62 80q19 26 50 26q18 0 40.5 -19.5t55.5 -45t80.5 -49.5t116.5 -32l106 553q-63 19 -124.5 44.5t-109 64t-76.5 95t-29 140.5q0 85 33.5 164t97 140.5t154.5 100.5t204 43l29 148q4 19 19.5 34t35.5 15h66l-39 -201 q105 -12 178.5 -54t125.5 -102l-49 -66q-11 -15 -22.5 -22t-26.5 -7q-14 0 -33.5 13t-47 30.5t-65 34.5t-88.5 24l-95 -503q66 -22 130 -47t115 -62.5t82.5 -93t31.5 -136.5q0 -97 -35.5 -184.5t-103.5 -155.5t-164 -110.5t-216 -49.5l-35 -183q-4 -18 -18.5 -32.5 t-36.5 -14.5h-65zM908 442q0 45 -17.5 78.5t-47 58t-69.5 42.5t-85 33l-98 -520q76 7 134.5 33t99.5 66t62 93.5t21 115.5zM415 1072q0 -43 15.5 -75t43 -57.5t64 -44t78.5 -35.5l90 469q-74 -6 -129 -29t-91 -58t-53.5 -78.5t-17.5 -91.5z" />
<glyph unicode="%" horiz-adv-x="1486" d="M750 1171q0 -101 -30.5 -180t-79.5 -133t-112 -82t-130 -28q-57 0 -105 21t-82.5 60t-53.5 96t-19 129q0 101 28 180.5t75.5 134t111.5 83.5t135 29q57 0 105.5 -21t83 -61t54 -97.5t19.5 -130.5zM611 1170q0 51 -10.5 88t-29 60.5t-43.5 35t-54 11.5q-41 0 -77.5 -17.5 t-63 -55.5t-42.5 -96.5t-16 -141.5q0 -50 10.5 -85.5t29 -58.5t43.5 -34t55 -11q40 0 76 17t63.5 53.5t43 94t15.5 140.5zM1257 1437q11 11 26 19.5t39 8.5h127l-1167 -1436q-10 -13 -25 -21t-36 -8h-129zM1405 407q0 -101 -29.5 -181t-79 -133.5t-113 -81.5t-129.5 -28 q-57 0 -105 21t-82 60t-53 95.5t-19 128.5q0 101 27.5 180.5t75 134.5t111.5 84t136 29q57 0 104.5 -21t82.5 -61t54 -97t19 -130zM1268 406q0 51 -11 87t-29.5 60t-43.5 34.5t-54 10.5q-41 0 -77 -17.5t-63 -55.5t-43 -96t-16 -141q0 -50 10 -86t28.5 -59t43.5 -34t55 -11 q40 0 76.5 17t64 54t43.5 95.5t16 141.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1328" d="M741 1482q72 0 130.5 -22.5t100.5 -62.5t65 -94.5t23 -117.5v-6t-1 -9l-104 -19q-5 -1 -8.5 -1.5t-7.5 -0.5q-30 0 -37 34q-6 30 -18.5 58.5t-33.5 51.5t-51.5 37.5t-72.5 14.5q-52 0 -94 -19.5t-72.5 -54t-46.5 -81t-16 -99.5q0 -58 24 -120t75 -126l330 -419 q42 68 69.5 140t41.5 145q4 18 13.5 29.5t28.5 11.5h122q-16 -118 -62.5 -231.5t-119.5 -212.5l244 -308h-152q-22 0 -37.5 2.5t-28 8.5t-23 16.5t-23.5 24.5l-98 125q-96 -89 -214 -141t-250 -52q-84 0 -153.5 25t-119.5 71.5t-78 113t-28 149.5q0 81 27.5 152.5t75 131.5 t111.5 107t138 80q-40 68 -59 133.5t-19 129.5q0 84 29 158.5t83 128.5t129.5 86t167.5 32zM241 366q0 -57 17.5 -102t49 -75.5t74.5 -46.5t93 -16q97 0 183 44t158 116l-341 435q-118 -63 -176 -154.5t-58 -200.5z" />
<glyph unicode="'" horiz-adv-x="436" d="M413 1465l-37 -296l-35 -158q-7 -32 -23 -49.5t-46 -17.5q-27 0 -38 17.5t-11 49.5v158l38 296h152z" />
<glyph unicode="(" horiz-adv-x="565" d="M281 452q0 -76 7 -157.5t21 -163.5t35.5 -161t48.5 -147q6 -15 6 -28q0 -17 -9 -28t-21 -18l-83 -47q-43 88 -73.5 181.5t-50 188t-28.5 186.5t-9 177q0 143 16.5 279t60 274t119 285t195.5 313l73 -48q8 -5 14 -13.5t6 -21.5q0 -16 -14 -38q-76 -117 -134.5 -233 t-98 -239t-60.5 -256t-21 -285z" />
<glyph unicode=")" horiz-adv-x="565" d="M321 837q0 75 -7.5 156.5t-22.5 163.5t-36 161t-48 147q-6 15 -6 27q0 17 9 27.5t21 18.5l84 48q42 -89 72.5 -182t50 -187.5t28.5 -186.5t9 -178q0 -143 -16.5 -279t-59.5 -274t-119 -284.5t-196 -312.5l-72 47q-8 6 -14 14.5t-6 19.5q0 17 14 40q75 117 134 235.5 t99 244t61 258.5t21 276z" />
<glyph unicode="*" horiz-adv-x="753" d="M370 886l25 203q2 18 6.5 35t12.5 32q-11 -13 -24.5 -23t-31.5 -20l-173 -102l-33 74l173 104q37 22 73 24q-37 3 -67 25l-149 104l52 75l149 -103q16 -11 28 -22.5t20 -27.5q-8 25 -8 48q0 6 0.5 12t1.5 13l26 204h86l-25 -203q-2 -19 -7 -36t-15 -34q11 14 26 25t33 21 l173 102l33 -75l-173 -103q-36 -22 -69 -25q17 -2 32 -7.5t30 -16.5l149 -105l-51 -73l-149 102q-15 10 -27.5 20.5t-20.5 24.5q7 -20 7 -43q0 -6 -0.5 -12t-1.5 -13l-24 -204h-87z" />
<glyph unicode="+" d="M750 1193l-55 -439h420l-16 -133h-420l-54 -442h-145l55 442h-417l16 133h417l55 439h144z" />
<glyph unicode="," horiz-adv-x="435" d="M51 126q0 48 33.5 82t88.5 34q31 0 54.5 -11.5t39.5 -31.5t24 -46t8 -57q0 -46 -13 -95.5t-38 -98t-60.5 -95t-82.5 -85.5l-31 30q-13 12 -14 29q0 12 16.5 31.5t38 46.5t41.5 62.5t27 78.5h-12q-53 0 -86.5 35.5t-33.5 90.5z" />
<glyph unicode="-" horiz-adv-x="656" d="M118 688h468l-20 -149h-467z" />
<glyph unicode="." horiz-adv-x="435" d="M46 113q0 27 10 50t27 40.5t40 28t51 10.5q27 0 50 -10.5t40.5 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5q-28 0 -51 9.5t-40 27t-27 40.5t-10 51z" />
<glyph unicode="/" horiz-adv-x="711" d="M95 -26q-16 -33 -46 -50t-57 -17h-75l741 1514q14 31 41 48t60 17h75z" />
<glyph unicode="0" d="M712 1482q97 0 179 -37.5t141 -112t92 -186.5t33 -261q0 -228 -50 -398t-136 -281.5t-200.5 -166t-243.5 -54.5q-98 0 -180 37t-141 111.5t-92 186.5t-33 263q0 228 50 397t136 280.5t201 166.5t244 55zM550 133q86 0 163.5 45.5t135 138t91.5 233t34 331.5 q0 123 -23 209.5t-62 140.5t-91 78.5t-109 24.5q-86 0 -163 -45.5t-135 -138t-92 -233.5t-34 -331q0 -123 23 -209.5t62 -140.5t91 -78.5t109 -24.5z" />
<glyph unicode="1" d="M250 135h317l126 1029l19 79l-300 -234q-18 -14 -39 -15q-15 0 -27.5 7t-17.5 15l-45 77l481 377h143l-164 -1335h291l-16 -135h-784z" />
<glyph unicode="2" d="M732 1482q85 0 157.5 -24t124.5 -70.5t81 -112.5t29 -150q0 -93 -29 -169.5t-79 -145.5t-117.5 -133.5t-142.5 -132.5l-442 -402q42 11 84 17.5t81 6.5h502q27 0 41 -14.5t14 -37.5q0 -10 -2 -27.5t-4 -32.5l-7 -54h-975l7 60q2 17 12.5 38.5t28.5 37.5l531 474 q75 67 133 127t98.5 118.5t61.5 120t21 132.5q0 55 -18 97t-49.5 70t-75 42t-95.5 14q-115 0 -195 -60t-118 -164q-11 -30 -29 -42t-44 -12q-5 0 -11 0.5t-12 1.5l-89 15q26 100 76.5 177t119 129t153 79t178.5 27z" />
<glyph unicode="3" d="M755 1482q86 0 157 -23.5t121 -67t78 -105t28 -136.5q0 -81 -21.5 -143t-61 -108t-95.5 -76t-124 -49q125 -36 187.5 -115t62.5 -194q0 -111 -44 -199.5t-118.5 -151.5t-173.5 -96.5t-207 -33.5q-102 0 -179 23.5t-131.5 70.5t-88 118.5t-50.5 169.5l82 30q23 8 41 8 q19 0 33 -8t19 -24q16 -59 39 -104t56.5 -74.5t81 -44.5t113.5 -15q84 0 149.5 28.5t110 75t68 104.5t23.5 119q0 49 -15 90.5t-55 71.5t-107.5 47.5t-172.5 17.5l17 126q195 4 290 83.5t95 216.5q0 54 -17.5 95t-49.5 68.5t-75 40.5t-94 13q-117 0 -196.5 -60.5 t-118.5 -163.5q-11 -30 -29 -42t-43 -12q-5 0 -10.5 0.5t-11.5 1.5l-90 15q26 100 76.5 177t119.5 129t153 79t178 27z" />
<glyph unicode="4" d="M931 526h222l-14 -100q-2 -15 -13 -26.5t-31 -11.5h-182l-47 -388h-159l48 388h-645q-20 0 -38 11.5t-20 28.5l-6 89l831 950h170zM848 1144q2 25 8 53t14 57l-629 -728h530z" />
<glyph unicode="5" d="M1110 1390q-5 -37 -31 -61t-83 -24h-466l-117 -395q114 25 208 25q108 0 188 -29t133.5 -80.5t80.5 -121.5t27 -153q0 -129 -48 -234t-128.5 -178.5t-188.5 -114t-228 -40.5q-65 0 -122 12t-106 33t-89 48.5t-70 58.5l62 76q22 25 50 25q19 0 43.5 -16t58.5 -35t83 -35 t117 -16q85 0 156.5 29t122.5 80.5t79 123.5t28 160q0 58 -18 106t-55 82.5t-93.5 52.5t-133.5 18q-51 0 -110 -8.5t-128 -26.5l-106 31l202 684h692z" />
<glyph unicode="6" d="M714 897q81 0 151 -25.5t121.5 -74.5t81.5 -120t30 -160q0 -114 -43 -211t-119 -168.5t-179 -112.5t-223 -41q-100 0 -179.5 30.5t-134.5 87t-84.5 135t-29.5 176.5q0 63 14.5 124t44 123t74 126t105.5 135l437 502q15 17 41.5 29.5t57.5 12.5h156l-504 -550 q-47 -52 -85 -97q56 37 123.5 58t143.5 21zM273 407q0 -60 18.5 -111t53.5 -88t86 -57.5t117 -20.5q81 0 149 27t117.5 75.5t77.5 114t28 142.5q0 62 -20 113t-56.5 86.5t-88.5 54.5t-115 19q-81 0 -148.5 -28.5t-115.5 -77.5t-75.5 -113t-27.5 -136z" />
<glyph unicode="7" d="M1229 1467l-7 -69q-3 -26 -10 -49t-16 -37l-784 -1267q-11 -18 -31.5 -31.5t-43.5 -13.5h-146l772 1219q15 25 31 45.5t35 40.5h-771q-17 0 -28 11t-11 27v5l15 119h995z" />
<glyph unicode="8" d="M530 -16q-101 0 -183.5 26t-141 74t-90 116t-31.5 151q0 165 88.5 268.5t248.5 145.5q-99 39 -149 113.5t-50 175.5q0 90 37 168t102 136t155 91t198 33q92 0 165.5 -27t125.5 -73.5t79.5 -109.5t27.5 -134q0 -134 -71 -231.5t-208 -143.5q119 -36 182 -115t63 -198 q0 -108 -41.5 -194t-114.5 -147t-173.5 -93t-218.5 -32zM547 124q80 0 144.5 23.5t110.5 65.5t70.5 101.5t24.5 130.5q0 66 -23 112t-62 75.5t-89.5 43t-105.5 13.5q-68 0 -130 -18.5t-110.5 -57.5t-78 -99.5t-29.5 -145.5q0 -54 18.5 -99t54.5 -77t87 -50t118 -18zM634 830 q83 0 141 27t94 69.5t52.5 94.5t16.5 104q0 46 -15 86t-44.5 70.5t-74.5 48t-106 17.5q-74 0 -130.5 -23.5t-95.5 -62t-59 -92t-20 -112.5q0 -44 13.5 -85t42.5 -72.5t74.5 -50.5t110.5 -19z" />
<glyph unicode="9" d="M579 603q-77 0 -143.5 24.5t-116 71.5t-77.5 115.5t-28 154.5q0 108 42 201.5t115 162.5t172.5 109t214.5 40q96 0 172.5 -30.5t129.5 -84.5t81.5 -130t28.5 -166q0 -74 -15 -138t-45.5 -124t-73.5 -120.5t-98 -128.5l-423 -518q-15 -18 -39.5 -30t-55.5 -12h-162 l517 586q27 30 50.5 57.5t43.5 53.5q-62 -46 -136 -70t-154 -24zM1002 1072q0 59 -18.5 108t-53.5 83.5t-83.5 53.5t-108.5 19q-75 0 -137.5 -25.5t-109 -71t-72.5 -108.5t-26 -139q0 -120 68 -186.5t191 -66.5q81 0 145.5 27.5t110 73.5t70 106t24.5 126z" />
<glyph unicode=":" horiz-adv-x="435" d="M46 113q0 27 10 50t27 40.5t40 28t51 10.5q27 0 50 -10.5t40.5 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5q-28 0 -51 9.5t-40 27t-27 40.5t-10 51zM148 881q0 27 10 50t27.5 40.5t40.5 28t50 10.5t50 -10.5t40.5 -28t27.5 -41t10 -49.5 q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5q-28 0 -50.5 9.5t-40 27t-27.5 40.5t-10 51z" />
<glyph unicode=";" horiz-adv-x="435" d="M47 126q0 48 33.5 82t88.5 34q31 0 54.5 -11.5t39.5 -31.5t24 -46t8 -57q0 -46 -13 -95.5t-38 -98t-61 -95t-82 -85.5l-31 30q-13 12 -14 29q0 12 16.5 31.5t38 46.5t41.5 62.5t27 78.5h-12q-53 0 -86.5 35.5t-33.5 90.5zM155 881q0 27 9.5 50t27 40.5t40.5 28t51 10.5 q27 0 49.5 -10.5t40 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-40.5 -27t-49 -9.5q-28 0 -51 9.5t-40.5 27t-27 40.5t-9.5 51z" />
<glyph unicode="&#x3c;" horiz-adv-x="1092" d="M172 727l842 411l-17 -130q-2 -17 -12 -30t-31 -24l-488 -233q-22 -11 -46 -19t-50 -14q25 -5 46.5 -13t41.5 -19l432 -232q15 -8 23 -20t8 -23q0 -1 -1.5 -14.5t-4.5 -33.5t-6 -44.5t-6 -48.5l-740 411z" />
<glyph unicode="=" d="M161 587h878l-16 -134h-879zM204 928h876l-15 -134h-878z" />
<glyph unicode="&#x3e;" horiz-adv-x="1092" d="M970 651l-842 -410l16 130q2 17 12.5 30t30.5 23l489 233q22 11 45.5 19t50.5 14q-25 5 -47 13.5t-41 18.5l-432 232q-15 8 -23 20t-8 23q0 1 1.5 14.5t4 33.5t6 44.5t6.5 49.5l740 -412z" />
<glyph unicode="?" horiz-adv-x="754" d="M135 1334q34 30 72.5 56.5t83 47t95 32.5t108.5 12q72 0 129.5 -21.5t98.5 -59.5t62.5 -89.5t21.5 -112.5q0 -86 -24 -146.5t-61 -105t-81.5 -76.5t-85 -62.5t-71.5 -63t-42 -76.5l-38 -158h-121l11 170q3 49 30 87t66 71t83.5 65t82 69t62.5 83.5t25 108.5 q0 80 -48.5 126t-131.5 46q-57 0 -99 -15t-72.5 -33.5t-50.5 -34t-33 -15.5q-25 0 -35 23zM187 113q0 27 10 50t27 40.5t40 28t50 10.5t50.5 -10.5t41 -28t27 -41t9.5 -49.5q0 -28 -9.5 -51t-27 -40.5t-41 -27t-50.5 -9.5q-28 0 -50.5 9.5t-39.5 27t-27 40.5t-10 51z" />
<glyph unicode="@" horiz-adv-x="1532" d="M1051 190q-72 0 -114 38t-46 117q-65 -82 -133.5 -117t-142.5 -35q-49 0 -85 17.5t-60 48.5t-35.5 73t-11.5 91q0 61 16.5 125.5t48.5 124.5t78.5 113.5t107.5 93.5t135 63t160 23q65 0 111 -10.5t88 -30.5l-135 -369q-19 -53 -28 -92t-9 -68q0 -28 7.5 -45.5t20 -28.5 t29.5 -15t37 -4q52 0 101.5 36t88 101t62 155t23.5 198q0 119 -35 209t-97.5 150t-149 91t-189.5 31q-138 0 -262.5 -60t-218.5 -167.5t-149 -255t-55 -322.5q0 -144 41 -256t114 -187t174.5 -114t222.5 -39q144 0 253.5 32t187.5 81q11 7 19.5 9t16.5 2q15 0 24.5 -10 t13.5 -22l12 -52q-110 -74 -243.5 -116t-296.5 -42q-148 0 -271.5 49t-212 140.5t-137 222t-48.5 294.5q0 130 30.5 249t85.5 222t131.5 187.5t169 144.5t197 93t215.5 33q120 0 226 -40.5t185.5 -117.5t125.5 -187.5t46 -251.5q0 -130 -35.5 -240.5t-96 -190.5t-140 -126 t-168.5 -46zM666 310q28 0 58.5 10.5t60 36.5t58 70.5t53.5 113.5l110 303q-36 9 -78 9q-75 0 -141.5 -36.5t-116 -94.5t-79 -131.5t-29.5 -148.5q0 -58 26 -95t78 -37z" />
<glyph unicode="A" horiz-adv-x="1266" d="M1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61z" />
<glyph unicode="B" horiz-adv-x="1219" d="M99 0l182 1467h434q115 0 197 -23t135.5 -65.5t79 -103.5t25.5 -135q0 -66 -19 -125.5t-57 -109.5t-94.5 -89t-132.5 -62q132 -30 198.5 -104.5t66.5 -188.5q0 -101 -36.5 -186t-105 -146t-167 -95t-222.5 -34h-484zM374 671l-65 -519h289q81 0 142.5 21.5t102.5 61 t61.5 96t20.5 127.5q0 97 -65.5 155t-197.5 58h-288zM390 806h237q83 0 145 22t103 61.5t61.5 94.5t20.5 122q0 105 -63.5 158t-196.5 53h-243z" />
<glyph unicode="C" horiz-adv-x="1263" d="M695 148q68 0 120 11.5t92 28t68.5 36.5t49.5 36t35 27.5t25 11.5q9 0 15.5 -3.5t10.5 -8.5l67 -83q-96 -104 -222 -162t-292 -58q-131 0 -235.5 47t-177 131.5t-111 201t-38.5 257.5q0 191 59.5 349.5t163.5 273t244.5 177.5t301.5 63q78 0 141.5 -13.5t116 -39 t95 -61.5t79.5 -80l-66 -80q-8 -10 -18.5 -16.5t-25.5 -6.5q-18 0 -38.5 20.5t-56 44.5t-91.5 44.5t-145 20.5q-119 0 -222 -47.5t-179 -136.5t-119.5 -214.5t-43.5 -282.5q0 -114 29.5 -204t82.5 -153t125.5 -97t159.5 -34z" />
<glyph unicode="D" horiz-adv-x="1418" d="M1373 837q0 -187 -58 -341.5t-159.5 -264.5t-242.5 -170.5t-307 -60.5h-507l182 1467h505q135 0 244.5 -46t185 -129.5t116.5 -199.5t41 -255zM1177 833q0 111 -28.5 199.5t-81 150.5t-128.5 95.5t-171 33.5h-313l-144 -1157h314q123 0 224 46.5t174 134t113.5 213 t40.5 284.5z" />
<glyph unicode="E" horiz-adv-x="1092" d="M1096 1311h-642l-62 -495h520l-19 -150h-519l-63 -510h644l-19 -156h-837l182 1467h834z" />
<glyph unicode="F" horiz-adv-x="1065" d="M1096 1311h-642l-65 -523h550l-21 -155h-547l-78 -633h-194l182 1467h834z" />
<glyph unicode="G" horiz-adv-x="1381" d="M721 138q53 0 99 6t86.5 17t78 27t74.5 36l40 332h-204q-17 0 -27 9t-10 24v5q1 5 1.5 11t0.5 11l1 6l9 80h420l-68 -560q-55 -40 -114 -69.5t-124.5 -49.5t-138.5 -29.5t-157 -9.5q-133 0 -241 47.5t-185 133t-118.5 204t-41.5 260.5q0 189 59 346.5t163 270.5 t247 175.5t312 62.5q85 0 154.5 -14.5t126 -40t102 -60.5t83.5 -76l-62 -80q-10 -13 -21 -20t-25 -7q-17 0 -34 10q-23 13 -50 34t-65 40t-92.5 33t-131.5 14q-126 0 -231 -48t-180 -137.5t-117 -215t-42 -280.5q0 -116 30 -208t85.5 -156.5t133.5 -99t174 -34.5z" />
<glyph unicode="H" horiz-adv-x="1425" d="M1201 0h-193l83 670h-716l-82 -670h-194l182 1467h192l-81 -658h716l81 658h192z" />
<glyph unicode="I" horiz-adv-x="579" d="M325 0h-193l179 1467h193z" />
<glyph unicode="J" horiz-adv-x="836" d="M673 486q-30 -244 -152.5 -373t-324.5 -129q-55 0 -103.5 7t-97.5 21l14 112q2 15 12 26.5t30 11.5q10 0 23.5 -2.5t30.5 -6t39 -6.5t51 -3q53 0 100 17.5t85 57.5t64 104.5t37 158.5l120 985h192z" />
<glyph unicode="K" horiz-adv-x="1287" d="M408 821h67q36 0 59 10t45 33l507 557q24 27 46.5 36.5t53.5 9.5h163l-586 -634q-22 -24 -41 -40t-39 -25q25 -10 42.5 -28t35.5 -45l442 -695h-165q-18 0 -31 3t-21.5 8t-14.5 13t-13 17l-385 586q-16 25 -36 36t-64 11h-83l-83 -674h-190l179 1467h191z" />
<glyph unicode="L" horiz-adv-x="969" d="M310 162h581l-20 -162h-772l182 1467h191z" />
<glyph unicode="M" horiz-adv-x="1736" d="M840 512q6 -22 11.5 -41.5t10.5 -41.5q18 43 43 83l577 923q14 24 29 28t40 4h143l-181 -1467h-167l134 1084q2 20 6.5 44t9.5 50l-570 -920q-27 -44 -75 -44h-27q-47 0 -64 44l-353 922q-1 -25 -2.5 -48.5t-3.5 -44.5l-133 -1087h-169l182 1467h140q26 0 40.5 -4.5 t22.5 -27.5l356 -923v0z" />
<glyph unicode="N" horiz-adv-x="1424" d="M379 1467q26 0 37.5 -6t23.5 -26l633 -1122q2 24 4 45t4 40l131 1069h169l-180 -1467h-96q-24 0 -39 7.5t-27 28.5l-631 1122q0 -5 -0.5 -14t-2 -19.5t-2.5 -21t-2 -18.5l-133 -1085h-169l182 1467h98v0z" />
<glyph unicode="O" horiz-adv-x="1500" d="M1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49t184.5 -135t116.5 -204t40.5 -258zM1260 834q0 112 -29 202 t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5z" />
<glyph unicode="P" horiz-adv-x="1155" d="M375 551l-68 -551h-190l179 1467h404q117 0 203 -27.5t142 -78.5t83.5 -123t27.5 -160q0 -116 -38 -212.5t-110 -166t-178 -109t-242 -39.5h-213zM469 1317l-76 -615h213q87 0 153.5 27t111.5 74.5t69 113.5t24 144q0 59 -17.5 106t-52.5 80.5t-88 51.5t-124 18h-213z " />
<glyph unicode="Q" horiz-adv-x="1499" d="M1456 838q0 -124 -26 -234.5t-74 -205t-116.5 -170.5t-152.5 -130l296 -402h-154q-35 0 -62 9.5t-46 35.5l-201 279q-55 -17 -113 -26t-120 -9q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49 t184.5 -135t116.5 -204t40.5 -258zM1260 834q0 112 -29 202t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5z" />
<glyph unicode="R" horiz-adv-x="1216" d="M383 615l-76 -615h-190l179 1467h386q117 0 202.5 -24.5t141 -70t83 -110.5t27.5 -145q0 -89 -27 -166t-77 -137.5t-120.5 -103t-159.5 -63.5q33 -23 54 -61l316 -586h-170q-50 0 -71 41l-277 528q-13 26 -30.5 36t-55.5 10h-135zM470 1317l-70 -562h188q87 0 153.5 24.5 t112 69t68.5 105.5t23 133q0 112 -70 171t-210 59h-195z" />
<glyph unicode="S" horiz-adv-x="1000" d="M948 1240q-11 -14 -21.5 -22t-24.5 -8q-17 0 -36.5 18t-50 39t-73.5 39t-107 18q-66 0 -117 -20.5t-86.5 -56t-54 -82.5t-18.5 -101q0 -53 25 -89.5t64.5 -62.5t91.5 -46t105 -41t105 -46.5t92 -63.5t64.5 -91.5t24.5 -128.5q0 -104 -36 -197t-103 -162.5t-162 -110.5 t-214 -41q-131 0 -234 51.5t-170 139.5l68 91q8 11 20.5 18.5t26.5 7.5q19 0 41.5 -23t57.5 -51.5t86.5 -51.5t127.5 -23q71 0 128 22.5t96 62.5t60.5 96t21.5 123q0 56 -24.5 94t-64.5 63.5t-90.5 44.5t-104 38.5t-104.5 44t-91 62t-64.5 93t-24.5 136.5q0 90 33 173 t94.5 146.5t149.5 101.5t198 38q113 0 203 -43t149 -118z" />
<glyph unicode="T" horiz-adv-x="1113" d="M1205 1467l-20 -160h-433l-160 -1307h-191l160 1307h-435l19 160h1060z" />
<glyph unicode="U" horiz-adv-x="1375" d="M645 151q82 0 151 31.5t121 87.5t85 133.5t45 169.5l109 894h192l-109 -894q-15 -126 -66 -234t-130.5 -187t-185.5 -124t-232 -45q-114 0 -202 36.5t-149 102.5t-92.5 157.5t-31.5 199.5q0 46 6 94l108 894h192l-110 -893q-2 -20 -3.5 -39.5t-1.5 -37.5q0 -77 20 -140.5 t58.5 -109.5t95 -71t130.5 -25z" />
<glyph unicode="V" horiz-adv-x="1289" d="M127 1467h153q26 0 40 -13t19 -33l245 -1048q8 -33 15.5 -72.5t12.5 -82.5q14 43 30 82t33 73l501 1048q8 16 27 31t43 15h154l-730 -1467h-173z" />
<glyph unicode="W" horiz-adv-x="1927" d="M132 1467h150q26 0 41 -12.5t18 -33.5l158 -1038q2 -14 3.5 -34.5t3.5 -40.5l2 -46q10 33 21 63.5t22 57.5l439 1038q8 17 26.5 31.5t43.5 14.5h44q26 0 41 -12.5t19 -33.5l184 -1038q5 -27 8 -56.5t5 -62.5q10 33 19 62.5t19 56.5l412 1038q7 17 27.5 31.5t44.5 14.5 h150l-601 -1467h-174l-198 1129q-3 16 -5.5 36t-4.5 40q-7 -20 -14 -39.5t-14 -36.5l-476 -1129h-172z" />
<glyph unicode="X" horiz-adv-x="1220" d="M512 783l-345 684h173q22 0 31 -7t16 -21l262 -553q9 19 23 38l372 514q11 14 22 21.5t27 7.5h197l-513 -675l387 -792h-172q-20 0 -32 12t-18 25l-299 640q-8 -17 -18 -31l-442 -609q-27 -37 -63 -37h-192z" />
<glyph unicode="Y" horiz-adv-x="1192" d="M702 587l-72 -587h-192l73 585l-387 882h170q26 0 39 -12.5t21 -32.5l244 -587q10 -30 17.5 -56.5t12.5 -53.5q12 27 27 53.5t32 56.5l390 587q11 16 28 30.5t42 14.5h159z" />
<glyph unicode="Z" horiz-adv-x="1175" d="M1224 1467l-9 -61q-2 -14 -8 -26t-15 -24l-897 -1200h756l-21 -156h-1014l9 60q2 14 7.5 25.5t14.5 24.5l897 1201h-730l19 156h991z" />
<glyph unicode="[" horiz-adv-x="565" d="M34 -294l228 1863h351l-8 -69q-2 -20 -17.5 -33.5t-36.5 -13.5h-155l-201 -1630h156q19 0 31 -11.5t12 -28.5q0 -1 -0.5 -7t-1.5 -15t-2.5 -18.5t-2.5 -18.5l-2 -18h-351z" />
<glyph unicode="\" horiz-adv-x="710" d="M111 1486h74q33 0 55 -17.5t30 -47.5l370 -1514h-74q-29 0 -54.5 17.5t-33.5 49.5z" />
<glyph unicode="]" horiz-adv-x="565" d="M214 1569h352l-229 -1863h-352l9 69q2 20 17 34t37 14h156l200 1630h-155q-20 0 -32 12t-12 29l6 49z" />
<glyph unicode="^" d="M588 1467h117l367 -661h-133q-17 0 -29.5 10t-20.5 25l-201 360q-13 24 -23 46t-17 43q-14 -45 -38 -89l-197 -360q-8 -14 -20.5 -24.5t-33.5 -10.5h-139z" />
<glyph unicode="_" horiz-adv-x="743" d="M660 -172l-15 -119h-742l14 119h743z" />
<glyph unicode="`" horiz-adv-x="581" d="M246 1482q32 0 46 -10.5t24 -32.5l110 -248h-101q-20 0 -32.5 6t-24.5 22l-189 263h167z" />
<glyph unicode="a" horiz-adv-x="1034" d="M836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137q50 0 98.5 31.5 t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247z" />
<glyph unicode="b" horiz-adv-x="1075" d="M81 0l182 1508h179l-90 -733q39 62 84 114t94.5 88t102.5 56t108 20q132 0 206 -96.5t74 -281.5q0 -84 -17.5 -168t-49.5 -160.5t-78 -142.5t-101.5 -114.5t-121 -76t-137.5 -27.5q-83 0 -150 34.5t-110 99.5l-13 -67q-8 -27 -21 -40t-44 -13h-97zM673 906 q-49 0 -99.5 -30.5t-97 -85t-87.5 -129t-71 -163.5l-32 -270q39 -55 93 -78t110 -23q53 0 100.5 22.5t86.5 61t69.5 90t51.5 110.5t32.5 122t11.5 124q0 122 -44 185.5t-124 63.5z" />
<glyph unicode="c" horiz-adv-x="886" d="M839 186q-53 -58 -101 -97t-96 -62t-100 -32t-111 -9q-90 0 -159 30.5t-115.5 86.5t-71 134t-24.5 173q0 126 39.5 241.5t110.5 204.5t168 142.5t214 53.5q102 0 172 -36.5t122 -106.5l-58 -68q-6 -8 -15 -13.5t-20 -5.5q-14 0 -28 13t-36 29t-55.5 28.5t-85.5 12.5 q-71 0 -134 -38.5t-110 -105t-74 -157.5t-27 -195q0 -63 14 -115t43 -88.5t70.5 -56.5t96.5 -20q46 0 82 9.5t63.5 24t48.5 31.5t38 31.5t30.5 24t28.5 9.5q20 0 35 -17l45 -56v0z" />
<glyph unicode="d" horiz-adv-x="1077" d="M774 0q-36 0 -48.5 18.5t-12.5 44.5l19 217q-39 -67 -85 -121.5t-96.5 -92.5t-105.5 -59t-111 -21q-133 0 -206.5 96.5t-73.5 281.5q0 84 17.5 167.5t50 160.5t78 143t101 114t121 76t137.5 28q76 0 139 -30t107 -85l71 570h179l-187 -1508h-94zM402 132q49 0 99 30.5 t96.5 84t87 128t70.5 162.5l33 274q-39 54 -93 76.5t-109 22.5q-81 0 -146 -48t-110.5 -124t-70.5 -170.5t-25 -185.5q0 -122 44 -186t124 -64z" />
<glyph unicode="e" horiz-adv-x="944" d="M902 815q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q-1 -11 -1 -22v-22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42.5 33t35.5 25t33 10q18 0 34 -17l45 -57q-52 -54 -102 -92.5t-102 -64t-109 -37t-123 -11.5q-89 0 -159.5 29.5 t-119.5 84.5t-76 132.5t-27 174.5q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80zM589 921q-65 0 -119 -27t-96 -73.5t-72 -109.5t-46 -136q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5 t-46.5 27.5t-69.5 11z" />
<glyph unicode="f" horiz-adv-x="619" d="M383 886l-103 -852l-48 -212q-17 -77 -94 -77h-71l138 1138l-103 13q-17 5 -27 14.5t-10 26.5l8 80h148l12 100q11 89 44.5 158.5t84.5 117t116 72t139 24.5q63 0 116 -21l-17 -94q-2 -14 -14 -17.5t-30 -3.5q-9 0 -20 0.5t-23 0.5q-44 0 -81.5 -13t-66 -41.5t-48 -74.5 t-27.5 -113l-13 -95h259l-17 -131h-252z" />
<glyph unicode="g" horiz-adv-x="951" d="M836 750q-1 -51 -13 -92.5t-29.5 -76t-38 -65.5t-38 -61.5t-29 -63t-11.5 -69.5q0 -43 21 -78t46 -72t46 -80.5t21 -102.5q0 -73 -34 -138t-96.5 -115t-152 -79.5t-200.5 -29.5q-89 0 -162.5 18.5t-125 52.5t-80 81.5t-28.5 104.5q0 81 42 139.5t111 96.5t156.5 55.5 t178.5 17.5q37 0 73 -3.5t72 -8.5q-9 19 -14.5 40.5t-5.5 45.5q0 32 9.5 66t32.5 76q-39 -27 -87 -41.5t-110 -14.5q-59 0 -111 19.5t-90.5 57.5t-61.5 95t-23 132q0 74 27.5 145t79.5 127t129 90t177 34q113 0 196 -54h281l-8 -60q-2 -17 -11.5 -31.5t-29.5 -17.5l-129 -23 q9 -27 14.5 -55.5t5.5 -61.5zM440 468q55 0 97.5 24t71.5 63.5t44.5 88.5t15.5 99q0 90 -45.5 136.5t-125.5 46.5q-55 0 -97.5 -22.5t-71 -60.5t-43.5 -86t-15 -101q0 -92 44.5 -140t124.5 -48zM645 -26q0 27 -5 50t-13 44q-55 9 -104 13t-92 4q-84 0 -145.5 -15 t-101.5 -39.5t-59.5 -56.5t-19.5 -67q0 -74 62 -113t176 -39q68 0 123.5 18t95 47.5t61.5 69.5t22 84z" />
<glyph unicode="h" horiz-adv-x="1085" d="M81 0l182 1508h182l-85 -700q81 121 180 183t202 62q118 0 181.5 -78t63.5 -226q0 -20 -1 -42.5t-4 -46.5l-78 -660h-182l78 660q2 20 3.5 39.5t1.5 36.5q0 84 -31.5 125t-98.5 41q-46 0 -95 -25t-95 -71t-86 -111t-69 -145l-67 -550h-182z" />
<glyph unicode="i" horiz-adv-x="498" d="M401 1037l-125 -1037h-179l125 1037h179zM470 1363q0 -27 -10.5 -50t-28.5 -41t-40.5 -28t-47.5 -10q-24 0 -46.5 10t-39 28t-27 41t-10.5 50t10.5 50.5t27.5 41.5t40 28.5t46 10.5q25 0 47.5 -10t40.5 -28t28 -42t10 -51z" />
<glyph unicode="j" horiz-adv-x="486" d="M400 1037l-137 -1114q-8 -62 -31 -116t-62 -94t-92.5 -63t-122.5 -23q-36 0 -61.5 5.5t-50.5 15.5l19 98q5 13 12 17t22 4q7 0 15.5 -0.5t19.5 -0.5q73 0 108.5 37t45.5 120l136 1114h179zM465 1363q0 -27 -11 -50t-28.5 -41t-41 -28t-46.5 -10q-25 0 -47 10t-38.5 28 t-27 41t-10.5 50t10.5 50.5t27.5 41.5t39 28.5t47 10.5t47.5 -10t40 -28t28 -42t10.5 -51z" />
<glyph unicode="k" horiz-adv-x="982" d="M444 1508l-108 -887h32q20 0 33.5 5.5t29.5 22.5l343 351q16 16 31.5 26.5t40.5 10.5h161l-401 -406q-15 -16 -30 -30t-32 -24q16 -12 27.5 -29t22.5 -36l311 -512h-156q-23 0 -37 8t-25 28l-264 425q-13 20 -26 27.5t-42 7.5h-34l-61 -496h-180l185 1508h179z" />
<glyph unicode="l" horiz-adv-x="486" d="M90 0l183 1508h180l-184 -1508h-179z" />
<glyph unicode="m" horiz-adv-x="1604" d="M73 0l125 1038h91q61 0 61 -61l-14 -185q77 129 168 195t192 66q105 0 155.5 -72.5t50.5 -206.5q78 142 175 210.5t203 68.5q115 0 171.5 -75t56.5 -215q0 -24 -2 -49.5t-5 -53.5l-78 -660h-179l78 660q6 50 6 92q0 77 -25.5 113t-85.5 36q-44 0 -88.5 -22t-85.5 -64.5 t-76 -105.5t-63 -145l-66 -564h-181l79 660q3 28 5 52.5t2 46.5q0 73 -24 107.5t-84 34.5q-49 0 -94.5 -24.5t-86 -71t-75.5 -113.5t-64 -151l-64 -541h-178z" />
<glyph unicode="n" horiz-adv-x="1077" d="M73 0l124 1038h91q61 0 61 -61l-15 -201q83 137 187 207t215 70q116 0 180 -78t64 -227q0 -20 -1 -42t-4 -46l-78 -660h-182l78 660q2 20 3.5 39.5t1.5 36.5q0 84 -31.5 124.5t-97.5 40.5q-49 0 -100 -26.5t-98 -75t-87.5 -118t-69.5 -154.5l-59 -527h-182z" />
<glyph unicode="o" horiz-adv-x="1029" d="M459 128q75 0 136 41t104 109t67 157t24 187q0 143 -56.5 215.5t-163.5 72.5q-76 0 -136.5 -40.5t-103.5 -108.5t-66.5 -157t-23.5 -186q0 -143 55.5 -216.5t163.5 -73.5zM442 -14q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5q0 131 41 246.5t112.5 202.5t168 137.5 t209.5 50.5q85 0 155.5 -29.5t121.5 -84.5t80 -135.5t29 -181.5q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5z" />
<glyph unicode="p" horiz-adv-x="1060" d="M29 -351l169 1389h91q61 0 61 -61l-17 -221q39 68 85 122.5t96.5 93.5t106 60t112.5 21q132 0 206 -96.5t74 -281.5q0 -84 -17.5 -168t-50 -160.5t-78 -142.5t-101 -114.5t-121 -76t-137.5 -27.5q-77 0 -140.5 30t-106.5 86l-55 -453h-177zM665 906q-49 0 -100 -30.5 t-98 -86t-87.5 -131t-70.5 -164.5l-33 -265q39 -56 94 -79t111 -23q53 0 100.5 22.5t86.5 61t69.5 90t51.5 110.5t32.5 122t11.5 124q0 122 -44 185.5t-124 63.5z" />
<glyph unicode="q" horiz-adv-x="1034" d="M792 -351h-115q-33 0 -46.5 19t-13.5 44q0 3 1.5 11.5t2.5 17t2 15.5t1 10l60 482q-36 -60 -77 -108.5t-87 -82.5t-96 -52.5t-103 -18.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11 t123.5 -37zM389 137q49 0 97 31t91 85t79.5 129.5t65.5 164.5l46 363q-22 5 -44 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247z" />
<glyph unicode="r" horiz-adv-x="718" d="M73 0l125 1038h91q29 0 45 -13t16 -41q0 -4 -0.5 -11.5t-1 -16t-1 -14.5t-0.5 -7l-14 -189q67 143 149 221.5t173 78.5q43 0 86 -19l-32 -175q-48 19 -94 19q-99 0 -177 -96.5t-130 -293.5l-57 -481h-178z" />
<glyph unicode="s" horiz-adv-x="796" d="M740 871q-8 -12 -16 -17.5t-21 -5.5q-14 0 -31 11t-41 25t-58.5 26t-84.5 12q-46 0 -83.5 -13t-65.5 -35.5t-42.5 -52.5t-14.5 -64q0 -48 31.5 -77t79.5 -50t104 -40.5t104 -49t80 -75.5t32 -120q0 -72 -28.5 -137.5t-81 -115t-127 -79t-166.5 -29.5q-99 0 -175.5 35 t-124.5 91l45 69q8 13 19.5 20.5t28.5 7.5q16 0 33 -14.5t41.5 -32.5t61.5 -32t95 -14q50 0 90.5 15t68.5 41t43 60t15 73q0 52 -32 83t-80 51.5t-103 38.5t-103.5 46t-80 73t-31.5 120q0 66 26 126.5t75 107.5t119.5 75.5t158.5 28.5q93 0 164 -31.5t122 -84.5z" />
<glyph unicode="t" horiz-adv-x="701" d="M163 196q0 6 0.5 15.5t2 28t4.5 48t9 78.5l65 519h-116q-13 0 -22 7.5t-9 23.5q0 3 1 12.5t2.5 21.5l3 24t2.5 23l160 22l77 320q4 15 15.5 25t27.5 10h96l-45 -357h273l-16 -132h-273l-63 -510q-5 -42 -7.5 -67.5t-4 -41t-2 -21.5t-0.5 -9q0 -52 24 -77.5t66 -25.5 q27 0 47.5 7.5t35 17t25 17t18.5 7.5t13 -4t10 -13l38 -87q-49 -44 -113.5 -69t-128.5 -25q-99 0 -156.5 54.5t-59.5 157.5z" />
<glyph unicode="u" horiz-adv-x="1075" d="M362 1037l-77 -659q-2 -20 -3.5 -38.5t-1.5 -36.5q0 -84 31 -125t99 -41q47 0 96.5 25.5t96 73t86.5 114t70 148.5l64 539h182l-125 -1037h-90q-32 0 -49.5 14.5t-17.5 43.5q0 1 1 18.5t2.5 38t3 38t1.5 18.5l7 83q-83 -132 -185 -200t-211 -68q-116 0 -180 78.5 t-64 226.5q0 20 1 41.5t4 45.5l78 659h181z" />
<glyph unicode="v" horiz-adv-x="974" d="M77 1037h147q20 0 32.5 -11t15.5 -27l155 -667q8 -38 11.5 -76t5.5 -75q11 37 23.5 74.5t31.5 76.5l321 667q8 16 22.5 27t31.5 11h143l-520 -1037h-153z" />
<glyph unicode="w" horiz-adv-x="1453" d="M83 1037h136q20 0 33 -10t15 -28l95 -667q5 -36 6 -69.5t2 -65.5q12 33 25 66t27 69l284 672q6 15 19.5 25t31.5 10h76q20 0 31.5 -10t13.5 -25l115 -672q6 -36 10 -70t7 -67q10 33 19.5 67t24.5 70l262 667q6 16 20.5 27t32.5 11h131l-435 -1037h-138q-24 0 -30 35 l-126 703q-3 16 -5 33l-4 34q-5 -17 -10.5 -33.5t-12.5 -34.5l-300 -702q-14 -35 -41 -35h-133z" />
<glyph unicode="x" horiz-adv-x="959" d="M369 558l-249 479h163q20 0 29 -5.5t16 -19.5l174 -357q5 11 10.5 21.5t13.5 21.5l235 311q11 14 20.5 21t22.5 7h165l-371 -480l276 -557h-162q-20 0 -31.5 11t-18.5 25l-196 415q-10 -23 -22 -39l-282 -377q-10 -14 -22.5 -24.5t-30.5 -10.5h-160z" />
<glyph unicode="y" horiz-adv-x="974" d="M324 -309q-22 -42 -64 -42h-133l224 412l-268 976h150q22 0 32.5 -10.5t15.5 -27.5l166 -653q5 -22 8.5 -43t5.5 -43q9 22 17.5 44t20.5 43l327 654q8 16 23 26t31 10h144z" />
<glyph unicode="z" horiz-adv-x="878" d="M868 959q-2 -19 -11 -37t-20 -32l-598 -748h512l-17 -142h-731l9 76q2 13 11 31t21 34l601 753h-504l18 143h719z" />
<glyph unicode="{" horiz-adv-x="565" d="M162 464q0 53 -29 87t-83 34l13 106q76 0 113.5 61t37.5 188q0 23 -1.5 50t-3 55t-3 55t-1.5 50q0 99 23 177t68.5 131.5t113 82t155.5 28.5h50l-9 -79q-2 -20 -18 -28.5t-26 -8.5h-18q-40 0 -74 -15.5t-58 -50.5t-37.5 -92t-13.5 -140v-50.5t0.5 -62.5t0.5 -63v-50 q0 -79 -17 -132t-43.5 -86t-56.5 -50t-56 -24q54 -17 87 -64t33 -111q0 -61 -15 -119.5t-33 -117.5t-33.5 -119t-15.5 -124q0 -72 34.5 -115.5t96.5 -43.5h19q10 0 22 -8t12 -25q0 -3 -1 -10t-1.5 -15t-1.5 -14t-1 -7l-4 -38h-51q-71 0 -123 20.5t-86.5 57t-52 86.5 t-17.5 110q0 68 16.5 129t36 120t36 117t16.5 118z" />
<glyph unicode="|" horiz-adv-x="614" d="M175 1570h141v-1921h-141v1921z" />
<glyph unicode="}" horiz-adv-x="565" d="M437 812q0 -53 29 -87t83 -34l-13 -106q-76 0 -113.5 -61t-37.5 -188q0 -23 1.5 -50t3 -55t3 -55.5t1.5 -49.5q0 -99 -23 -177t-68.5 -132t-113 -82t-155.5 -28h-50l9 79q1 10 6 17t11.5 11.5t14 6.5t12.5 2h18q40 0 74 15.5t58 50.5t37.5 92t13.5 140v50t-0.5 63 t-0.5 62.5v50.5q0 78 17 131t43.5 86.5t56.5 50.5t56 24q-54 17 -87 64t-33 111q0 61 15.5 119.5t33 117.5t33 119t15.5 124q0 72 -34.5 115.5t-96.5 43.5h-19q-10 0 -22 7.5t-12 25.5q0 3 1 10t1.5 15t1.5 14t1 7l4 38h51q71 0 123 -20.5t86.5 -57t52 -86.5t17.5 -110 q0 -68 -16.5 -129t-36 -120t-36 -117t-16.5 -118z" />
<glyph unicode="~" d="M717 628q67 0 104 43.5t38 114.5h148q0 -69 -19 -126t-54.5 -98t-87.5 -63.5t-118 -22.5q-53 0 -105.5 16.5t-101 36.5t-92.5 37t-81 17q-67 0 -104 -43t-38 -116h-148q0 69 19 126t54.5 98t87 64t118.5 23q53 0 105.5 -17t101 -36.5t92.5 -36.5t81 -17z" />
<glyph unicode="&#xa1;" horiz-adv-x="650" d="M152 -352l68 555q6 46 13 89.5t14.5 88t16.5 91.5t20 100h106q-2 -54 -4.5 -101t-5.5 -91t-6.5 -87.5t-9.5 -89.5l-68 -555h-144zM249 925q0 27 9.5 50t27 40.5t40.5 28t51 10.5q27 0 50 -10.5t40 -28t27.5 -41t10.5 -49.5q0 -28 -10.5 -51t-27.5 -40.5t-40.5 -27 t-49.5 -9.5q-28 0 -51 9.5t-40.5 27t-27 40.5t-9.5 51z" />
<glyph unicode="&#xa2;" d="M515 -10q-84 9 -153 44t-118 92.5t-76 137t-27 177.5q0 129 39.5 239.5t113.5 191t181 128t242 50.5l35 185q4 19 19 34t36 15h65l-46 -240q84 -11 148 -43t113 -82l-53 -62q-8 -11 -16.5 -15.5t-23.5 -4.5q-12 0 -27.5 9t-37.5 21.5t-53 24.5t-74 19l-151 -789 q66 4 112.5 20t78.5 33.5t54 31t38 13.5q23 0 32 -15l40 -62q-34 -36 -76.5 -63.5t-92 -47t-103 -30.5t-108.5 -14l-34 -182q-4 -19 -19 -34t-36 -15h-66zM314 450q0 -135 59.5 -217.5t168.5 -104.5l149 786q-92 -6 -162 -40.5t-117.5 -95t-72.5 -144t-25 -184.5z" />
<glyph unicode="&#xa3;" d="M911 677q-2 -17 -17.5 -32t-39.5 -15h-433l-32 -262q-9 -77 -36.5 -131.5t-77.5 -97.5q30 6 61 10t60 4h703l-9 -75q-2 -13 -9 -27t-18.5 -25.5t-26.5 -18.5t-32 -7h-989l15 115q36 10 65 26t51 40t36 58t20 82l37 309h-167l8 71q2 18 18 33t39 15h116l34 271 q12 96 53 180.5t107 147t156 98.5t202 36q86 0 149 -19.5t108 -53.5t74 -80.5t48 -100.5l-77 -45q-20 -10 -41 -11q-14 0 -26.5 5.5t-20.5 19.5q-17 28 -35 52.5t-44 43t-62.5 28.5t-90.5 10q-67 0 -121.5 -22t-96 -62.5t-67.5 -97.5t-34 -128l-33 -272h484l-8 -72v0z" />
<glyph unicode="&#xa5;" d="M167 639h317l-317 826h147q25 0 39.5 -11.5t21.5 -32.5l216 -586q12 -34 17 -64t9 -58q9 29 21 59t33 63l359 586q11 17 28.5 30.5t42.5 13.5h148l-522 -826h317l-11 -101h-351l-13 -108h350l-12 -101h-351l-40 -329h-176l40 329h-351l13 101h351l13 108h-351z" />
<glyph unicode="&#xa7;" horiz-adv-x="949" d="M870 1297q-15 -23 -39 -22q-15 0 -32 11.5t-41 25.5t-57.5 26t-83.5 12q-48 0 -86 -14.5t-66 -38t-42.5 -55.5t-14.5 -66q0 -47 34.5 -80.5t87 -62t112.5 -57.5t112.5 -67t87.5 -89t35 -125q0 -93 -46 -166t-143 -115q38 -34 61 -77.5t23 -102.5q0 -76 -27.5 -141.5 t-80 -115t-127.5 -77.5t-171 -28q-99 0 -177.5 35t-128.5 90l50 68q9 13 20 19t30 6q18 0 35 -14.5t41.5 -31.5t62 -31.5t96.5 -14.5q51 0 91.5 15t69 41t43 61.5t14.5 77.5t-21 72.5t-54.5 55t-77 45t-89 42t-89 46t-77.5 57t-55 74.5t-21 99q0 91 49.5 163t155.5 111 q-38 35 -61.5 80t-23.5 109q0 67 26 127t75.5 106t121 73.5t160.5 27.5q93 0 164.5 -32t119.5 -87zM290 728q0 -48 29.5 -82t75.5 -62.5t102.5 -54t108.5 -56.5q59 30 85 74t26 98q0 50 -27.5 85t-71.5 63t-96.5 52.5t-102.5 53.5q-71 -36 -100 -77t-29 -94z" />
<glyph unicode="&#xa8;" horiz-adv-x="581" d="M327 1342q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5t-24.5 36t-9.5 43.5q0 24 9.5 45.5t24.5 37.5t35.5 25t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM694 1342q0 -24 -9 -44t-25 -35.5t-37 -24.5t-44 -9q-24 0 -45 9t-36.5 24.5 t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1635" d="M1109 485q9 0 13.5 -3t10.5 -8l61 -65q-58 -68 -142.5 -105.5t-203.5 -37.5q-100 0 -182.5 35t-141.5 97.5t-91.5 148.5t-32.5 188q0 103 35 190t97.5 149t148 96t185.5 34q112 0 189.5 -34t135.5 -92l-47 -66q-5 -6 -13.5 -12.5t-19.5 -6.5q-14 0 -30 11.5t-41.5 25 t-65 25.5t-98.5 12q-73 0 -130.5 -23t-98.5 -66t-62.5 -104.5t-21.5 -138.5q0 -79 21.5 -141t60 -103.5t91 -64t116.5 -22.5q65 0 109 13t73 28t46 27.5t29 12.5zM95 733q0 103 26.5 199t76 179.5t117.5 151.5t151 117.5t178 76t199 26.5q103 0 199 -26.5t179 -76 t151.5 -117.5t117 -151.5t75 -179t26.5 -199.5q0 -102 -26.5 -198t-75 -179t-117 -151t-151.5 -117t-179 -75.5t-199 -26.5q-102 0 -198 26.5t-179 75.5t-151 117t-117.5 151t-76 178.5t-26.5 198.5zM199 733q0 -91 22.5 -175t64 -157t100 -132.5t130 -102t154.5 -65.5 t173 -23t173.5 23t155.5 65.5t131 102t101 132.5t65 157t23 175q0 137 -50.5 257.5t-138.5 210t-206.5 141.5t-253.5 52q-90 0 -173 -23.5t-154.5 -66t-130 -103t-100 -134t-64 -158.5t-22.5 -176z" />
<glyph unicode="&#xaa;" horiz-adv-x="647" d="M549 860q-17 0 -27.5 4.5t-12.5 24.5l-5 50q-26 -22 -49.5 -38t-48 -27.5t-51 -17t-58.5 -5.5q-67 0 -109 35.5t-42 104.5q0 41 19 79.5t63 68.5t117 49.5t181 22.5l5 39q1 7 1 13.5v12.5q0 47 -20 74.5t-70 27.5q-35 0 -59.5 -9t-42.5 -20t-33.5 -20t-31.5 -9 q-14 0 -22 7t-12 18l-18 40q55 51 116.5 75t131.5 24q45 0 80 -16t59 -42.5t36 -63t12 -77.5q0 -8 -0.5 -17.5t-1.5 -17.5l-48 -390h-59zM347 942q47 0 83.5 19.5t73.5 55.5l13 111q-68 -2 -115 -11t-76 -24.5t-42 -37t-13 -47.5q0 -35 21.5 -50.5t54.5 -15.5z" />
<glyph unicode="&#xab;" horiz-adv-x="896" d="M136 530l3 23l285 397l53 -27q24 -12 24 -35q0 -20 -16 -40l-179 -269q-16 -26 -33 -37q10 -10 24 -37l114 -269q6 -14 6 -28q0 -33 -33 -48l-60 -28zM419 530l3 23l285 397l53 -27q24 -12 23 -35q0 -20 -15 -40l-179 -269q-16 -26 -33 -37q10 -10 24 -37l113 -269 q6 -14 6 -28q0 -33 -32 -48l-61 -28z" />
<glyph unicode="&#xac;" d="M184 754h880l-53 -428h-151l37 295h-728z" />
<glyph unicode="&#xad;" horiz-adv-x="656" d="M118 688h468l-20 -149h-467z" />
<glyph unicode="&#xae;" horiz-adv-x="1635" d="M96 733q0 103 27 199t76 179.5t117 151.5t151.5 117.5t178.5 76t199 26.5q103 0 198.5 -26.5t179 -76t151.5 -117.5t117 -151.5t75.5 -179t26.5 -199.5q0 -102 -26.5 -198t-75.5 -179t-117 -151t-151.5 -117t-179 -75.5t-198.5 -26.5t-198.5 26.5t-179 75.5t-151.5 117 t-117 151t-76 178.5t-27 198.5zM200 733q0 -91 22.5 -175t64.5 -157t100 -132.5t130 -102t155 -65.5t173 -23t173 23t155 65.5t131 102t101 132.5t65 157t23 175q0 137 -50.5 257.5t-138 210t-206 141.5t-253.5 52t-253 -52t-205 -141.5t-137 -210t-50 -257.5zM697 641v-362 h-159v913h295q176 0 261 -64t85 -189q0 -96 -55 -163.5t-163 -93.5q17 -10 29 -26t26 -35l232 -342h-151q-34 0 -50 25l-206 309q-9 14 -22 21t-40 7h-82zM697 758h118q57 0 96.5 10.5t63.5 31t35 50.5t11 69q0 38 -10 66.5t-32 47t-57.5 27.5t-88.5 9h-136v-311z" />
<glyph unicode="&#xaf;" horiz-adv-x="581" d="M129 1377h543l-15 -115h-542z" />
<glyph unicode="&#xb0;" horiz-adv-x="791" d="M145 1155q0 69 26.5 129.5t71.5 105t105.5 70t129.5 25.5q71 0 132 -25.5t106 -70t70.5 -105t25.5 -129.5q0 -68 -25.5 -127.5t-70.5 -104t-106 -71t-132 -26.5q-70 0 -130 26.5t-105 71t-71.5 104t-26.5 127.5zM273 1154q0 -43 16 -80.5t43.5 -65t65 -43.5t80.5 -16 t80.5 16t65 43.5t43 65t15.5 80.5t-15.5 81t-43 66t-65 44t-80.5 16t-80.5 -16t-65 -44t-43.5 -66t-16 -81z" />
<glyph unicode="&#xb1;" d="M753 1228l-48 -388h420l-16 -134h-421l-46 -379h-143l46 379h-416l15 134h417l48 388h144zM68 216h980l-17 -134h-979z" />
<glyph unicode="&#xb4;" horiz-adv-x="581" d="M692 1482l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#xb5;" horiz-adv-x="1050" d="M356 1035l-85 -695q-2 -11 -2 -21v-20q0 -84 44 -129.5t127 -45.5q72 0 139.5 38t130.5 105l95 768h175l-127 -1035h-105q-18 0 -30 9t-12 28v110q-72 -74 -141 -108t-151 -34q-67 0 -115 24t-79 68v-16q0 -37 -2.5 -74t-6.5 -69l-36 -290h-87q-33 0 -52 16.5t-19 45.5 v6.5t1 7.5l162 1311h176z" />
<glyph unicode="&#xb6;" horiz-adv-x="1371" d="M1451 1467l-19 -156h-226l-185 -1517h-161l187 1517h-284l-186 -1517h-161l108 882q-95 0 -172 25.5t-130.5 71.5t-82.5 110.5t-29 143.5q0 92 36.5 172.5t103.5 140t161.5 93.5t210.5 34h829z" />
<glyph unicode="&#xb7;" horiz-adv-x="560" d="M152 607q0 32 11.5 60.5t32.5 49t48.5 33t58.5 12.5q32 0 60 -12.5t48.5 -33t33 -49t12.5 -60.5q0 -31 -12.5 -58.5t-33 -48.5t-48.5 -32.5t-60 -11.5q-31 0 -58.5 11.5t-48.5 32.5t-32.5 48.5t-11.5 58.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="581" d="M71 -255q6 0 13 -4t17.5 -8.5t23.5 -9t34 -4.5q41 0 63 20.5t22 51.5q0 36 -36.5 51t-104.5 24l60 144h109l-32 -84q76 -17 110 -49.5t34 -79.5q0 -37 -17 -66t-47 -49t-71.5 -30.5t-90.5 -10.5q-39 0 -73 8.5t-61 23.5l23 55q9 17 24 17z" />
<glyph unicode="&#xba;" horiz-adv-x="720" d="M481 1483q59 0 107 -19t81 -54.5t50.5 -85t17.5 -111.5q0 -84 -24.5 -151.5t-68 -115t-105 -73t-135.5 -25.5q-60 0 -108 19t-81.5 54t-51.5 85.5t-18 111.5q0 83 25 150.5t69.5 115.5t106 73.5t135.5 25.5zM418 953q92 0 137.5 70.5t45.5 189.5q0 77 -33 120.5t-98 43.5 q-51 0 -86.5 -19.5t-57.5 -53.5t-32 -82t-10 -106q0 -77 32.5 -120t101.5 -43z" />
<glyph unicode="&#xbb;" horiz-adv-x="896" d="M485 553l-2 -23l-283 -398l-55 28q-24 11 -23 36q0 16 13 40l182 269q16 25 31 36q-13 12 -22 38l-115 269q-6 13 -6 26q0 33 34 49l60 27zM768 553l-2 -23l-284 -398l-54 28q-24 11 -24 36q0 16 14 40l182 269q16 25 31 36q-13 12 -23 38l-114 269q-6 13 -7 26 q0 33 34 49l61 27z" />
<glyph unicode="&#xbf;" horiz-adv-x="754" d="M644 -218q-34 -30 -73 -56.5t-83.5 -46.5t-94.5 -32.5t-107 -12.5q-70 0 -127 20t-98.5 57t-64 90t-22.5 119q0 83 24.5 142t61.5 101.5t81.5 73t84.5 58t70 55t39 64.5l37 158h120l-10 -172q-3 -46 -28 -79.5t-62 -63.5t-79.5 -58.5t-78 -63t-59.5 -78.5t-24 -104 q0 -42 13 -74.5t36 -54.5t53.5 -33t63.5 -11q56 0 99 15.5t73.5 33.5t50.5 33.5t32 15.5q23 0 34 -21zM343 925q0 27 9.5 50t26.5 40.5t40 28t51 10.5q27 0 50.5 -10.5t41 -28t27 -41t9.5 -49.5q0 -28 -9.5 -51t-27 -40.5t-41 -27t-50.5 -9.5q-28 0 -51 9.5t-40 27 t-26.5 40.5t-9.5 51z" />
<glyph unicode="&#xc0;" horiz-adv-x="1266" d="M0 0zM1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61zM629 1824q32 0 46 -7t30 -26l172 -208h-148q-20 0 -31.5 6.5t-26.5 19.5 l-241 215h199z" />
<glyph unicode="&#xc1;" horiz-adv-x="1266" d="M0 0zM1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61zM1152 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5 t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#xc2;" horiz-adv-x="1266" d="M0 0zM1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61zM1071 1583h-134q-11 0 -24 3t-21 10l-117 110l-145 -110q-9 -7 -23 -10t-26 -3 h-134l252 214h173z" />
<glyph unicode="&#xc3;" horiz-adv-x="1266" d="M0 0zM1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61zM895 1700q31 0 52 21.5t26 59.5h96q-5 -43 -20.5 -79.5t-39 -63.5t-55 -42 t-69.5 -15q-33 0 -61.5 14t-54.5 30t-49.5 30t-44.5 14q-31 0 -51 -23t-25 -61h-99q5 43 21 80t39.5 64t55 42t69.5 15q34 0 62.5 -13.5t54 -30t48.5 -29.5t45 -13z" />
<glyph unicode="&#xc4;" horiz-adv-x="1266" d="M0 0zM1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61zM687 1705q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5t-40 -7.5q-20 0 -38.5 7.5 t-32.5 21.5t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23t23 -33.5t8.5 -40.5zM1039 1705q0 -20 -8 -38.5t-22.5 -32.5t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5t32.5 23t40 8t40 -8t32.5 -23 t22.5 -33.5t8 -40.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1266" d="M0 0zM1208 0h-147q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61zM568 1699q0 40 15.5 73.5t42 57.5t61.5 37.5t74 13.5q40 0 75 -13.5t62 -37.5 t42.5 -57.5t15.5 -73.5q0 -39 -15.5 -72t-42.5 -56.5t-62 -36.5t-75 -13q-39 0 -74 13t-61.5 36.5t-42 56.5t-15.5 72zM659 1699q0 -44 28 -73.5t76 -29.5q46 0 74 29.5t28 73.5q0 46 -28 74.5t-74 28.5q-48 0 -76 -28.5t-28 -74.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1745" d="M791 1467h976l-18 -156h-709v-496h525l-20 -150h-505v-509h568l-21 -156h-727v404h-517l-210 -358q-11 -18 -30.5 -32t-44.5 -14h-148zM425 544h435v767q-14 -37 -29.5 -70t-32.5 -62z" />
<glyph unicode="&#xc7;" horiz-adv-x="1263" d="M519 -255q6 0 13.5 -4t17.5 -8.5t23.5 -9t33.5 -4.5q41 0 63 20.5t22 51.5q0 36 -36 51t-104 24l49 121q-117 11 -209.5 62t-157 134t-98.5 194.5t-34 243.5q0 191 59.5 349.5t163.5 273t244.5 177.5t301.5 63q78 0 141.5 -13.5t116 -39t95 -61.5t79.5 -80l-66 -80 q-8 -10 -18.5 -16.5t-25.5 -6.5q-18 0 -38.5 20.5t-56 44.5t-91.5 44.5t-145 20.5q-119 0 -222 -47.5t-179 -136.5t-119.5 -214.5t-43.5 -282.5q0 -114 29.5 -204t82.5 -153t125.5 -97t159.5 -34q68 0 120 11.5t92 28t68.5 36.5t49.5 36t35 27.5t25 11.5q9 0 15.5 -3.5 t10.5 -8.5l67 -83q-89 -96 -203.5 -153t-263.5 -65l-23 -60q76 -17 110.5 -49.5t34.5 -79.5q0 -37 -17 -66t-47.5 -49t-72 -30.5t-90.5 -10.5q-39 0 -72.5 8.5t-61.5 23.5l24 55q8 17 23 17z" />
<glyph unicode="&#xc8;" horiz-adv-x="1092" d="M99 0zM1096 1311h-642l-62 -495h520l-19 -150h-519l-63 -510h644l-19 -156h-837l182 1467h834zM585 1824q32 0 46 -7t30 -26l172 -208h-148q-20 0 -31.5 6.5t-26.5 19.5l-241 215h199z" />
<glyph unicode="&#xc9;" horiz-adv-x="1092" d="M99 0zM1096 1311h-642l-62 -495h520l-19 -150h-519l-63 -510h644l-19 -156h-837l182 1467h834zM1092 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#xca;" horiz-adv-x="1092" d="M99 0zM1096 1311h-642l-62 -495h520l-19 -150h-519l-63 -510h644l-19 -156h-837l182 1467h834zM1027 1583h-134q-11 0 -24 3t-21 10l-117 110l-145 -110q-9 -7 -23 -10t-26 -3h-134l252 214h173z" />
<glyph unicode="&#xcb;" horiz-adv-x="1092" d="M99 0zM1096 1311h-642l-62 -495h520l-19 -150h-519l-63 -510h644l-19 -156h-837l182 1467h834zM643 1705q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5t-40 -7.5q-20 0 -38.5 7.5t-32.5 21.5t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23 t23 -33.5t8.5 -40.5zM995 1705q0 -20 -8 -38.5t-22.5 -32.5t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5t32.5 23t40 8t40 -8t32.5 -23t22.5 -33.5t8 -40.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="579" d="M88 0zM325 0h-193l179 1467h193zM287 1824q32 0 46 -7t30 -26l172 -208h-148q-20 0 -31.5 6.5t-26.5 19.5l-241 215h199z" />
<glyph unicode="&#xcd;" horiz-adv-x="579" d="M132 0zM325 0h-193l179 1467h193zM794 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#xce;" horiz-adv-x="579" d="M105 0zM325 0h-193l179 1467h193zM729 1583h-134q-11 0 -24 3t-21 10l-117 110l-145 -110q-9 -7 -23 -10t-26 -3h-134l252 214h173z" />
<glyph unicode="&#xcf;" horiz-adv-x="579" d="M132 0zM325 0h-193l179 1467h193zM345 1705q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5t-40 -7.5q-20 0 -38.5 7.5t-32.5 21.5t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23t23 -33.5t8.5 -40.5zM697 1705q0 -20 -8 -38.5t-22.5 -32.5 t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5t32.5 23t40 8t40 -8t32.5 -23t22.5 -33.5t8 -40.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1486" d="M85 796h183l82 671h506q135 0 243.5 -46t185 -129.5t117 -199.5t40.5 -255q0 -187 -57.5 -341.5t-160 -264.5t-242 -170.5t-305.5 -60.5h-506l83 683h-183zM1245 833q0 111 -28 199.5t-80.5 150.5t-128 95.5t-170.5 33.5h-315l-63 -516h356l-13 -113h-357l-64 -528h313 q123 0 224 46.5t173.5 134t112.5 213t40 284.5z" />
<glyph unicode="&#xd1;" horiz-adv-x="1424" d="M99 0zM379 1467q26 0 37.5 -6t23.5 -26l633 -1122q2 24 4 45t4 40l131 1069h169l-180 -1467h-96q-24 0 -39 7.5t-27 28.5l-631 1122q0 -5 -0.5 -14t-2 -19.5t-2.5 -21t-2 -18.5l-133 -1085h-169l182 1467h98v0zM972 1700q31 0 52 21.5t26 59.5h96q-5 -43 -20.5 -79.5 t-39 -63.5t-55 -42t-69.5 -15q-33 0 -61.5 14t-54.5 30t-49.5 30t-44.5 14q-31 0 -51 -23t-25 -61h-99q5 43 21 80t39.5 64t55 42t69.5 15q34 0 62.5 -13.5t54 -30t48.5 -29.5t45 -13z" />
<glyph unicode="&#xd2;" horiz-adv-x="1500" d="M102 0zM1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49t184.5 -135t116.5 -204t40.5 -258zM1260 834 q0 112 -29 202t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5zM743 1824q32 0 46 -7t30 -26l172 -208h-148 q-20 0 -31.5 6.5t-26.5 19.5l-241 215h199z" />
<glyph unicode="&#xd3;" horiz-adv-x="1500" d="M102 0zM1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49t184.5 -135t116.5 -204t40.5 -258zM1260 834 q0 112 -29 202t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5zM1250 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148 l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#xd4;" horiz-adv-x="1500" d="M102 0zM1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49t184.5 -135t116.5 -204t40.5 -258zM1260 834 q0 112 -29 202t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5zM1185 1583h-134q-11 0 -24 3t-21 10l-117 110l-145 -110 q-9 -7 -23 -10t-26 -3h-134l252 214h173z" />
<glyph unicode="&#xd5;" horiz-adv-x="1500" d="M102 0zM1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49t184.5 -135t116.5 -204t40.5 -258zM1260 834 q0 112 -29 202t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5zM1009 1700q31 0 52 21.5t26 59.5h96q-5 -43 -20.5 -79.5 t-39 -63.5t-55 -42t-69.5 -15q-33 0 -61.5 14t-54.5 30t-49.5 30t-44.5 14q-31 0 -51 -23t-25 -61h-99q5 43 21 80t39.5 64t55 42t69.5 15q34 0 62.5 -13.5t54 -30t48.5 -29.5t45 -13z" />
<glyph unicode="&#xd6;" horiz-adv-x="1500" d="M102 0zM1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5q0 187 58.5 344t161.5 271t244 177.5t305 63.5q135 0 243.5 -49t184.5 -135t116.5 -204t40.5 -258zM1260 834 q0 112 -29 202t-81.5 153.5t-129 98.5t-171.5 35q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5q0 -112 28.5 -202t81.5 -153.5t128.5 -98t171.5 -34.5q123 0 224 48.5t174 138t113.5 217t40.5 285.5zM801 1705q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5 t-40 -7.5q-20 0 -38.5 7.5t-32.5 21.5t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23t23 -33.5t8.5 -40.5zM1153 1705q0 -20 -8 -38.5t-22.5 -32.5t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5 t32.5 23t40 8t40 -8t32.5 -23t22.5 -33.5t8 -40.5z" />
<glyph unicode="&#xd8;" horiz-adv-x="1499" d="M1456 838q0 -125 -26.5 -237t-75 -206.5t-118 -170.5t-155 -129t-185 -81.5t-209.5 -28.5q-99 0 -183 25.5t-153 73.5l-111 -140q-25 -31 -56 -43.5t-59 -12.5h-76l216 272q-79 86 -121 205t-42 263q0 187 58.5 344t161.5 271t244 177.5t305 63.5q106 0 196 -30.5 t161 -85.5l91 115q11 14 20.5 24t19 15.5t21.5 8t28 2.5h98l-196 -246q71 -86 108.5 -200t37.5 -249zM298 633q0 -99 22 -180.5t64 -143.5l730 917q-51 47 -117.5 72t-147.5 25q-121 0 -222.5 -48.5t-174 -138.5t-113.5 -217.5t-41 -285.5zM1260 834q0 90 -18.5 165 t-53.5 136l-725 -911q49 -39 110 -59t135 -20q123 0 224 48.5t174 138t113.5 217t40.5 285.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1375" d="M150 0zM645 151q82 0 151 31.5t121 87.5t85 133.5t45 169.5l109 894h192l-109 -894q-15 -126 -66 -234t-130.5 -187t-185.5 -124t-232 -45q-114 0 -202 36.5t-149 102.5t-92.5 157.5t-31.5 199.5q0 46 6 94l108 894h192l-110 -893q-2 -20 -3.5 -39.5t-1.5 -37.5 q0 -77 20 -140.5t58.5 -109.5t95 -71t130.5 -25zM685 1824q32 0 46 -7t30 -26l172 -208h-148q-20 0 -31.5 6.5t-26.5 19.5l-241 215h199z" />
<glyph unicode="&#xda;" horiz-adv-x="1375" d="M150 0zM645 151q82 0 151 31.5t121 87.5t85 133.5t45 169.5l109 894h192l-109 -894q-15 -126 -66 -234t-130.5 -187t-185.5 -124t-232 -45q-114 0 -202 36.5t-149 102.5t-92.5 157.5t-31.5 199.5q0 46 6 94l108 894h192l-110 -893q-2 -20 -3.5 -39.5t-1.5 -37.5 q0 -77 20 -140.5t58.5 -109.5t95 -71t130.5 -25zM1192 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#xdb;" horiz-adv-x="1375" d="M150 0zM645 151q82 0 151 31.5t121 87.5t85 133.5t45 169.5l109 894h192l-109 -894q-15 -126 -66 -234t-130.5 -187t-185.5 -124t-232 -45q-114 0 -202 36.5t-149 102.5t-92.5 157.5t-31.5 199.5q0 46 6 94l108 894h192l-110 -893q-2 -20 -3.5 -39.5t-1.5 -37.5 q0 -77 20 -140.5t58.5 -109.5t95 -71t130.5 -25zM1127 1583h-134q-11 0 -24 3t-21 10l-117 110l-145 -110q-9 -7 -23 -10t-26 -3h-134l252 214h173z" />
<glyph unicode="&#xdc;" horiz-adv-x="1375" d="M150 0zM645 151q82 0 151 31.5t121 87.5t85 133.5t45 169.5l109 894h192l-109 -894q-15 -126 -66 -234t-130.5 -187t-185.5 -124t-232 -45q-114 0 -202 36.5t-149 102.5t-92.5 157.5t-31.5 199.5q0 46 6 94l108 894h192l-110 -893q-2 -20 -3.5 -39.5t-1.5 -37.5 q0 -77 20 -140.5t58.5 -109.5t95 -71t130.5 -25zM743 1705q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5t-40 -7.5q-20 0 -38.5 7.5t-32.5 21.5t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23t23 -33.5t8.5 -40.5zM1095 1705q0 -20 -8 -38.5 t-22.5 -32.5t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5t32.5 23t40 8t40 -8t32.5 -23t22.5 -33.5t8 -40.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1192" d="M124 0zM702 587l-72 -587h-192l73 585l-387 882h170q26 0 39 -12.5t21 -32.5l244 -587q10 -30 17.5 -56.5t12.5 -53.5q12 27 27 53.5t32 56.5l390 587q11 16 28 30.5t42 14.5h159zM1117 1823l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5 t27.5 1.5h199z" />
<glyph unicode="&#xde;" horiz-adv-x="1155" d="M515 1193h214q117 0 202.5 -27.5t141.5 -78.5t84 -122t28 -158q0 -116 -38 -212.5t-110.5 -167t-178 -109t-242.5 -38.5h-214l-33 -280h-191l179 1467h192zM497 1042l-76 -611h214q87 0 153.5 26.5t111.5 74.5t68.5 114t23.5 144q0 119 -69.5 185.5t-212.5 66.5h-213z " />
<glyph unicode="&#xdf;" horiz-adv-x="1123" d="M745 1488q91 0 156 -27.5t106.5 -70t60.5 -94.5t19 -100q0 -70 -24 -119t-59.5 -86t-77 -64.5t-77.5 -53.5t-60 -54.5t-24 -67.5q0 -36 22 -60t54.5 -45t71 -43t71.5 -53.5t55 -76.5t22 -112q0 -88 -32 -158t-85.5 -118.5t-125.5 -74.5t-152 -26q-91 0 -162 35t-121 91 l49 67q9 12 21 19.5t29 7.5t35 -15t41 -32.5t55.5 -32t81.5 -14.5q43 0 79.5 15.5t62.5 43.5t41 65t15 80q0 49 -23.5 81.5t-58.5 56t-76.5 44t-76 47t-58 63.5t-23.5 94q0 63 25 108.5t62.5 81t82.5 65t82.5 62.5t63 74t25.5 98q0 29 -11 59t-34 54.5t-60.5 40t-90.5 15.5 q-63 0 -119 -34.5t-99.5 -94t-73.5 -137.5t-40 -165l-110 -893l-48 -212q-17 -77 -94 -77h-71l139 1138l-113 14q-41 5 -41 39l8 79h167q24 94 72 179.5t115.5 151t151.5 104t179 38.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1034" d="M49 0zM836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137 q50 0 98.5 31.5t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247zM616 1482q32 0 46 -10.5t24 -32.5l110 -248h-101q-20 0 -32.5 6t-24.5 22l-189 263h167z" />
<glyph unicode="&#xe1;" horiz-adv-x="1034" d="M49 0zM836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137 q50 0 98.5 31.5t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247zM998 1482l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#xe2;" horiz-adv-x="1034" d="M49 0zM836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137 q50 0 98.5 31.5t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247zM966 1197h-117q-11 0 -21.5 4t-15.5 10l-102 131l-7 7t-7 9q-5 -5 -9 -8.5t-8 -7.5l-136 -131 q-6 -5 -17.5 -9.5t-23.5 -4.5h-122l245 268h164z" />
<glyph unicode="&#xe3;" horiz-adv-x="1034" d="M49 0zM836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137 q50 0 98.5 31.5t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247zM797 1353q67 0 81 90h107q-6 -48 -23 -87.5t-42.5 -67.5t-59.5 -44t-71 -16q-33 0 -60.5 14.5t-50.5 32.5 t-44 33t-42 15q-33 0 -53.5 -23t-26.5 -68h-109q6 47 23.5 87t44 68.5t59.5 44.5t71 16q34 0 60.5 -15t49.5 -33t43.5 -32.5t42.5 -14.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1034" d="M49 0zM836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137 q50 0 98.5 31.5t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247zM624 1342q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5t-24.5 36t-9.5 43.5 q0 24 9.5 45.5t24.5 37.5t35.5 25t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM991 1342q0 -24 -9 -44t-25 -35.5t-37 -24.5t-44 -9q-24 0 -45 9t-36.5 24.5t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1034" d="M49 0zM836 0h-95q-36 0 -48.5 18.5t-12.5 44.5l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37zM389 137 q50 0 98.5 31.5t91.5 86.5t80 131.5t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247zM506 1347q0 43 16.5 77.5t45 60t65 39.5t77.5 14q42 0 79.5 -14t66 -39.5t45 -60t16.5 -77.5 q0 -42 -16.5 -77t-45 -59.5t-66 -38.5t-79.5 -14q-41 0 -77.5 14t-65 38.5t-45 59.5t-16.5 77zM609 1347q0 -45 27.5 -74t75.5 -29q46 0 74.5 29t28.5 74q0 46 -28.5 74.5t-74.5 28.5q-48 0 -75.5 -28.5t-27.5 -74.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1480" d="M1144 1052q61 0 113.5 -19t90.5 -53t60 -79.5t22 -97.5q0 -58 -28.5 -113.5t-98.5 -99t-190 -72t-302 -31.5q-1 -10 -1 -20v-21q0 -159 62.5 -238t173.5 -79q50 0 88 10.5t68.5 25t53.5 32.5t42.5 33t35.5 25t32 10q20 0 35 -17l45 -57q-52 -53 -102 -92t-102 -64 t-109 -37t-122 -12q-108 0 -189.5 58t-121.5 175q-33 -63 -78 -107.5t-96.5 -73.5t-105.5 -41.5t-105 -12.5q-136 0 -209.5 60.5t-73.5 183.5q0 73 32.5 138.5t106.5 116.5t193.5 82.5t294.5 36.5q4 32 7 52.5t4 34t1.5 22.5t0.5 16q0 183 -158 183q-61 0 -104.5 -16.5 t-76 -37.5t-57 -38t-47.5 -17q-18 0 -30.5 9t-18.5 24l-23 56q89 83 179.5 124t197.5 41q111 0 173 -49.5t85 -136.5q61 86 149.5 134.5t202.5 48.5zM646 490q-123 -5 -208 -24t-137.5 -49.5t-75.5 -72t-23 -92.5q0 -74 40 -109t106 -35q53 0 101 18.5t86 56.5t65 95t36 133 zM1121 922q-61 0 -111.5 -23t-87.5 -66t-62.5 -103.5t-38.5 -134.5q133 7 219.5 25.5t137.5 44.5t71.5 59t20.5 70q0 58 -38.5 93t-110.5 35z" />
<glyph unicode="&#xe7;" horiz-adv-x="886" d="M287 -255q6 0 13 -4t17.5 -8.5t23.5 -9t34 -4.5q41 0 63 20.5t22 51.5q0 36 -36.5 51t-104.5 24l52 124q-76 10 -134 44.5t-97 89t-59 127.5t-20 159q0 126 39.5 241.5t110.5 204.5t168 142.5t214 53.5q102 0 172 -36.5t122 -106.5l-58 -68q-6 -8 -15 -13.5t-20 -5.5 q-14 0 -28 13t-36 29t-55.5 28.5t-85.5 12.5q-71 0 -134 -38.5t-110 -105t-74 -157.5t-27 -195q0 -63 14 -115t43 -88.5t70.5 -56.5t96.5 -20q46 0 82 9.5t63.5 24t48.5 31.5t38 31.5t30.5 24t28.5 9.5q20 0 35 -17l45 -56q-48 -52 -91 -89t-86.5 -60t-88 -34.5t-94.5 -14.5 l-23 -62q76 -17 110 -49.5t34 -79.5q0 -37 -17 -66t-47 -49t-71.5 -30.5t-90.5 -10.5q-39 0 -73 8.5t-61 23.5l23 55q9 17 24 17z" />
<glyph unicode="&#xe8;" horiz-adv-x="944" d="M60 0zM902 815q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q-1 -11 -1 -22v-22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42.5 33t35.5 25t33 10q18 0 34 -17l45 -57q-52 -54 -102 -92.5t-102 -64t-109 -37t-123 -11.5 q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80zM589 921q-65 0 -119 -27t-96 -73.5t-72 -109.5t-46 -136q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38 t-26 36.5t-46.5 27.5t-69.5 11zM504 1482q32 0 46 -10.5t24 -32.5l110 -248h-101q-20 0 -32.5 6t-24.5 22l-189 263h167z" />
<glyph unicode="&#xe9;" horiz-adv-x="944" d="M60 0zM902 815q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q-1 -11 -1 -22v-22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42.5 33t35.5 25t33 10q18 0 34 -17l45 -57q-52 -54 -102 -92.5t-102 -64t-109 -37t-123 -11.5 q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80zM589 921q-65 0 -119 -27t-96 -73.5t-72 -109.5t-46 -136q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38 t-26 36.5t-46.5 27.5t-69.5 11zM918 1482l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#xea;" horiz-adv-x="944" d="M60 0zM902 815q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q-1 -11 -1 -22v-22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42.5 33t35.5 25t33 10q18 0 34 -17l45 -57q-52 -54 -102 -92.5t-102 -64t-109 -37t-123 -11.5 q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80zM589 921q-65 0 -119 -27t-96 -73.5t-72 -109.5t-46 -136q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38 t-26 36.5t-46.5 27.5t-69.5 11zM895 1197h-117q-11 0 -21.5 4t-15.5 10l-102 131l-7 7t-7 9q-5 -5 -9 -8.5t-8 -7.5l-136 -131q-6 -5 -17.5 -9.5t-23.5 -4.5h-122l245 268h164z" />
<glyph unicode="&#xeb;" horiz-adv-x="944" d="M60 0zM902 815q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q-1 -11 -1 -22v-22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42.5 33t35.5 25t33 10q18 0 34 -17l45 -57q-52 -54 -102 -92.5t-102 -64t-109 -37t-123 -11.5 q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80zM589 921q-65 0 -119 -27t-96 -73.5t-72 -109.5t-46 -136q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38 t-26 36.5t-46.5 27.5t-69.5 11zM553 1342q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5t-24.5 36t-9.5 43.5q0 24 9.5 45.5t24.5 37.5t35.5 25t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM920 1342q0 -24 -9 -44t-25 -35.5t-37 -24.5t-44 -9 q-24 0 -45 9t-36.5 24.5t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z" />
<glyph unicode="&#xec;" horiz-adv-x="498" d="M63 0zM401 1037l-125 -1037h-179l125 1037h179zM230 1482q32 0 46 -10.5t24 -32.5l110 -248h-101q-20 0 -32.5 6t-24.5 22l-189 263h167z" />
<glyph unicode="&#xed;" horiz-adv-x="498" d="M97 0zM401 1037l-125 -1037h-179l125 1037h179zM644 1482l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#xee;" horiz-adv-x="498" d="M35 0zM401 1037l-125 -1037h-179l125 1037h179zM621 1197h-117q-11 0 -21.5 4t-15.5 10l-102 131l-7 7t-7 9q-5 -5 -9 -8.5t-8 -7.5l-136 -131q-6 -5 -17.5 -9.5t-23.5 -4.5h-122l245 268h164z" />
<glyph unicode="&#xef;" horiz-adv-x="498" d="M48 0zM401 1037l-125 -1037h-179l125 1037h179zM279 1342q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5t-24.5 36t-9.5 43.5q0 24 9.5 45.5t24.5 37.5t35.5 25t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM646 1342q0 -24 -9 -44t-25 -35.5 t-37 -24.5t-44 -9q-24 0 -45 9t-36.5 24.5t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1044" d="M468 1091q-3 5 -4 9.5t-1 9.5q0 19 15 29l132 96q-42 25 -91 43.5t-105 36.5q-38 11 -38 42q0 13 8 33l26 56q90 -16 173.5 -49t154.5 -85l178 127l30 -59q6 -9 6 -21q0 -16 -19 -31l-116 -85q83 -86 131 -209.5t48 -292.5t-35.5 -307.5t-105.5 -238t-172 -154 t-236 -54.5q-84 0 -154.5 29t-122 84t-80.5 132t-29 175q0 111 35.5 209.5t99.5 172t153 117t197 43.5q99 0 179 -46.5t129 -140.5q-2 139 -41 240t-117 172l-194 -142zM468 124q67 0 123.5 26t101.5 81.5t77 142t48 207.5q-9 45 -28.5 86.5t-50 73.5t-74 51.5t-102.5 19.5 q-77 0 -137.5 -30.5t-102.5 -84.5t-64 -126t-22 -155q0 -69 17 -123t48.5 -91.5t74 -57.5t91.5 -20z" />
<glyph unicode="&#xf1;" horiz-adv-x="1077" d="M73 0zM73 0l124 1038h91q61 0 61 -61l-15 -201q83 137 187 207t215 70q116 0 180 -78t64 -227q0 -20 -1 -42t-4 -46l-78 -660h-182l78 660q2 20 3.5 39.5t1.5 36.5q0 84 -31.5 124.5t-97.5 40.5q-49 0 -100 -26.5t-98 -75t-87.5 -118t-69.5 -154.5l-59 -527h-182z M763 1355q67 0 81 90h107q-6 -48 -23 -87.5t-42.5 -67.5t-59.5 -44t-71 -16q-33 0 -60.5 14.5t-50.5 32.5t-44 33t-42 15q-33 0 -53.5 -23t-26.5 -68h-109q6 47 23.5 87t44 68.5t59.5 44.5t71 16q34 0 60.5 -15t49.5 -33t43.5 -32.5t42.5 -14.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1029" d="M56 0zM459 128q75 0 136 41t104 109t67 157t24 187q0 143 -56.5 215.5t-163.5 72.5q-76 0 -136.5 -40.5t-103.5 -108.5t-66.5 -157t-23.5 -186q0 -143 55.5 -216.5t163.5 -73.5zM442 -14q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5q0 131 41 246.5t112.5 202.5 t168 137.5t209.5 50.5q85 0 155.5 -29.5t121.5 -84.5t80 -135.5t29 -181.5q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5zM497 1484q32 0 46 -10.5t24 -32.5l110 -248h-101q-20 0 -32.5 6t-24.5 22l-189 263h167z" />
<glyph unicode="&#xf3;" horiz-adv-x="1029" d="M56 0zM459 128q75 0 136 41t104 109t67 157t24 187q0 143 -56.5 215.5t-163.5 72.5q-76 0 -136.5 -40.5t-103.5 -108.5t-66.5 -157t-23.5 -186q0 -143 55.5 -216.5t163.5 -73.5zM442 -14q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5q0 131 41 246.5t112.5 202.5 t168 137.5t209.5 50.5q85 0 155.5 -29.5t121.5 -84.5t80 -135.5t29 -181.5q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5zM910 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#xf4;" horiz-adv-x="1029" d="M56 0zM459 128q75 0 136 41t104 109t67 157t24 187q0 143 -56.5 215.5t-163.5 72.5q-76 0 -136.5 -40.5t-103.5 -108.5t-66.5 -157t-23.5 -186q0 -143 55.5 -216.5t163.5 -73.5zM442 -14q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5q0 131 41 246.5t112.5 202.5 t168 137.5t209.5 50.5q85 0 155.5 -29.5t121.5 -84.5t80 -135.5t29 -181.5q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5zM887 1199h-117q-11 0 -21.5 4t-15.5 10l-102 131l-7 7t-7 9q-5 -5 -9 -8.5t-8 -7.5l-136 -131q-6 -5 -17.5 -9.5t-23.5 -4.5h-122l245 268 h164z" />
<glyph unicode="&#xf5;" horiz-adv-x="1029" d="M56 0zM459 128q75 0 136 41t104 109t67 157t24 187q0 143 -56.5 215.5t-163.5 72.5q-76 0 -136.5 -40.5t-103.5 -108.5t-66.5 -157t-23.5 -186q0 -143 55.5 -216.5t163.5 -73.5zM442 -14q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5q0 131 41 246.5t112.5 202.5 t168 137.5t209.5 50.5q85 0 155.5 -29.5t121.5 -84.5t80 -135.5t29 -181.5q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5zM718 1355q67 0 81 90h107q-6 -48 -23 -87.5t-42.5 -67.5t-59.5 -44t-71 -16q-33 0 -60.5 14.5t-50.5 32.5t-44 33t-42 15q-33 0 -53.5 -23 t-26.5 -68h-109q6 47 23.5 87t44 68.5t59.5 44.5t71 16q34 0 60.5 -15t49.5 -33t43.5 -32.5t42.5 -14.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1029" d="M56 0zM459 128q75 0 136 41t104 109t67 157t24 187q0 143 -56.5 215.5t-163.5 72.5q-76 0 -136.5 -40.5t-103.5 -108.5t-66.5 -157t-23.5 -186q0 -143 55.5 -216.5t163.5 -73.5zM442 -14q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5q0 131 41 246.5t112.5 202.5 t168 137.5t209.5 50.5q85 0 155.5 -29.5t121.5 -84.5t80 -135.5t29 -181.5q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5zM545 1344q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5t-24.5 36t-9.5 43.5q0 24 9.5 45.5t24.5 37.5t35.5 25 t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM912 1344q0 -24 -9 -44t-25 -35.5t-37 -24.5t-44 -9q-24 0 -45 9t-36.5 24.5t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z" />
<glyph unicode="&#xf7;" d="M134 754h981l-16 -133h-981zM535 1036q0 29 11 54.5t30.5 45t45 31t54.5 11.5q25 0 44.5 -9.5t34.5 -25t23 -36t8 -43.5q0 -30 -11.5 -55t-31 -43.5t-45 -29t-53.5 -10.5q-50 0 -80 31.5t-30 78.5zM444 307q0 29 11.5 54.5t31 45t45 31t54.5 11.5q25 0 44.5 -9.5 t34.5 -25t23 -36t8 -43.5q0 -30 -11.5 -55t-31 -43.5t-45 -29t-53.5 -10.5q-49 0 -80 31.5t-31 78.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1029" d="M442 -14q-127 0 -219 62l-37 -50q-23 -31 -52.5 -43.5t-58.5 -12.5h-69l135 185q-41 54 -63 127t-22 163q0 131 41 246.5t112.5 202.5t168 137.5t209.5 50.5q128 0 221 -66l35 47q19 29 35.5 40t49.5 11h92l-131 -177q40 -54 62 -125.5t22 -160.5q0 -130 -41 -246 t-112 -203t-168.5 -137.5t-209.5 -50.5zM228 418q0 -91 27 -159l453 617q-57 46 -138 46q-76 0 -138 -39t-107.5 -107t-71 -160.5t-25.5 -197.5zM459 117q75 0 137.5 39.5t108.5 107.5t71 160t25 198q0 45 -7 83t-19 71l-452 -614q56 -45 136 -45z" />
<glyph unicode="&#xf9;" horiz-adv-x="1075" d="M98 0zM362 1037l-77 -659q-2 -20 -3.5 -38.5t-1.5 -36.5q0 -84 31 -125t99 -41q47 0 96.5 25.5t96 73t86.5 114t70 148.5l64 539h182l-125 -1037h-90q-32 0 -49.5 14.5t-17.5 43.5q0 1 1 18.5t2.5 38t3 38t1.5 18.5l7 83q-83 -132 -185 -200t-211 -68q-116 0 -180 78.5 t-64 226.5q0 20 1 41.5t4 45.5l78 659h181zM504 1484q32 0 46 -10.5t24 -32.5l110 -248h-101q-20 0 -32.5 6t-24.5 22l-189 263h167z" />
<glyph unicode="&#xfa;" horiz-adv-x="1075" d="M98 0zM362 1037l-77 -659q-2 -20 -3.5 -38.5t-1.5 -36.5q0 -84 31 -125t99 -41q47 0 96.5 25.5t96 73t86.5 114t70 148.5l64 539h182l-125 -1037h-90q-32 0 -49.5 14.5t-17.5 43.5q0 1 1 18.5t2.5 38t3 38t1.5 18.5l7 83q-83 -132 -185 -200t-211 -68q-116 0 -180 78.5 t-64 226.5q0 20 1 41.5t4 45.5l78 659h181zM918 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#xfb;" horiz-adv-x="1075" d="M98 0zM362 1037l-77 -659q-2 -20 -3.5 -38.5t-1.5 -36.5q0 -84 31 -125t99 -41q47 0 96.5 25.5t96 73t86.5 114t70 148.5l64 539h182l-125 -1037h-90q-32 0 -49.5 14.5t-17.5 43.5q0 1 1 18.5t2.5 38t3 38t1.5 18.5l7 83q-83 -132 -185 -200t-211 -68q-116 0 -180 78.5 t-64 226.5q0 20 1 41.5t4 45.5l78 659h181zM895 1199h-117q-11 0 -21.5 4t-15.5 10l-102 131l-7 7t-7 9q-5 -5 -9 -8.5t-8 -7.5l-136 -131q-6 -5 -17.5 -9.5t-23.5 -4.5h-122l245 268h164z" />
<glyph unicode="&#xfc;" horiz-adv-x="1075" d="M98 0zM362 1037l-77 -659q-2 -20 -3.5 -38.5t-1.5 -36.5q0 -84 31 -125t99 -41q47 0 96.5 25.5t96 73t86.5 114t70 148.5l64 539h182l-125 -1037h-90q-32 0 -49.5 14.5t-17.5 43.5q0 1 1 18.5t2.5 38t3 38t1.5 18.5l7 83q-83 -132 -185 -200t-211 -68q-116 0 -180 78.5 t-64 226.5q0 20 1 41.5t4 45.5l78 659h181zM553 1344q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5t-24.5 36t-9.5 43.5q0 24 9.5 45.5t24.5 37.5t35.5 25t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM920 1344q0 -24 -9 -44t-25 -35.5t-37 -24.5 t-44 -9q-24 0 -45 9t-36.5 24.5t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="974" d="M83 0zM324 -309q-22 -42 -64 -42h-133l224 412l-268 976h150q22 0 32.5 -10.5t15.5 -27.5l166 -653q5 -22 8.5 -43t5.5 -43q9 22 17.5 44t20.5 43l327 654q8 16 23 26t31 10h144zM883 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172 z" />
<glyph unicode="&#xfe;" horiz-adv-x="1070" d="M38 -351l226 1859h180l-91 -733q39 62 84 114t94.5 88t102.5 56t108 20q132 0 206 -96.5t74 -281.5q0 -84 -17.5 -168t-49.5 -160.5t-78 -142.5t-101.5 -114.5t-121 -76t-137.5 -27.5q-77 0 -140 30t-107 85l-47 -387q-3 -27 -23 -46t-50 -19h-112zM674 906 q-49 0 -99.5 -30.5t-97.5 -85t-87.5 -129t-70.5 -163.5l-33 -271q39 -55 93.5 -77.5t110.5 -22.5q53 0 100.5 22.5t86.5 61t69.5 90t51.5 110.5t32.5 122t11.5 124q0 122 -44 185.5t-124 63.5z" />
<glyph unicode="&#xff;" horiz-adv-x="974" d="M83 0zM324 -309q-22 -42 -64 -42h-133l224 412l-268 976h150q22 0 32.5 -10.5t15.5 -27.5l166 -653q5 -22 8.5 -43t5.5 -43q9 22 17.5 44t20.5 43l327 654q8 16 23 26t31 10h144zM518 1344q0 -24 -9.5 -44t-26 -35.5t-37.5 -24.5t-44 -9q-24 0 -44.5 9t-35.5 24.5 t-24.5 36t-9.5 43.5q0 24 9.5 45.5t24.5 37.5t35.5 25t44.5 9t44.5 -9t37 -25t26 -37.5t9.5 -45.5zM885 1344q0 -24 -9 -44t-25 -35.5t-37 -24.5t-44 -9q-24 0 -45 9t-36.5 24.5t-24.5 36t-9 43.5q0 24 9 45.5t24.5 37.5t36.5 25t45 9t44.5 -9t36.5 -25t25 -37.5t9 -45.5z " />
<glyph unicode="&#x104;" horiz-adv-x="1266" d="M1217 -219q13 0 16 -12l18 -66q-31 -24 -76.5 -38.5t-94.5 -14.5q-78 0 -119.5 35.5t-41.5 95.5q0 65 44 121t111 98h-13q-26 0 -40 13t-20 33l-79 358h-598l-168 -358q-9 -18 -28 -32t-43 -14h-150l720 1467h194l359 -1467h-22q-22 -12 -45 -29t-42.5 -38.5t-31.5 -47.5 t-12 -56q0 -33 20.5 -53t52.5 -20q22 0 37 3.5t25 8.5t16 9t11 4zM388 544h503l-135 607q-6 27 -13.5 60.5t-12.5 72.5q-14 -39 -29 -73t-28 -61z" />
<glyph unicode="&#x105;" horiz-adv-x="1036" d="M850 -219q13 0 16 -12l19 -66q-31 -24 -76.5 -38.5t-94.5 -14.5q-78 0 -120 35.5t-42 95.5q0 33 12.5 64t34 59.5t51 54t64.5 45.5q-19 7 -26.5 23t-7.5 36l18 210q-37 -66 -79 -119t-90 -90t-100 -57.5t-108 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156 q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t123.5 -37l-123 -1004h-17q-22 -12 -44.5 -29t-42 -38.5t-32 -47.5t-12.5 -56q0 -33 20.5 -53t53.5 -20q22 0 36.5 3.5t25 8.5t16 9t10.5 4zM389 137q50 0 98.5 31.5t91.5 86.5t80 131.5 t66 167.5l44 356q-23 5 -45 7t-43 2q-61 0 -118.5 -20t-108 -56t-91 -85.5t-70 -109t-46 -126.5t-16.5 -138q0 -247 158 -247z" />
<glyph unicode="&#x106;" horiz-adv-x="1288" d="M102 0zM695 148q68 0 120 11.5t92 28t68.5 36.5t49.5 36t35 27.5t25 11.5q9 0 15.5 -3.5t10.5 -8.5l67 -83q-96 -104 -222 -162t-292 -58q-131 0 -235.5 47t-177 131.5t-111 201t-38.5 257.5q0 191 59.5 349.5t163.5 273t244.5 177.5t301.5 63q78 0 141.5 -13.5t116 -39 t95 -61.5t79.5 -80l-66 -80q-8 -10 -18.5 -16.5t-25.5 -6.5q-18 0 -38.5 20.5t-56 44.5t-91.5 44.5t-145 20.5q-119 0 -222 -47.5t-179 -136.5t-119.5 -214.5t-43.5 -282.5q0 -114 29.5 -204t82.5 -153t125.5 -97t159.5 -34zM1248 1824l-295 -215q-17 -12 -30 -19t-34 -7 h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#x107;" horiz-adv-x="886" d="M61 0zM839 186q-53 -58 -101 -97t-96 -62t-100 -32t-111 -9q-90 0 -159 30.5t-115.5 86.5t-71 134t-24.5 173q0 126 39.5 241.5t110.5 204.5t168 142.5t214 53.5q102 0 172 -36.5t122 -106.5l-58 -68q-6 -8 -15 -13.5t-20 -5.5q-14 0 -28 13t-36 29t-55.5 28.5 t-85.5 12.5q-71 0 -134 -38.5t-110 -105t-74 -157.5t-27 -195q0 -63 14 -115t43 -88.5t70.5 -56.5t96.5 -20q46 0 82 9.5t63.5 24t48.5 31.5t38 31.5t30.5 24t28.5 9.5q20 0 35 -17l45 -56v0zM900 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33 t49.5 10h172z" />
<glyph unicode="&#x118;" horiz-adv-x="1092" d="M859 -219q13 0 17 -12l18 -66q-31 -24 -76.5 -38.5t-94.5 -14.5q-78 0 -120 35.5t-42 95.5q0 65 44 121t112 98h-618l182 1467h834l-19 -156h-642l-62 -495h520l-19 -150h-519l-63 -510h644l-19 -156h-108q-22 -12 -44.5 -29t-42 -38.5t-32 -47.5t-12.5 -56 q0 -33 20.5 -53t53.5 -20q22 0 36.5 3.5t25 8.5t16 9t10.5 4z" />
<glyph unicode="&#x119;" horiz-adv-x="944" d="M599 -219q13 0 16 -12l19 -66q-31 -24 -76.5 -38.5t-94.5 -14.5q-78 0 -120 35.5t-42 95.5q0 59 37.5 111.5t95.5 93.5q-87 1 -156 31.5t-117.5 85t-74.5 131.5t-26 173q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77 t18 -80q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q-1 -11 -1 -22v-22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42.5 33t35.5 25t33 10q18 0 34 -17l45 -57q-79 -82 -154 -129.5t-161 -64.5q-22 -12 -43.5 -29t-40 -38t-30.5 -46.5 t-12 -54.5q0 -33 20.5 -53t53.5 -20q22 0 36.5 3.5t25 8.5t16 9t10.5 4zM589 921q-65 0 -119 -27t-96 -73.5t-72 -109.5t-46 -136q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5t-46.5 27.5t-69.5 11z" />
<glyph unicode="&#x131;" horiz-adv-x="498" d="M401 1037l-125 -1037h-179l125 1037h179z" />
<glyph unicode="&#x141;" horiz-adv-x="1025" d="M447 825l374 196l-15 -128q-4 -29 -31 -43l-347 -185l-62 -503h580l-19 -162h-772l70 570l-196 -98l16 131q3 27 29 40l171 89l91 735h191z" />
<glyph unicode="&#x142;" horiz-adv-x="615" d="M78 743q0 30 25 41l157 70l80 654h179l-72 -584l178 75v-104q0 -34 -28 -45l-167 -75l-94 -775h-179l86 707l-165 -71v107z" />
<glyph unicode="&#x143;" horiz-adv-x="1424" d="M99 0zM379 1467q26 0 37.5 -6t23.5 -26l633 -1122q2 24 4 45t4 40l131 1069h169l-180 -1467h-96q-24 0 -39 7.5t-27 28.5l-631 1122q0 -5 -0.5 -14t-2 -19.5t-2.5 -21t-2 -18.5l-133 -1085h-169l182 1467h98v0zM1213 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208 q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#x144;" horiz-adv-x="1077" d="M73 0zM73 0l124 1038h91q61 0 61 -61l-15 -201q83 137 187 207t215 70q116 0 180 -78t64 -227q0 -20 -1 -42t-4 -46l-78 -660h-182l78 660q2 20 3.5 39.5t1.5 36.5q0 84 -31.5 124.5t-97.5 40.5q-49 0 -100 -26.5t-98 -75t-87.5 -118t-69.5 -154.5l-59 -527h-182z M955 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#x152;" horiz-adv-x="2059" d="M2082 1467l-19 -156h-641l-60 -496h515l-17 -150h-517l-62 -509h640l-19 -156h-811l28 231q-92 -117 -218.5 -181t-279.5 -64q-120 0 -216.5 47t-163.5 130.5t-103 199.5t-36 254q0 189 53.5 348t147.5 274.5t222.5 180t278.5 64.5q77 0 143 -19.5t120.5 -56t97 -88 t73.5 -116.5l33 263h811zM1164 844q0 109 -24 198.5t-70 152.5t-112.5 98t-151.5 35q-112 0 -205 -50.5t-160.5 -143.5t-105 -223t-37.5 -288q0 -110 24.5 -198.5t71 -152t113.5 -98t152 -34.5q112 0 204.5 50.5t160 142.5t104 221.5t36.5 289.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1581" d="M1257 1052q67 0 120.5 -17.5t91 -50t57.5 -77.5t20 -99q0 -42 -11.5 -81t-40.5 -73t-76 -64.5t-118.5 -55t-169 -44t-226.5 -32.5v-12q0 -159 62 -238t174 -79q49 0 87.5 10.5t68.5 25t53 32.5t42.5 33t35.5 25t32 10q19 0 35 -17l45 -57q-51 -53 -100.5 -92t-102 -64 t-110 -37t-122.5 -12q-108 0 -189 59t-121 177q-71 -113 -176.5 -174.5t-237.5 -61.5q-83 0 -145.5 30t-104 82t-62.5 119.5t-21 140.5q0 168 43.5 297.5t118 218t172 133.5t207.5 45t183 -52t109 -145q65 92 159.5 144.5t217.5 52.5zM423 124q83 0 147 40t107.5 111.5 t65.5 169t22 212.5q0 118 -47.5 187t-147.5 69q-80 0 -143 -38.5t-108.5 -110t-69.5 -171.5t-24 -224q0 -48 10.5 -92.5t34 -78t61.5 -54t92 -20.5zM1229 923q-60 0 -112.5 -23.5t-93.5 -69.5t-69.5 -111t-41.5 -148q148 18 242 44.5t147.5 56t73 63t19.5 67.5q0 25 -10 46 t-30 38t-51 27t-74 10z" />
<glyph unicode="&#x15a;" horiz-adv-x="1000" d="M12 0zM948 1240q-11 -14 -21.5 -22t-24.5 -8q-17 0 -36.5 18t-50 39t-73.5 39t-107 18q-66 0 -117 -20.5t-86.5 -56t-54 -82.5t-18.5 -101q0 -53 25 -89.5t64.5 -62.5t91.5 -46t105 -41t105 -46.5t92 -63.5t64.5 -91.5t24.5 -128.5q0 -104 -36 -197t-103 -162.5 t-162 -110.5t-214 -41q-131 0 -234 51.5t-170 139.5l68 91q8 11 20.5 18.5t26.5 7.5q19 0 41.5 -23t57.5 -51.5t86.5 -51.5t127.5 -23q71 0 128 22.5t96 62.5t60.5 96t21.5 123q0 56 -24.5 94t-64.5 63.5t-90.5 44.5t-104 38.5t-104.5 44t-91 62t-64.5 93t-24.5 136.5 q0 90 33 173t94.5 146.5t149.5 101.5t198 38q113 0 203 -43t149 -118zM1053 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#x15b;" horiz-adv-x="796" d="M10 0zM740 871q-8 -12 -16 -17.5t-21 -5.5q-14 0 -31 11t-41 25t-58.5 26t-84.5 12q-46 0 -83.5 -13t-65.5 -35.5t-42.5 -52.5t-14.5 -64q0 -48 31.5 -77t79.5 -50t104 -40.5t104 -49t80 -75.5t32 -120q0 -72 -28.5 -137.5t-81 -115t-127 -79t-166.5 -29.5 q-99 0 -175.5 35t-124.5 91l45 69q8 13 19.5 20.5t28.5 7.5q16 0 33 -14.5t41.5 -32.5t61.5 -32t95 -14q50 0 90.5 15t68.5 41t43 60t15 73q0 52 -32 83t-80 51.5t-103 38.5t-103.5 46t-80 73t-31.5 120q0 66 26 126.5t75 107.5t119.5 75.5t158.5 28.5q93 0 164 -31.5 t122 -84.5zM822 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#x160;" horiz-adv-x="1000" d="M12 0zM948 1240q-11 -14 -21.5 -22t-24.5 -8q-17 0 -36.5 18t-50 39t-73.5 39t-107 18q-66 0 -117 -20.5t-86.5 -56t-54 -82.5t-18.5 -101q0 -53 25 -89.5t64.5 -62.5t91.5 -46t105 -41t105 -46.5t92 -63.5t64.5 -91.5t24.5 -128.5q0 -104 -36 -197t-103 -162.5 t-162 -110.5t-214 -41q-131 0 -234 51.5t-170 139.5l68 91q8 11 20.5 18.5t26.5 7.5q19 0 41.5 -23t57.5 -51.5t86.5 -51.5t127.5 -23q71 0 128 22.5t96 62.5t60.5 96t21.5 123q0 56 -24.5 94t-64.5 63.5t-90.5 44.5t-104 38.5t-104.5 44t-91 62t-64.5 93t-24.5 136.5 q0 90 33 173t94.5 146.5t149.5 101.5t198 38q113 0 203 -43t149 -118zM1014 1797l-252 -214h-173l-199 214h134q11 0 24 -3t21 -10l117 -110l146 110q9 7 22.5 10t26.5 3h133z" />
<glyph unicode="&#x161;" horiz-adv-x="796" d="M10 0zM740 871q-8 -12 -16 -17.5t-21 -5.5q-14 0 -31 11t-41 25t-58.5 26t-84.5 12q-46 0 -83.5 -13t-65.5 -35.5t-42.5 -52.5t-14.5 -64q0 -48 31.5 -77t79.5 -50t104 -40.5t104 -49t80 -75.5t32 -120q0 -72 -28.5 -137.5t-81 -115t-127 -79t-166.5 -29.5 q-99 0 -175.5 35t-124.5 91l45 69q8 13 19.5 20.5t28.5 7.5q16 0 33 -14.5t41.5 -32.5t61.5 -32t95 -14q50 0 90.5 15t68.5 41t43 60t15 73q0 52 -32 83t-80 51.5t-103 38.5t-103.5 46t-80 73t-31.5 120q0 66 26 126.5t75 107.5t119.5 75.5t158.5 28.5q93 0 164 -31.5 t122 -84.5zM245 1467h123q11 0 20.5 -4t14.5 -10l103 -132l13 -20l20 20l134 132q7 6 18 10t23 4h117l-244 -268h-163z" />
<glyph unicode="&#x178;" horiz-adv-x="1192" d="M124 0zM702 587l-72 -587h-192l73 585l-387 882h170q26 0 39 -12.5t21 -32.5l244 -587q10 -30 17.5 -56.5t12.5 -53.5q12 27 27 53.5t32 56.5l390 587q11 16 28 30.5t42 14.5h159zM668 1704q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5t-40 -7.5q-20 0 -38.5 7.5t-32.5 21.5 t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23t23 -33.5t8.5 -40.5zM1020 1704q0 -20 -8 -38.5t-22.5 -32.5t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5t32.5 23t40 8t40 -8t32.5 -23t22.5 -33.5 t8 -40.5z" />
<glyph unicode="&#x179;" horiz-adv-x="1175" d="M16 0zM1224 1467l-9 -61q-2 -14 -8 -26t-15 -24l-897 -1200h756l-21 -156h-1014l9 60q2 14 7.5 25.5t14.5 24.5l897 1201h-730l19 156h991zM1112 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph unicode="&#x17a;" horiz-adv-x="878" d="M3 0zM868 959q-2 -19 -11 -37t-20 -32l-598 -748h512l-17 -142h-731l9 76q2 13 11 31t21 34l601 753h-504l18 143h719zM846 1484l-254 -263q-14 -15 -29 -21.5t-35 -6.5h-105l170 248q14 23 31.5 33t49.5 10h172z" />
<glyph unicode="&#x17b;" horiz-adv-x="1175" d="M16 0zM1224 1467l-9 -61q-2 -14 -8 -26t-15 -24l-897 -1200h756l-21 -156h-1014l9 60q2 14 7.5 25.5t14.5 24.5l897 1201h-730l19 156h991zM874 1729q0 -25 -10 -47.5t-27.5 -39.5t-40.5 -27t-49 -10q-25 0 -47.5 10t-39 27t-26.5 39.5t-10 47.5q0 26 10 48t26.5 39.5 t39 27.5t47.5 10q26 0 49 -10t40.5 -27.5t27.5 -39.5t10 -48z" />
<glyph unicode="&#x17c;" horiz-adv-x="878" d="M3 0zM868 959q-2 -19 -11 -37t-20 -32l-598 -748h512l-17 -142h-731l9 76q2 13 11 31t21 34l601 753h-504l18 143h719zM695 1372q0 -27 -11 -50t-29 -40t-41.5 -27.5t-49.5 -10.5q-27 0 -50 10.5t-40.5 27.5t-27.5 40t-10 50t10 51t27.5 42t40.5 28t50 10t50 -10t41 -28 t29 -42t11 -51z" />
<glyph unicode="&#x17d;" horiz-adv-x="1175" d="M16 0zM1224 1467l-9 -61q-2 -14 -8 -26t-15 -24l-897 -1200h756l-21 -156h-1014l9 60q2 14 7.5 25.5t14.5 24.5l897 1201h-730l19 156h991zM1078 1797l-252 -214h-173l-199 214h134q11 0 24 -3t21 -10l117 -110l146 110q9 7 22.5 10t26.5 3h133z" />
<glyph unicode="&#x17e;" horiz-adv-x="878" d="M3 0zM868 959q-2 -19 -11 -37t-20 -32l-598 -748h512l-17 -142h-731l9 76q2 13 11 31t21 34l601 753h-504l18 143h719zM269 1467h123q11 0 20.5 -4t14.5 -10l103 -132l13 -20l20 20l134 132q7 6 18 10t23 4h117l-244 -268h-163z" />
<glyph unicode="&#x192;" d="M722 788l-183 -752q-51 -208 -185 -302.5t-349 -94.5l11 93q7 56 67 56q47 1 90.5 14.5t80.5 43t65.5 77.5t46.5 119l181 741l-159 16q-18 2 -27.5 12t-9.5 26q0 9 1.5 22t3.5 27t4 30h220l43 175q49 202 180 299.5t353 97.5l-12 -97q-3 -29 -19 -41t-47 -12 q-48 0 -91.5 -13t-81 -43.5t-66.5 -78.5t-47 -119l-44 -168h334l-16 -128h-344z" />
<glyph unicode="&#x2c6;" horiz-adv-x="581" d="M669 1197h-117q-11 0 -21.5 4t-15.5 10l-102 131l-7 7t-7 9q-5 -5 -9 -8.5t-8 -7.5l-136 -131q-6 -5 -17.5 -9.5t-23.5 -4.5h-122l245 268h164z" />
<glyph unicode="&#x2c7;" horiz-adv-x="581" d="M116 1465h123q11 0 20.5 -4t14.5 -10l103 -132l13 -20l20 20l134 132q7 6 18 10t23 4h117l-244 -268h-163z" />
<glyph unicode="&#x2d8;" horiz-adv-x="581" d="M379 1192q-63 0 -107.5 17t-72 46.5t-40 69t-12.5 85.5q0 13 1.5 27t3.5 28h123q-3 -22 -3 -41q0 -52 27 -84t94 -32q40 0 67.5 11.5t45.5 32.5t28.5 50.5t14.5 62.5h125q-7 -55 -26.5 -104.5t-55 -87t-88 -59.5t-125.5 -22z" />
<glyph unicode="&#x2d9;" horiz-adv-x="581" d="M541 1370q0 -27 -11 -50t-29 -40t-41.5 -27.5t-49.5 -10.5q-27 0 -50 10.5t-40.5 27.5t-27.5 40t-10 50t10 51t27.5 42t40.5 28t50 10t50 -10t41 -28t29 -42t11 -51z" />
<glyph unicode="&#x2da;" horiz-adv-x="581" d="M189 1347q0 43 16.5 77.5t45 60t65 39.5t77.5 14q42 0 79.5 -14t66 -39.5t45 -60t16.5 -77.5q0 -42 -16.5 -77t-45 -59.5t-66 -38.5t-79.5 -14q-41 0 -77.5 14t-65 38.5t-45 59.5t-16.5 77zM292 1347q0 -45 27.5 -74t75.5 -29q46 0 74.5 29t28.5 74q0 46 -28.5 74.5 t-74.5 28.5q-48 0 -75.5 -28.5t-27.5 -74.5z" />
<glyph unicode="&#x2db;" horiz-adv-x="581" d="M346 -219q13 0 16 -12l19 -66q-31 -24 -76.5 -38.5t-94.5 -14.5q-78 0 -120 35.5t-42 95.5q0 35 14 67.5t37.5 62.5t56 55.5t70.5 46.5l89 -13q-22 -12 -44.5 -29t-42 -38.5t-32 -47.5t-12.5 -56q0 -33 20.5 -53t53.5 -20q22 0 36.5 3.5t25 8.5t16 9t10.5 4z" />
<glyph unicode="&#x2dc;" horiz-adv-x="581" d="M500 1353q67 0 81 90h107q-6 -48 -23 -87.5t-42.5 -67.5t-59.5 -44t-71 -16q-33 0 -60.5 14.5t-50.5 32.5t-44 33t-42 15q-33 0 -53.5 -23t-26.5 -68h-109q6 47 23.5 87t44 68.5t59.5 44.5t71 16q34 0 60.5 -15t49.5 -33t43.5 -32.5t42.5 -14.5z" />
<glyph unicode="&#x2dd;" horiz-adv-x="581" d="M528 1482l-219 -263q-14 -16 -29 -22t-35 -6h-74l160 248q14 23 31.5 33t49.5 10h116zM828 1482l-262 -263q-14 -15 -28.5 -21.5t-34.5 -6.5h-85l199 248q17 20 34 31.5t48 11.5h129z" />
<glyph unicode="&#x3c0;" horiz-adv-x="1171" d="M1202 1035l-8 -69q-2 -24 -19.5 -42t-44.5 -18h-126l-111 -906h-176l109 906h-365l-81 -658q-15 -122 -81 -191t-190 -69q-32 0 -63.5 6t-58.5 23l15 74q2 9 5.5 15t12 8t21 2.5t33.5 0.5q61 0 91.5 32t38.5 101l80 656h-176l8 65q1 10 6.5 21.5t15 21t23.5 15.5t30 6 h1011z" />
<glyph unicode="&#x2000;" horiz-adv-x="940" />
<glyph unicode="&#x2001;" horiz-adv-x="1881" />
<glyph unicode="&#x2002;" horiz-adv-x="940" />
<glyph unicode="&#x2003;" horiz-adv-x="1881" />
<glyph unicode="&#x2004;" horiz-adv-x="627" />
<glyph unicode="&#x2005;" horiz-adv-x="470" />
<glyph unicode="&#x2006;" horiz-adv-x="313" />
<glyph unicode="&#x2007;" horiz-adv-x="313" />
<glyph unicode="&#x2008;" horiz-adv-x="235" />
<glyph unicode="&#x2009;" horiz-adv-x="376" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="656" d="M118 688h468l-20 -149h-467z" />
<glyph unicode="&#x2011;" horiz-adv-x="656" d="M118 688h468l-20 -149h-467z" />
<glyph unicode="&#x2012;" horiz-adv-x="656" d="M118 688h468l-20 -149h-467z" />
<glyph unicode="&#x2013;" horiz-adv-x="1050" d="M169 668h755l-16 -128h-754z" />
<glyph unicode="&#x2014;" horiz-adv-x="1552" d="M169 668h1255l-16 -128h-1254z" />
<glyph unicode="&#x2018;" horiz-adv-x="403" d="M224 1040q-17 38 -25 76t-8 76q0 102 54.5 197.5t150.5 173.5l48 -32q12 -9 13 -23q0 -8 -5 -15t-16 -19q-16 -19 -33 -44t-30.5 -54t-22 -62t-8.5 -68q0 -27 5 -54.5t19 -55.5q3 -8 3 -15q0 -25 -31 -38z" />
<glyph unicode="&#x2019;" horiz-adv-x="403" d="M377 1539q17 -38 25 -76t8 -75q0 -102 -54.5 -197.5t-150.5 -173.5l-49 32q-11 9 -12 22q0 8 5 15.5t16 19.5q16 19 33 44t30.5 53.5t22 61t8.5 68.5q0 27 -5 54.5t-18 56.5q-3 7 -4 14q0 25 31 38z" />
<glyph unicode="&#x201a;" horiz-adv-x="437" d="M233 242q17 -38 25 -76t8 -76q0 -102 -54 -197.5t-151 -173.5l-49 32q-11 9 -11 23q0 8 4.5 15t16.5 20q16 19 33 43.5t30 53t22 61.5t9 69q0 27 -5.5 54.5t-18.5 55.5q-3 7 -3 15q0 25 31 38z" />
<glyph unicode="&#x201c;" horiz-adv-x="692" d="M224 1040q-17 38 -25 76t-8 76q0 102 54.5 197.5t150.5 173.5l48 -32q12 -9 13 -23q0 -8 -5 -15t-16 -19q-16 -19 -33 -44t-30.5 -54t-22 -62t-8.5 -68q0 -27 5 -54.5t19 -55.5q3 -8 3 -15q0 -25 -31 -38zM515 1040q-17 38 -25 76t-8 76q0 102 54.5 197.5t150.5 173.5 l48 -32q12 -9 13 -23q0 -8 -5 -15t-16 -19q-16 -19 -33 -44t-30.5 -54t-22 -62t-8.5 -68q0 -27 5 -54.5t18 -55.5q3 -8 3 -15q0 -25 -30 -38z" />
<glyph unicode="&#x201d;" horiz-adv-x="692" d="M386 1539q17 -38 25 -76t8 -75q0 -102 -54.5 -197.5t-150.5 -173.5l-49 32q-11 9 -11 22q0 8 4.5 15.5t15.5 19.5q16 19 33 44t30.5 53.5t22 61t8.5 68.5q0 27 -5 54.5t-18 56.5q-3 7 -3 14q0 25 30 38zM676 1539q17 -38 25 -76t8 -75q0 -102 -54.5 -197.5t-150.5 -173.5 l-49 32q-11 9 -12 22q0 8 5 15.5t16 19.5q16 19 33 44t30.5 53.5t22 61t8.5 68.5q0 27 -5 54.5t-18 56.5q-3 7 -4 14q0 25 31 38z" />
<glyph unicode="&#x201e;" horiz-adv-x="692" d="M196 242q17 -38 24.5 -76t7.5 -76q0 -102 -54 -197.5t-150 -173.5l-50 32q-11 9 -11 23q0 8 4.5 15t16.5 20q16 19 33 43.5t30 53t22 61.5t9 69q0 27 -5 54.5t-19 55.5q-3 7 -3 15q0 25 31 38zM486 242q17 -38 25 -76t8 -76q0 -102 -54 -197.5t-151 -173.5l-49 32 q-11 9 -11 23q0 8 4.5 15t15.5 20q16 19 33.5 43.5t30.5 53t22 61.5t9 69q0 27 -5.5 54.5t-18.5 55.5q-3 7 -3 15q0 25 31 38z" />
<glyph unicode="&#x2020;" horiz-adv-x="1093" d="M165 963q4 30 26.5 53.5t65.5 23.5q65 -1 138 -14t146 -20l36 479q42 23 94 22q52 0 88 -22l-82 -479q37 3 75.5 9t75.5 11t73 9.5t69 4.5q39 0 55.5 -21t16.5 -47v-3t-0.5 -9t-2 -20t-3.5 -37h-371l-56 -454l-73 -793q-20 -11 -45 -17t-51 -6q-27 0 -48.5 6t-39.5 17 l120 793l55 454h-370z" />
<glyph unicode="&#x2021;" horiz-adv-x="1093" d="M165 963q4 30 26.5 53.5t65.5 23.5q65 -1 138 -14t146 -20l36 479q42 23 94 22q52 0 88 -22l-82 -479q37 3 75.5 9t75.5 11t73 9.5t69 4.5q39 0 56 -20t17 -46q0 -15 -2.5 -35.5t-4.5 -35.5h-371l-82 -665h370l-6 -60q-3 -31 -26.5 -55t-66.5 -24q-65 1 -138.5 14 t-146.5 19l-33 -476q-20 -11 -45 -17t-51 -6q-27 0 -48.5 6t-39.5 17l81 476q-37 -3 -75.5 -8t-75.5 -10.5t-73.5 -10t-68.5 -4.5q-39 0 -55.5 20.5t-16.5 48.5v3t1 9t2 20.5t4 37.5h371l82 665h-371z" />
<glyph unicode="&#x2022;" d="M237 609q0 79 29.5 148.5t81 121.5t120.5 82t146 30q79 0 149 -30t121.5 -82t81.5 -121.5t30 -148.5t-30 -148t-81.5 -120t-121.5 -81.5t-149 -30.5q-78 0 -146.5 30.5t-120 81.5t-81 120t-29.5 148z" />
<glyph unicode="&#x2026;" horiz-adv-x="1488" d="M46 113q0 27 10 50t27 40.5t40 28t51 10.5q27 0 50 -10.5t40.5 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5q-28 0 -51 9.5t-40 27t-27 40.5t-10 51zM572 113q0 27 10 50t27.5 40.5t40.5 28t50 10.5t50 -10.5t40.5 -28t27.5 -41t10 -49.5 q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5q-28 0 -50.5 9.5t-40 27t-27.5 40.5t-10 51zM1100 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5 t-9.5 51z" />
<glyph unicode="&#x202f;" horiz-adv-x="376" />
<glyph unicode="&#x2030;" horiz-adv-x="2178" d="M750 1171q0 -101 -30.5 -180t-79.5 -133t-112 -82t-130 -28q-57 0 -105 21t-82.5 60t-53.5 96t-19 129q0 101 28 180.5t75.5 134t111.5 83.5t135 29q57 0 105.5 -21t83 -61t54 -97.5t19.5 -130.5zM611 1170q0 51 -10.5 88t-29 60.5t-43.5 35t-54 11.5q-41 0 -77.5 -17.5 t-63 -55.5t-42.5 -96.5t-16 -141.5q0 -50 10.5 -85.5t29 -58.5t43.5 -34t55 -11q40 0 76 17t63.5 53.5t43 94t15.5 140.5zM1257 1437q11 11 26 19.5t39 8.5h127l-1167 -1436q-10 -13 -25 -21t-36 -8h-129zM1405 407q0 -101 -29.5 -181t-79 -133.5t-113 -81.5t-129.5 -28 q-57 0 -105 21t-82 60t-53 95.5t-19 128.5q0 101 27.5 180.5t75 134.5t111.5 84t136 29q57 0 104.5 -21t82.5 -61t54 -97t19 -130zM1268 406q0 51 -11 87t-29.5 60t-43.5 34.5t-54 10.5q-41 0 -77 -17.5t-63 -55.5t-43 -96t-16 -141q0 -50 10 -86t28.5 -59t43.5 -34t55 -11 q40 0 76.5 17t64 54t43.5 95.5t16 141.5zM2096 407q0 -101 -29.5 -181t-79 -133.5t-112.5 -81.5t-129 -28q-57 0 -105.5 21t-82.5 60t-53 95.5t-19 128.5q0 101 27.5 180.5t75 134.5t111.5 84t136 29q57 0 105 -21t82.5 -61t53.5 -97t19 -130zM1959 406q0 51 -11 87t-29 60 t-43.5 34.5t-53.5 10.5q-41 0 -77 -17.5t-63 -55.5t-42.5 -96t-15.5 -141q0 -50 10.5 -86t28 -59t43 -34t54.5 -11q40 0 76 17t63.5 54t43.5 95.5t16 141.5z" />
<glyph unicode="&#x2039;" horiz-adv-x="612" d="M136 530l3 23l285 397l53 -27q24 -12 24 -35q0 -20 -16 -40l-179 -269q-16 -26 -33 -37q10 -10 24 -37l114 -269q6 -14 6 -28q0 -33 -33 -48l-60 -28z" />
<glyph unicode="&#x203a;" horiz-adv-x="612" d="M485 553l-2 -23l-283 -398l-55 28q-24 11 -23 36q0 16 13 40l182 269q16 25 31 36q-13 12 -22 38l-115 269q-6 13 -6 26q0 33 34 49l60 27z" />
<glyph unicode="&#x2044;" horiz-adv-x="590" d="M-65 54q-22 -33 -45.5 -43.5t-53.5 -10.5h-72l947 1404q20 30 45 45.5t59 15.5h73z" />
<glyph unicode="&#x205f;" horiz-adv-x="470" />
<glyph unicode="&#x20ac;" d="M86 919h144q34 130 95 234t143.5 177t183.5 112t215 39q136 0 227.5 -51t151.5 -140l-68 -67q-12 -12 -19.5 -16.5t-21.5 -4.5q-11 0 -21.5 9.5t-24.5 23t-33.5 30t-47 30.5t-65.5 23t-90 9q-79 0 -148.5 -26.5t-126.5 -79t-100 -128.5t-69 -174h573l-6 -57 q-3 -17 -18 -31t-40 -14h-531q-6 -36 -9.5 -72.5t-5.5 -75.5h485l-7 -56q-2 -17 -18 -31.5t-39 -14.5h-422q7 -209 92.5 -319t233.5 -110q84 0 141 23t94.5 51t61 51t41.5 23q8 0 14 -2t12 -11l66 -69q-85 -103 -198.5 -161t-258.5 -58q-116 0 -204.5 42t-149 118t-92 183.5 t-35.5 238.5h-148l12 102h139q3 76 15 148h-135z" />
<glyph unicode="&#x2122;" horiz-adv-x="1358" d="M985 1130q8 -28 12 -51q6 13 13 25.5t15 25.5l194 315q9 14 17.5 17t25.5 3h104l-73 -604h-109l47 379l16 73l-211 -345q-16 -28 -46 -28h-17q-29 0 -39 28l-128 346v-74l-47 -379h-108l73 604h105q16 0 25 -3t14 -17zM637 1465l-13 -103h-168l-62 -501h-123l61 501h-168 l14 103h459z" />
<glyph unicode="&#x2202;" horiz-adv-x="1092" d="M427 1369q40 28 78 49.5t77.5 35.5t82.5 21.5t92 7.5q75 0 134 -29t100.5 -84t64 -135t22.5 -181q0 -30 -2 -62.5t-6 -65.5q-26 -207 -82 -379.5t-141 -297t-198 -193.5t-254 -69q-74 0 -134.5 24t-103.5 69t-66.5 109.5t-23.5 146.5q0 17 1 35t3 37q14 118 62.5 221 t123 179t168.5 120t200 44q96 0 162 -42t101 -125q5 35 9.5 66t7.5 55q4 32 5.5 61t1.5 55q0 146 -51.5 220t-145.5 74q-40 0 -74.5 -10t-62.5 -23t-49 -23t-34 -10q-11 0 -20 6t-17 24zM434 130q59 0 117 28t109.5 85t95 144t75.5 204q-4 46 -16 88.5t-35.5 75t-61 52.5 t-91.5 20q-77 0 -140 -31.5t-110 -87.5t-77 -132t-41 -165q-2 -16 -3 -31t-1 -30q0 -105 47.5 -162.5t131.5 -57.5z" />
<glyph unicode="&#x2206;" horiz-adv-x="1321" d="M1246 0h-1294l741 1465h173zM223 154h790l-235 997q-5 24 -11.5 53t-11.5 62q-13 -34 -25.5 -63t-23.5 -53z" />
<glyph unicode="&#x220f;" horiz-adv-x="1401" d="M1454 1465l-17 -149h-194l-205 -1668h-182l204 1668h-519l-206 -1668h-181l204 1668h-192l17 149h1271z" />
<glyph unicode="&#x2211;" horiz-adv-x="1291" d="M207 1467h1132l-18 -156h-855l443 -726l-7 -54l-620 -726h853l-19 -156h-1132l8 66q2 14 8.5 29t17.5 27l684 789l-490 783q-13 22 -13 45v6.5t1 6.5z" />
<glyph unicode="&#x221a;" horiz-adv-x="1171" d="M639 0h-147l-174 681h-180q-26 0 -43 13.5t-17 45.5v4.5t1 10t2 19.5t4 37h342q22 0 34.5 -11.5t16.5 -25.5l104 -431q8 -33 10 -66t2 -66q8 27 17.5 55t21.5 57l617 1393q8 16 23.5 27t35.5 11h112z" />
<glyph unicode="&#x221e;" horiz-adv-x="1290" d="M912 260q-52 0 -93 17t-73 45.5t-57 67t-47 80.5q-32 -42 -66.5 -80.5t-74.5 -67t-85 -45.5t-98 -17q-54 0 -100.5 20.5t-81 59t-54.5 91.5t-20 120q0 84 28.5 156.5t75.5 126.5t108.5 85t128.5 31q52 0 93 -17t73.5 -46t58 -67.5t47.5 -80.5q32 42 66.5 80.5t74 67.5 t84.5 46t97 17q54 0 101.5 -20.5t82 -58.5t54.5 -92t20 -121q0 -84 -28 -156.5t-75.5 -126t-109.5 -84.5t-130 -31zM344 410q34 0 64.5 15t59.5 42.5t56.5 62.5t55.5 75q-18 40 -37 75.5t-41.5 62t-49 42.5t-60.5 16q-39 0 -72 -17t-58 -47.5t-39.5 -73.5t-14.5 -97 q0 -39 10 -68.5t28.5 -49t43.5 -29t54 -9.5zM925 410q38 0 71.5 16.5t58.5 47.5t39.5 74t14.5 96q0 40 -11 69t-29 48.5t-43.5 29.5t-53.5 10q-34 0 -64 -16t-59 -42.5t-56.5 -62t-55.5 -75.5q18 -40 37.5 -75t41.5 -62.5t48.5 -42.5t60.5 -15z" />
<glyph unicode="&#x222b;" horiz-adv-x="759" d="M485 1117q42 178 138.5 272.5t236.5 94.5q36 0 69.5 -7.5t61.5 -23.5l-19 -89q-5 -18 -20.5 -28t-58.5 -10q-94 0 -150.5 -56.5t-85.5 -175.5l-253 -1055q-25 -104 -65 -179t-92.5 -123.5t-116.5 -71.5t-136 -23q-33 0 -68 6.5t-62 22.5l20 78q3 12 8.5 19t14.5 10 t24 3.5t38 0.5q55 0 97 14t73 43.5t53.5 77.5t38.5 116z" />
<glyph unicode="&#x2248;" d="M852 876q29 0 57.5 6.5t53 16.5t43.5 23t30 24l12 -110q-41 -49 -101 -73t-123 -24q-53 0 -103.5 17.5t-99.5 38t-96 38t-93 17.5q-31 0 -60 -7t-53.5 -18t-43.5 -24.5t-29 -25.5l-18 108q42 52 103 77t129 25q53 0 104 -17t99.5 -38t95.5 -37.5t93 -16.5zM809 530 q29 0 57.5 7t53.5 17.5t44 23t30 23.5l11 -112q-40 -48 -100.5 -71t-123.5 -23q-53 0 -103.5 17t-99.5 37.5t-96 37.5t-93 17q-31 0 -59.5 -7t-53.5 -17.5t-44 -23.5t-29 -25l-17 107q42 52 103 77t129 25q54 0 104.5 -17t99.5 -38t95.5 -38t91.5 -17z" />
<glyph unicode="&#x2260;" d="M204 928h503l147 249h128l-147 -249h245l-15 -134h-310l-122 -207h406l-16 -134h-470l-153 -258h-128l153 258h-281l17 134h344l122 207h-440z" />
<glyph unicode="&#x2264;" horiz-adv-x="1092" d="M182 810l839 386l-15 -125q-2 -16 -13.5 -30t-38.5 -26l-478 -212q-24 -10 -49.5 -17.5t-52.5 -13.5q27 -5 51 -12t44 -19l425 -214q22 -11 30.5 -22t8.5 -26q0 -2 -0.5 -4t-0.5 -4l-16 -125l-743 388zM110 220h791l-17 -138h-792z" />
<glyph unicode="&#x2265;" horiz-adv-x="1092" d="M899 82h-791l17 138h791zM980 734l-839 -386l16 125q2 16 13 30t38 26l478 212q24 10 49.5 17.5t52.5 13.5q-27 5 -50.5 12.5t-44.5 18.5l-425 214q-22 11 -30.5 22.5t-8.5 25.5q0 2 0.5 4t0.5 4l16 125l743 -388z" />
<glyph unicode="&#x25ca;" d="M146 718l400 841h127l400 -841l-400 -840h-127zM298 718l283 -595q8 -24 15.5 -43.5t12.5 -37.5q5 18 12.5 37.5t16.5 43.5l287 595l-287 596q-18 45 -29 81q-5 -19 -12.5 -39t-15.5 -42z" />
<glyph unicode="&#xe000;" horiz-adv-x="1038" d="M0 1039h1039v-1039h-1039v1039z" />
<glyph horiz-adv-x="581" d="M286 1824q32 0 46 -7t30 -26l172 -208h-148q-20 0 -31.5 6.5t-26.5 19.5l-241 215h199z" />
<glyph horiz-adv-x="581" d="M344 1705q0 -20 -8.5 -38.5t-23 -32.5t-33.5 -21.5t-40 -7.5q-20 0 -38.5 7.5t-32.5 21.5t-22.5 32t-8.5 39q0 22 8.5 40.5t22.5 33.5t32 23t39 8q22 0 40.5 -8t33 -23t23 -33.5t8.5 -40.5zM696 1705q0 -20 -8 -38.5t-22.5 -32.5t-33 -21.5t-39.5 -7.5q-22 0 -40 7.5 t-32.5 21.5t-23 32t-8.5 39q0 22 8.5 40.5t23 33.5t32.5 23t40 8t40 -8t32.5 -23t22.5 -33.5t8 -40.5z" />
<glyph horiz-adv-x="581" d="M793 1824l-295 -215q-17 -12 -30 -19t-34 -7h-148l223 208q10 9 19 16t18 10.5t20.5 5t27.5 1.5h199z" />
<glyph horiz-adv-x="581" d="M728 1583h-134q-11 0 -24 3t-21 10l-117 110l-145 -110q-9 -7 -23 -10t-26 -3h-134l252 214h173z" />
<glyph horiz-adv-x="581" d="M759 1797l-252 -214h-173l-199 214h134q11 0 24 -3t21 -10l117 -110l146 110q9 7 22.5 10t26.5 3h133z" />
<glyph horiz-adv-x="581" d="M555 1729q0 -25 -10 -47.5t-27.5 -39.5t-40.5 -27t-49 -10q-25 0 -47.5 10t-39 27t-26.5 39.5t-10 47.5q0 26 10 48t26.5 39.5t39 27.5t47.5 10q26 0 49 -10t40.5 -27.5t27.5 -39.5t10 -48z" />
<glyph horiz-adv-x="581" d="M224 1699q0 40 15.5 73.5t42 57.5t61.5 37.5t74 13.5q40 0 75 -13.5t62 -37.5t42.5 -57.5t15.5 -73.5q0 -39 -15.5 -72t-42.5 -56.5t-62 -36.5t-75 -13q-39 0 -74 13t-61.5 36.5t-42 56.5t-15.5 72zM315 1699q0 -44 28 -73.5t76 -29.5q46 0 74 29.5t28 73.5 q0 46 -28 74.5t-74 28.5q-48 0 -76 -28.5t-28 -74.5z" />
<glyph horiz-adv-x="581" d="M552 1700q31 0 52 21.5t26 59.5h96q-5 -43 -20.5 -79.5t-39 -63.5t-55 -42t-69.5 -15q-33 0 -61.5 14t-54.5 30t-49.5 30t-44.5 14q-31 0 -51 -23t-25 -61h-99q5 43 21 80t39.5 64t55 42t69.5 15q34 0 62.5 -13.5t54 -30t48.5 -29.5t45 -13z" />
<glyph horiz-adv-x="581" d="M0 0z" />
<hkern u1="&#x22;" u2="&#x2206;" k="175" />
<hkern u1="&#x22;" u2="&#x203a;" k="85" />
<hkern u1="&#x22;" u2="&#x2039;" k="85" />
<hkern u1="&#x22;" u2="&#x2026;" k="94" />
<hkern u1="&#x22;" u2="&#x2022;" k="85" />
<hkern u1="&#x22;" u2="&#x201e;" k="94" />
<hkern u1="&#x22;" u2="&#x201a;" k="94" />
<hkern u1="&#x22;" u2="&#x2014;" k="85" />
<hkern u1="&#x22;" u2="&#x2013;" k="85" />
<hkern u1="&#x22;" u2="&#x178;" k="-46" />
<hkern u1="&#x22;" u2="&#x153;" k="93" />
<hkern u1="&#x22;" u2="&#x119;" k="93" />
<hkern u1="&#x22;" u2="&#x107;" k="93" />
<hkern u1="&#x22;" u2="&#x105;" k="93" />
<hkern u1="&#x22;" u2="&#x104;" k="175" />
<hkern u1="&#x22;" u2="&#xf8;" k="93" />
<hkern u1="&#x22;" u2="&#xf6;" k="93" />
<hkern u1="&#x22;" u2="&#xf5;" k="93" />
<hkern u1="&#x22;" u2="&#xf4;" k="93" />
<hkern u1="&#x22;" u2="&#xf3;" k="93" />
<hkern u1="&#x22;" u2="&#xf2;" k="93" />
<hkern u1="&#x22;" u2="&#xf0;" k="93" />
<hkern u1="&#x22;" u2="&#xeb;" k="93" />
<hkern u1="&#x22;" u2="&#xea;" k="93" />
<hkern u1="&#x22;" u2="&#xe9;" k="93" />
<hkern u1="&#x22;" u2="&#xe8;" k="93" />
<hkern u1="&#x22;" u2="&#xe7;" k="93" />
<hkern u1="&#x22;" u2="&#xe6;" k="93" />
<hkern u1="&#x22;" u2="&#xe5;" k="93" />
<hkern u1="&#x22;" u2="&#xe4;" k="93" />
<hkern u1="&#x22;" u2="&#xe3;" k="93" />
<hkern u1="&#x22;" u2="&#xe2;" k="93" />
<hkern u1="&#x22;" u2="&#xe1;" k="93" />
<hkern u1="&#x22;" u2="&#xe0;" k="93" />
<hkern u1="&#x22;" u2="&#xdd;" k="-46" />
<hkern u1="&#x22;" u2="&#xc6;" k="175" />
<hkern u1="&#x22;" u2="&#xc5;" k="175" />
<hkern u1="&#x22;" u2="&#xc4;" k="175" />
<hkern u1="&#x22;" u2="&#xc3;" k="175" />
<hkern u1="&#x22;" u2="&#xc2;" k="175" />
<hkern u1="&#x22;" u2="&#xc1;" k="175" />
<hkern u1="&#x22;" u2="&#xc0;" k="175" />
<hkern u1="&#x22;" u2="&#xbb;" k="85" />
<hkern u1="&#x22;" u2="&#xb7;" k="85" />
<hkern u1="&#x22;" u2="&#xab;" k="85" />
<hkern u1="&#x22;" u2="q" k="93" />
<hkern u1="&#x22;" u2="o" k="93" />
<hkern u1="&#x22;" u2="e" k="93" />
<hkern u1="&#x22;" u2="d" k="93" />
<hkern u1="&#x22;" u2="c" k="93" />
<hkern u1="&#x22;" u2="a" k="93" />
<hkern u1="&#x22;" u2="\" k="-55" />
<hkern u1="&#x22;" u2="Y" k="-46" />
<hkern u1="&#x22;" u2="W" k="-49" />
<hkern u1="&#x22;" u2="V" k="-55" />
<hkern u1="&#x22;" u2="A" k="175" />
<hkern u1="&#x22;" u2="&#x2f;" k="175" />
<hkern u1="&#x22;" u2="&#x2e;" k="94" />
<hkern u1="&#x22;" u2="&#x2d;" k="85" />
<hkern u1="&#x22;" u2="&#x2c;" k="94" />
<hkern u1="&#x22;" u2="&#x26;" k="175" />
<hkern u1="&#x27;" u2="&#x2206;" k="175" />
<hkern u1="&#x27;" u2="&#x203a;" k="85" />
<hkern u1="&#x27;" u2="&#x2039;" k="85" />
<hkern u1="&#x27;" u2="&#x2026;" k="94" />
<hkern u1="&#x27;" u2="&#x2022;" k="85" />
<hkern u1="&#x27;" u2="&#x201e;" k="94" />
<hkern u1="&#x27;" u2="&#x201a;" k="94" />
<hkern u1="&#x27;" u2="&#x2014;" k="85" />
<hkern u1="&#x27;" u2="&#x2013;" k="85" />
<hkern u1="&#x27;" u2="&#x178;" k="-46" />
<hkern u1="&#x27;" u2="&#x153;" k="93" />
<hkern u1="&#x27;" u2="&#x119;" k="93" />
<hkern u1="&#x27;" u2="&#x107;" k="93" />
<hkern u1="&#x27;" u2="&#x105;" k="93" />
<hkern u1="&#x27;" u2="&#x104;" k="175" />
<hkern u1="&#x27;" u2="&#xf8;" k="93" />
<hkern u1="&#x27;" u2="&#xf6;" k="93" />
<hkern u1="&#x27;" u2="&#xf5;" k="93" />
<hkern u1="&#x27;" u2="&#xf4;" k="93" />
<hkern u1="&#x27;" u2="&#xf3;" k="93" />
<hkern u1="&#x27;" u2="&#xf2;" k="93" />
<hkern u1="&#x27;" u2="&#xf0;" k="93" />
<hkern u1="&#x27;" u2="&#xeb;" k="93" />
<hkern u1="&#x27;" u2="&#xea;" k="93" />
<hkern u1="&#x27;" u2="&#xe9;" k="93" />
<hkern u1="&#x27;" u2="&#xe8;" k="93" />
<hkern u1="&#x27;" u2="&#xe7;" k="93" />
<hkern u1="&#x27;" u2="&#xe6;" k="93" />
<hkern u1="&#x27;" u2="&#xe5;" k="93" />
<hkern u1="&#x27;" u2="&#xe4;" k="93" />
<hkern u1="&#x27;" u2="&#xe3;" k="93" />
<hkern u1="&#x27;" u2="&#xe2;" k="93" />
<hkern u1="&#x27;" u2="&#xe1;" k="93" />
<hkern u1="&#x27;" u2="&#xe0;" k="93" />
<hkern u1="&#x27;" u2="&#xdd;" k="-46" />
<hkern u1="&#x27;" u2="&#xc6;" k="175" />
<hkern u1="&#x27;" u2="&#xc5;" k="175" />
<hkern u1="&#x27;" u2="&#xc4;" k="175" />
<hkern u1="&#x27;" u2="&#xc3;" k="175" />
<hkern u1="&#x27;" u2="&#xc2;" k="175" />
<hkern u1="&#x27;" u2="&#xc1;" k="175" />
<hkern u1="&#x27;" u2="&#xc0;" k="175" />
<hkern u1="&#x27;" u2="&#xbb;" k="85" />
<hkern u1="&#x27;" u2="&#xb7;" k="85" />
<hkern u1="&#x27;" u2="&#xab;" k="85" />
<hkern u1="&#x27;" u2="q" k="93" />
<hkern u1="&#x27;" u2="o" k="93" />
<hkern u1="&#x27;" u2="e" k="93" />
<hkern u1="&#x27;" u2="d" k="93" />
<hkern u1="&#x27;" u2="c" k="93" />
<hkern u1="&#x27;" u2="a" k="93" />
<hkern u1="&#x27;" u2="\" k="-55" />
<hkern u1="&#x27;" u2="Y" k="-46" />
<hkern u1="&#x27;" u2="W" k="-49" />
<hkern u1="&#x27;" u2="V" k="-55" />
<hkern u1="&#x27;" u2="A" k="175" />
<hkern u1="&#x27;" u2="&#x2f;" k="175" />
<hkern u1="&#x27;" u2="&#x2e;" k="94" />
<hkern u1="&#x27;" u2="&#x2d;" k="85" />
<hkern u1="&#x27;" u2="&#x2c;" k="94" />
<hkern u1="&#x27;" u2="&#x26;" k="175" />
<hkern u1="&#x28;" u2="&#x153;" k="32" />
<hkern u1="&#x28;" u2="&#x152;" k="41" />
<hkern u1="&#x28;" u2="&#x119;" k="32" />
<hkern u1="&#x28;" u2="&#x107;" k="32" />
<hkern u1="&#x28;" u2="&#x106;" k="41" />
<hkern u1="&#x28;" u2="&#x105;" k="32" />
<hkern u1="&#x28;" u2="&#xf8;" k="32" />
<hkern u1="&#x28;" u2="&#xf6;" k="32" />
<hkern u1="&#x28;" u2="&#xf5;" k="32" />
<hkern u1="&#x28;" u2="&#xf4;" k="32" />
<hkern u1="&#x28;" u2="&#xf3;" k="32" />
<hkern u1="&#x28;" u2="&#xf2;" k="32" />
<hkern u1="&#x28;" u2="&#xf0;" k="32" />
<hkern u1="&#x28;" u2="&#xeb;" k="32" />
<hkern u1="&#x28;" u2="&#xea;" k="32" />
<hkern u1="&#x28;" u2="&#xe9;" k="32" />
<hkern u1="&#x28;" u2="&#xe8;" k="32" />
<hkern u1="&#x28;" u2="&#xe7;" k="32" />
<hkern u1="&#x28;" u2="&#xe6;" k="32" />
<hkern u1="&#x28;" u2="&#xe5;" k="32" />
<hkern u1="&#x28;" u2="&#xe4;" k="32" />
<hkern u1="&#x28;" u2="&#xe3;" k="32" />
<hkern u1="&#x28;" u2="&#xe2;" k="32" />
<hkern u1="&#x28;" u2="&#xe1;" k="32" />
<hkern u1="&#x28;" u2="&#xe0;" k="32" />
<hkern u1="&#x28;" u2="&#xd8;" k="41" />
<hkern u1="&#x28;" u2="&#xd6;" k="41" />
<hkern u1="&#x28;" u2="&#xd5;" k="41" />
<hkern u1="&#x28;" u2="&#xd4;" k="41" />
<hkern u1="&#x28;" u2="&#xd3;" k="41" />
<hkern u1="&#x28;" u2="&#xd2;" k="41" />
<hkern u1="&#x28;" u2="&#xc7;" k="41" />
<hkern u1="&#x28;" u2="&#xae;" k="41" />
<hkern u1="&#x28;" u2="&#xa9;" k="41" />
<hkern u1="&#x28;" u2="q" k="32" />
<hkern u1="&#x28;" u2="o" k="32" />
<hkern u1="&#x28;" u2="e" k="32" />
<hkern u1="&#x28;" u2="d" k="32" />
<hkern u1="&#x28;" u2="c" k="32" />
<hkern u1="&#x28;" u2="a" k="32" />
<hkern u1="&#x28;" u2="Q" k="41" />
<hkern u1="&#x28;" u2="O" k="41" />
<hkern u1="&#x28;" u2="G" k="41" />
<hkern u1="&#x28;" u2="C" k="41" />
<hkern u1="&#x28;" u2="&#x40;" k="41" />
<hkern u1="&#x2a;" u2="&#x2206;" k="175" />
<hkern u1="&#x2a;" u2="&#x203a;" k="85" />
<hkern u1="&#x2a;" u2="&#x2039;" k="85" />
<hkern u1="&#x2a;" u2="&#x2026;" k="94" />
<hkern u1="&#x2a;" u2="&#x2022;" k="85" />
<hkern u1="&#x2a;" u2="&#x201e;" k="94" />
<hkern u1="&#x2a;" u2="&#x201a;" k="94" />
<hkern u1="&#x2a;" u2="&#x2014;" k="85" />
<hkern u1="&#x2a;" u2="&#x2013;" k="85" />
<hkern u1="&#x2a;" u2="&#x178;" k="-46" />
<hkern u1="&#x2a;" u2="&#x153;" k="93" />
<hkern u1="&#x2a;" u2="&#x119;" k="93" />
<hkern u1="&#x2a;" u2="&#x107;" k="93" />
<hkern u1="&#x2a;" u2="&#x105;" k="93" />
<hkern u1="&#x2a;" u2="&#x104;" k="175" />
<hkern u1="&#x2a;" u2="&#xf8;" k="93" />
<hkern u1="&#x2a;" u2="&#xf6;" k="93" />
<hkern u1="&#x2a;" u2="&#xf5;" k="93" />
<hkern u1="&#x2a;" u2="&#xf4;" k="93" />
<hkern u1="&#x2a;" u2="&#xf3;" k="93" />
<hkern u1="&#x2a;" u2="&#xf2;" k="93" />
<hkern u1="&#x2a;" u2="&#xf0;" k="93" />
<hkern u1="&#x2a;" u2="&#xeb;" k="93" />
<hkern u1="&#x2a;" u2="&#xea;" k="93" />
<hkern u1="&#x2a;" u2="&#xe9;" k="93" />
<hkern u1="&#x2a;" u2="&#xe8;" k="93" />
<hkern u1="&#x2a;" u2="&#xe7;" k="93" />
<hkern u1="&#x2a;" u2="&#xe6;" k="93" />
<hkern u1="&#x2a;" u2="&#xe5;" k="93" />
<hkern u1="&#x2a;" u2="&#xe4;" k="93" />
<hkern u1="&#x2a;" u2="&#xe3;" k="93" />
<hkern u1="&#x2a;" u2="&#xe2;" k="93" />
<hkern u1="&#x2a;" u2="&#xe1;" k="93" />
<hkern u1="&#x2a;" u2="&#xe0;" k="93" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-46" />
<hkern u1="&#x2a;" u2="&#xc6;" k="175" />
<hkern u1="&#x2a;" u2="&#xc5;" k="175" />
<hkern u1="&#x2a;" u2="&#xc4;" k="175" />
<hkern u1="&#x2a;" u2="&#xc3;" k="175" />
<hkern u1="&#x2a;" u2="&#xc2;" k="175" />
<hkern u1="&#x2a;" u2="&#xc1;" k="175" />
<hkern u1="&#x2a;" u2="&#xc0;" k="175" />
<hkern u1="&#x2a;" u2="&#xbb;" k="85" />
<hkern u1="&#x2a;" u2="&#xb7;" k="85" />
<hkern u1="&#x2a;" u2="&#xab;" k="85" />
<hkern u1="&#x2a;" u2="q" k="93" />
<hkern u1="&#x2a;" u2="o" k="93" />
<hkern u1="&#x2a;" u2="e" k="93" />
<hkern u1="&#x2a;" u2="d" k="93" />
<hkern u1="&#x2a;" u2="c" k="93" />
<hkern u1="&#x2a;" u2="a" k="93" />
<hkern u1="&#x2a;" u2="\" k="-55" />
<hkern u1="&#x2a;" u2="Y" k="-46" />
<hkern u1="&#x2a;" u2="W" k="-49" />
<hkern u1="&#x2a;" u2="V" k="-55" />
<hkern u1="&#x2a;" u2="A" k="175" />
<hkern u1="&#x2a;" u2="&#x2f;" k="175" />
<hkern u1="&#x2a;" u2="&#x2e;" k="94" />
<hkern u1="&#x2a;" u2="&#x2d;" k="85" />
<hkern u1="&#x2a;" u2="&#x2c;" k="94" />
<hkern u1="&#x2a;" u2="&#x26;" k="175" />
<hkern u1="&#x2c;" u2="&#x203a;" k="202" />
<hkern u1="&#x2c;" u2="&#x2039;" k="202" />
<hkern u1="&#x2c;" u2="&#x2022;" k="202" />
<hkern u1="&#x2c;" u2="&#x201d;" k="123" />
<hkern u1="&#x2c;" u2="&#x201c;" k="123" />
<hkern u1="&#x2c;" u2="&#x2019;" k="123" />
<hkern u1="&#x2c;" u2="&#x2018;" k="123" />
<hkern u1="&#x2c;" u2="&#x2014;" k="202" />
<hkern u1="&#x2c;" u2="&#x2013;" k="202" />
<hkern u1="&#x2c;" u2="&#x178;" k="202" />
<hkern u1="&#x2c;" u2="&#x152;" k="56" />
<hkern u1="&#x2c;" u2="&#x106;" k="56" />
<hkern u1="&#x2c;" u2="&#xff;" k="145" />
<hkern u1="&#x2c;" u2="&#xfd;" k="145" />
<hkern u1="&#x2c;" u2="&#xdd;" k="202" />
<hkern u1="&#x2c;" u2="&#xd8;" k="56" />
<hkern u1="&#x2c;" u2="&#xd6;" k="56" />
<hkern u1="&#x2c;" u2="&#xd5;" k="56" />
<hkern u1="&#x2c;" u2="&#xd4;" k="56" />
<hkern u1="&#x2c;" u2="&#xd3;" k="56" />
<hkern u1="&#x2c;" u2="&#xd2;" k="56" />
<hkern u1="&#x2c;" u2="&#xc7;" k="56" />
<hkern u1="&#x2c;" u2="&#xbb;" k="202" />
<hkern u1="&#x2c;" u2="&#xba;" k="123" />
<hkern u1="&#x2c;" u2="&#xb7;" k="202" />
<hkern u1="&#x2c;" u2="&#xb0;" k="123" />
<hkern u1="&#x2c;" u2="&#xae;" k="56" />
<hkern u1="&#x2c;" u2="&#xab;" k="202" />
<hkern u1="&#x2c;" u2="&#xaa;" k="123" />
<hkern u1="&#x2c;" u2="&#xa9;" k="56" />
<hkern u1="&#x2c;" u2="y" k="135" />
<hkern u1="&#x2c;" u2="w" k="74" />
<hkern u1="&#x2c;" u2="v" k="145" />
<hkern u1="&#x2c;" u2="\" k="217" />
<hkern u1="&#x2c;" u2="Y" k="202" />
<hkern u1="&#x2c;" u2="W" k="135" />
<hkern u1="&#x2c;" u2="V" k="217" />
<hkern u1="&#x2c;" u2="T" k="208" />
<hkern u1="&#x2c;" u2="Q" k="56" />
<hkern u1="&#x2c;" u2="O" k="56" />
<hkern u1="&#x2c;" u2="G" k="56" />
<hkern u1="&#x2c;" u2="C" k="56" />
<hkern u1="&#x2c;" u2="&#x40;" k="56" />
<hkern u1="&#x2c;" u2="&#x2d;" k="202" />
<hkern u1="&#x2c;" u2="&#x2a;" k="123" />
<hkern u1="&#x2c;" u2="&#x27;" k="123" />
<hkern u1="&#x2c;" u2="&#x22;" k="123" />
<hkern u1="&#x2d;" u2="&#x2206;" k="53" />
<hkern u1="&#x2d;" u2="&#x2026;" k="166" />
<hkern u1="&#x2d;" u2="&#x201e;" k="166" />
<hkern u1="&#x2d;" u2="&#x201d;" k="85" />
<hkern u1="&#x2d;" u2="&#x201c;" k="85" />
<hkern u1="&#x2d;" u2="&#x201a;" k="166" />
<hkern u1="&#x2d;" u2="&#x2019;" k="85" />
<hkern u1="&#x2d;" u2="&#x2018;" k="85" />
<hkern u1="&#x2d;" u2="&#x17d;" k="56" />
<hkern u1="&#x2d;" u2="&#x17b;" k="56" />
<hkern u1="&#x2d;" u2="&#x179;" k="56" />
<hkern u1="&#x2d;" u2="&#x178;" k="197" />
<hkern u1="&#x2d;" u2="&#x104;" k="53" />
<hkern u1="&#x2d;" u2="&#xdd;" k="197" />
<hkern u1="&#x2d;" u2="&#xc6;" k="53" />
<hkern u1="&#x2d;" u2="&#xc5;" k="53" />
<hkern u1="&#x2d;" u2="&#xc4;" k="53" />
<hkern u1="&#x2d;" u2="&#xc3;" k="53" />
<hkern u1="&#x2d;" u2="&#xc2;" k="53" />
<hkern u1="&#x2d;" u2="&#xc1;" k="53" />
<hkern u1="&#x2d;" u2="&#xc0;" k="53" />
<hkern u1="&#x2d;" u2="&#xba;" k="85" />
<hkern u1="&#x2d;" u2="&#xb0;" k="85" />
<hkern u1="&#x2d;" u2="&#xaa;" k="85" />
<hkern u1="&#x2d;" u2="\" k="125" />
<hkern u1="&#x2d;" u2="Z" k="56" />
<hkern u1="&#x2d;" u2="Y" k="197" />
<hkern u1="&#x2d;" u2="X" k="72" />
<hkern u1="&#x2d;" u2="V" k="125" />
<hkern u1="&#x2d;" u2="T" k="196" />
<hkern u1="&#x2d;" u2="A" k="53" />
<hkern u1="&#x2d;" u2="&#x2f;" k="53" />
<hkern u1="&#x2d;" u2="&#x2e;" k="166" />
<hkern u1="&#x2d;" u2="&#x2c;" k="166" />
<hkern u1="&#x2d;" u2="&#x2a;" k="85" />
<hkern u1="&#x2d;" u2="&#x27;" k="85" />
<hkern u1="&#x2d;" u2="&#x26;" k="53" />
<hkern u1="&#x2d;" u2="&#x22;" k="85" />
<hkern u1="&#x2e;" u2="&#x203a;" k="202" />
<hkern u1="&#x2e;" u2="&#x2039;" k="202" />
<hkern u1="&#x2e;" u2="&#x2022;" k="202" />
<hkern u1="&#x2e;" u2="&#x201d;" k="123" />
<hkern u1="&#x2e;" u2="&#x201c;" k="123" />
<hkern u1="&#x2e;" u2="&#x2019;" k="123" />
<hkern u1="&#x2e;" u2="&#x2018;" k="123" />
<hkern u1="&#x2e;" u2="&#x2014;" k="202" />
<hkern u1="&#x2e;" u2="&#x2013;" k="202" />
<hkern u1="&#x2e;" u2="&#x178;" k="202" />
<hkern u1="&#x2e;" u2="&#x152;" k="56" />
<hkern u1="&#x2e;" u2="&#x106;" k="56" />
<hkern u1="&#x2e;" u2="&#xff;" k="145" />
<hkern u1="&#x2e;" u2="&#xfd;" k="145" />
<hkern u1="&#x2e;" u2="&#xdd;" k="202" />
<hkern u1="&#x2e;" u2="&#xd8;" k="56" />
<hkern u1="&#x2e;" u2="&#xd6;" k="56" />
<hkern u1="&#x2e;" u2="&#xd5;" k="56" />
<hkern u1="&#x2e;" u2="&#xd4;" k="56" />
<hkern u1="&#x2e;" u2="&#xd3;" k="56" />
<hkern u1="&#x2e;" u2="&#xd2;" k="56" />
<hkern u1="&#x2e;" u2="&#xc7;" k="56" />
<hkern u1="&#x2e;" u2="&#xbb;" k="202" />
<hkern u1="&#x2e;" u2="&#xba;" k="123" />
<hkern u1="&#x2e;" u2="&#xb7;" k="202" />
<hkern u1="&#x2e;" u2="&#xb0;" k="123" />
<hkern u1="&#x2e;" u2="&#xae;" k="56" />
<hkern u1="&#x2e;" u2="&#xab;" k="202" />
<hkern u1="&#x2e;" u2="&#xaa;" k="123" />
<hkern u1="&#x2e;" u2="&#xa9;" k="56" />
<hkern u1="&#x2e;" u2="y" k="135" />
<hkern u1="&#x2e;" u2="w" k="74" />
<hkern u1="&#x2e;" u2="v" k="145" />
<hkern u1="&#x2e;" u2="\" k="217" />
<hkern u1="&#x2e;" u2="Y" k="202" />
<hkern u1="&#x2e;" u2="W" k="135" />
<hkern u1="&#x2e;" u2="V" k="217" />
<hkern u1="&#x2e;" u2="T" k="208" />
<hkern u1="&#x2e;" u2="Q" k="56" />
<hkern u1="&#x2e;" u2="O" k="56" />
<hkern u1="&#x2e;" u2="G" k="56" />
<hkern u1="&#x2e;" u2="C" k="56" />
<hkern u1="&#x2e;" u2="&#x40;" k="56" />
<hkern u1="&#x2e;" u2="&#x2d;" k="202" />
<hkern u1="&#x2e;" u2="&#x2a;" k="123" />
<hkern u1="&#x2e;" u2="&#x27;" k="123" />
<hkern u1="&#x2e;" u2="&#x22;" k="123" />
<hkern u1="&#x2f;" u2="&#x2206;" k="117" />
<hkern u1="&#x2f;" u2="&#x203a;" k="125" />
<hkern u1="&#x2f;" u2="&#x2039;" k="125" />
<hkern u1="&#x2f;" u2="&#x2026;" k="196" />
<hkern u1="&#x2f;" u2="&#x2022;" k="125" />
<hkern u1="&#x2f;" u2="&#x201e;" k="196" />
<hkern u1="&#x2f;" u2="&#x201d;" k="-55" />
<hkern u1="&#x2f;" u2="&#x201c;" k="-55" />
<hkern u1="&#x2f;" u2="&#x201a;" k="196" />
<hkern u1="&#x2f;" u2="&#x2019;" k="-55" />
<hkern u1="&#x2f;" u2="&#x2018;" k="-55" />
<hkern u1="&#x2f;" u2="&#x2014;" k="125" />
<hkern u1="&#x2f;" u2="&#x2013;" k="125" />
<hkern u1="&#x2f;" u2="&#x17e;" k="74" />
<hkern u1="&#x2f;" u2="&#x17c;" k="74" />
<hkern u1="&#x2f;" u2="&#x17a;" k="74" />
<hkern u1="&#x2f;" u2="&#x161;" k="115" />
<hkern u1="&#x2f;" u2="&#x15b;" k="115" />
<hkern u1="&#x2f;" u2="&#x153;" k="120" />
<hkern u1="&#x2f;" u2="&#x152;" k="41" />
<hkern u1="&#x2f;" u2="&#x144;" k="87" />
<hkern u1="&#x2f;" u2="&#x119;" k="120" />
<hkern u1="&#x2f;" u2="&#x107;" k="120" />
<hkern u1="&#x2f;" u2="&#x106;" k="41" />
<hkern u1="&#x2f;" u2="&#x105;" k="120" />
<hkern u1="&#x2f;" u2="&#x104;" k="117" />
<hkern u1="&#x2f;" u2="&#xff;" k="37" />
<hkern u1="&#x2f;" u2="&#xfd;" k="37" />
<hkern u1="&#x2f;" u2="&#xfc;" k="87" />
<hkern u1="&#x2f;" u2="&#xfb;" k="87" />
<hkern u1="&#x2f;" u2="&#xfa;" k="87" />
<hkern u1="&#x2f;" u2="&#xf9;" k="87" />
<hkern u1="&#x2f;" u2="&#xf8;" k="120" />
<hkern u1="&#x2f;" u2="&#xf6;" k="120" />
<hkern u1="&#x2f;" u2="&#xf5;" k="120" />
<hkern u1="&#x2f;" u2="&#xf4;" k="120" />
<hkern u1="&#x2f;" u2="&#xf3;" k="120" />
<hkern u1="&#x2f;" u2="&#xf2;" k="120" />
<hkern u1="&#x2f;" u2="&#xf1;" k="87" />
<hkern u1="&#x2f;" u2="&#xf0;" k="120" />
<hkern u1="&#x2f;" u2="&#xeb;" k="120" />
<hkern u1="&#x2f;" u2="&#xea;" k="120" />
<hkern u1="&#x2f;" u2="&#xe9;" k="120" />
<hkern u1="&#x2f;" u2="&#xe8;" k="120" />
<hkern u1="&#x2f;" u2="&#xe7;" k="120" />
<hkern u1="&#x2f;" u2="&#xe6;" k="120" />
<hkern u1="&#x2f;" u2="&#xe5;" k="120" />
<hkern u1="&#x2f;" u2="&#xe4;" k="120" />
<hkern u1="&#x2f;" u2="&#xe3;" k="120" />
<hkern u1="&#x2f;" u2="&#xe2;" k="120" />
<hkern u1="&#x2f;" u2="&#xe1;" k="120" />
<hkern u1="&#x2f;" u2="&#xe0;" k="120" />
<hkern u1="&#x2f;" u2="&#xd8;" k="41" />
<hkern u1="&#x2f;" u2="&#xd6;" k="41" />
<hkern u1="&#x2f;" u2="&#xd5;" k="41" />
<hkern u1="&#x2f;" u2="&#xd4;" k="41" />
<hkern u1="&#x2f;" u2="&#xd3;" k="41" />
<hkern u1="&#x2f;" u2="&#xd2;" k="41" />
<hkern u1="&#x2f;" u2="&#xc7;" k="41" />
<hkern u1="&#x2f;" u2="&#xc6;" k="117" />
<hkern u1="&#x2f;" u2="&#xc5;" k="117" />
<hkern u1="&#x2f;" u2="&#xc4;" k="117" />
<hkern u1="&#x2f;" u2="&#xc3;" k="117" />
<hkern u1="&#x2f;" u2="&#xc2;" k="117" />
<hkern u1="&#x2f;" u2="&#xc1;" k="117" />
<hkern u1="&#x2f;" u2="&#xc0;" k="117" />
<hkern u1="&#x2f;" u2="&#xbb;" k="125" />
<hkern u1="&#x2f;" u2="&#xba;" k="-55" />
<hkern u1="&#x2f;" u2="&#xb7;" k="125" />
<hkern u1="&#x2f;" u2="&#xb5;" k="87" />
<hkern u1="&#x2f;" u2="&#xb0;" k="-55" />
<hkern u1="&#x2f;" u2="&#xae;" k="41" />
<hkern u1="&#x2f;" u2="&#xab;" k="125" />
<hkern u1="&#x2f;" u2="&#xaa;" k="-55" />
<hkern u1="&#x2f;" u2="&#xa9;" k="41" />
<hkern u1="&#x2f;" u2="z" k="74" />
<hkern u1="&#x2f;" u2="y" k="37" />
<hkern u1="&#x2f;" u2="w" k="37" />
<hkern u1="&#x2f;" u2="v" k="37" />
<hkern u1="&#x2f;" u2="u" k="87" />
<hkern u1="&#x2f;" u2="s" k="115" />
<hkern u1="&#x2f;" u2="r" k="87" />
<hkern u1="&#x2f;" u2="q" k="120" />
<hkern u1="&#x2f;" u2="p" k="87" />
<hkern u1="&#x2f;" u2="o" k="120" />
<hkern u1="&#x2f;" u2="n" k="87" />
<hkern u1="&#x2f;" u2="m" k="87" />
<hkern u1="&#x2f;" u2="e" k="120" />
<hkern u1="&#x2f;" u2="d" k="120" />
<hkern u1="&#x2f;" u2="c" k="120" />
<hkern u1="&#x2f;" u2="a" k="120" />
<hkern u1="&#x2f;" u2="Q" k="41" />
<hkern u1="&#x2f;" u2="O" k="41" />
<hkern u1="&#x2f;" u2="J" k="155" />
<hkern u1="&#x2f;" u2="G" k="41" />
<hkern u1="&#x2f;" u2="C" k="41" />
<hkern u1="&#x2f;" u2="A" k="117" />
<hkern u1="&#x2f;" u2="&#x40;" k="41" />
<hkern u1="&#x2f;" u2="&#x3f;" k="-59" />
<hkern u1="&#x2f;" u2="&#x3b;" k="87" />
<hkern u1="&#x2f;" u2="&#x3a;" k="87" />
<hkern u1="&#x2f;" u2="&#x2f;" k="117" />
<hkern u1="&#x2f;" u2="&#x2e;" k="196" />
<hkern u1="&#x2f;" u2="&#x2d;" k="125" />
<hkern u1="&#x2f;" u2="&#x2c;" k="196" />
<hkern u1="&#x2f;" u2="&#x2a;" k="-55" />
<hkern u1="&#x2f;" u2="&#x27;" k="-55" />
<hkern u1="&#x2f;" u2="&#x26;" k="117" />
<hkern u1="&#x2f;" u2="&#x22;" k="-55" />
<hkern u1="&#x40;" u2="&#x2206;" k="37" />
<hkern u1="&#x40;" u2="&#x201d;" k="56" />
<hkern u1="&#x40;" u2="&#x201c;" k="56" />
<hkern u1="&#x40;" u2="&#x2019;" k="56" />
<hkern u1="&#x40;" u2="&#x2018;" k="56" />
<hkern u1="&#x40;" u2="&#x17d;" k="71" />
<hkern u1="&#x40;" u2="&#x17b;" k="71" />
<hkern u1="&#x40;" u2="&#x179;" k="71" />
<hkern u1="&#x40;" u2="&#x178;" k="82" />
<hkern u1="&#x40;" u2="&#x104;" k="37" />
<hkern u1="&#x40;" u2="&#xdd;" k="82" />
<hkern u1="&#x40;" u2="&#xc6;" k="37" />
<hkern u1="&#x40;" u2="&#xc5;" k="37" />
<hkern u1="&#x40;" u2="&#xc4;" k="37" />
<hkern u1="&#x40;" u2="&#xc3;" k="37" />
<hkern u1="&#x40;" u2="&#xc2;" k="37" />
<hkern u1="&#x40;" u2="&#xc1;" k="37" />
<hkern u1="&#x40;" u2="&#xc0;" k="37" />
<hkern u1="&#x40;" u2="&#xba;" k="56" />
<hkern u1="&#x40;" u2="&#xb0;" k="56" />
<hkern u1="&#x40;" u2="&#xaa;" k="56" />
<hkern u1="&#x40;" u2="&#x7d;" k="41" />
<hkern u1="&#x40;" u2="]" k="41" />
<hkern u1="&#x40;" u2="\" k="52" />
<hkern u1="&#x40;" u2="Z" k="71" />
<hkern u1="&#x40;" u2="Y" k="82" />
<hkern u1="&#x40;" u2="X" k="70" />
<hkern u1="&#x40;" u2="V" k="52" />
<hkern u1="&#x40;" u2="T" k="99" />
<hkern u1="&#x40;" u2="A" k="37" />
<hkern u1="&#x40;" u2="&#x2f;" k="37" />
<hkern u1="&#x40;" u2="&#x2a;" k="56" />
<hkern u1="&#x40;" u2="&#x29;" k="41" />
<hkern u1="&#x40;" u2="&#x27;" k="56" />
<hkern u1="&#x40;" u2="&#x26;" k="37" />
<hkern u1="&#x40;" u2="&#x22;" k="56" />
<hkern u1="A" u2="&#x203a;" k="53" />
<hkern u1="A" u2="&#x2039;" k="53" />
<hkern u1="A" u2="&#x2022;" k="53" />
<hkern u1="A" u2="&#x201d;" k="186" />
<hkern u1="A" u2="&#x201c;" k="186" />
<hkern u1="A" u2="&#x2019;" k="186" />
<hkern u1="A" u2="&#x2018;" k="186" />
<hkern u1="A" u2="&#x2014;" k="53" />
<hkern u1="A" u2="&#x2013;" k="53" />
<hkern u1="A" u2="&#x178;" k="156" />
<hkern u1="A" u2="&#x152;" k="48" />
<hkern u1="A" u2="&#x106;" k="48" />
<hkern u1="A" u2="&#xff;" k="48" />
<hkern u1="A" u2="&#xfd;" k="48" />
<hkern u1="A" u2="&#xdd;" k="156" />
<hkern u1="A" u2="&#xdc;" k="41" />
<hkern u1="A" u2="&#xdb;" k="41" />
<hkern u1="A" u2="&#xda;" k="41" />
<hkern u1="A" u2="&#xd9;" k="41" />
<hkern u1="A" u2="&#xd8;" k="48" />
<hkern u1="A" u2="&#xd6;" k="48" />
<hkern u1="A" u2="&#xd5;" k="48" />
<hkern u1="A" u2="&#xd4;" k="48" />
<hkern u1="A" u2="&#xd3;" k="48" />
<hkern u1="A" u2="&#xd2;" k="48" />
<hkern u1="A" u2="&#xc7;" k="48" />
<hkern u1="A" u2="&#xbb;" k="53" />
<hkern u1="A" u2="&#xba;" k="186" />
<hkern u1="A" u2="&#xb7;" k="53" />
<hkern u1="A" u2="&#xb0;" k="186" />
<hkern u1="A" u2="&#xae;" k="48" />
<hkern u1="A" u2="&#xab;" k="53" />
<hkern u1="A" u2="&#xaa;" k="186" />
<hkern u1="A" u2="&#xa9;" k="48" />
<hkern u1="A" u2="y" k="48" />
<hkern u1="A" u2="w" k="33" />
<hkern u1="A" u2="v" k="48" />
<hkern u1="A" u2="t" k="58" />
<hkern u1="A" u2="\" k="117" />
<hkern u1="A" u2="Y" k="156" />
<hkern u1="A" u2="W" k="85" />
<hkern u1="A" u2="V" k="117" />
<hkern u1="A" u2="U" k="41" />
<hkern u1="A" u2="T" k="135" />
<hkern u1="A" u2="Q" k="48" />
<hkern u1="A" u2="O" k="48" />
<hkern u1="A" u2="J" k="-63" />
<hkern u1="A" u2="G" k="48" />
<hkern u1="A" u2="C" k="48" />
<hkern u1="A" u2="&#x40;" k="48" />
<hkern u1="A" u2="&#x2d;" k="53" />
<hkern u1="A" u2="&#x2a;" k="186" />
<hkern u1="A" u2="&#x27;" k="186" />
<hkern u1="A" u2="&#x22;" k="186" />
<hkern u1="C" u2="&#x203a;" k="131" />
<hkern u1="C" u2="&#x2039;" k="131" />
<hkern u1="C" u2="&#x2022;" k="131" />
<hkern u1="C" u2="&#x2014;" k="131" />
<hkern u1="C" u2="&#x2013;" k="131" />
<hkern u1="C" u2="&#xbb;" k="131" />
<hkern u1="C" u2="&#xb7;" k="131" />
<hkern u1="C" u2="&#xab;" k="131" />
<hkern u1="C" u2="&#x2d;" k="131" />
<hkern u1="D" u2="&#x2206;" k="37" />
<hkern u1="D" u2="&#x201d;" k="56" />
<hkern u1="D" u2="&#x201c;" k="56" />
<hkern u1="D" u2="&#x2019;" k="56" />
<hkern u1="D" u2="&#x2018;" k="56" />
<hkern u1="D" u2="&#x17d;" k="71" />
<hkern u1="D" u2="&#x17b;" k="71" />
<hkern u1="D" u2="&#x179;" k="71" />
<hkern u1="D" u2="&#x178;" k="82" />
<hkern u1="D" u2="&#x104;" k="37" />
<hkern u1="D" u2="&#xdd;" k="82" />
<hkern u1="D" u2="&#xc6;" k="37" />
<hkern u1="D" u2="&#xc5;" k="37" />
<hkern u1="D" u2="&#xc4;" k="37" />
<hkern u1="D" u2="&#xc3;" k="37" />
<hkern u1="D" u2="&#xc2;" k="37" />
<hkern u1="D" u2="&#xc1;" k="37" />
<hkern u1="D" u2="&#xc0;" k="37" />
<hkern u1="D" u2="&#xba;" k="56" />
<hkern u1="D" u2="&#xb0;" k="56" />
<hkern u1="D" u2="&#xaa;" k="56" />
<hkern u1="D" u2="&#x7d;" k="41" />
<hkern u1="D" u2="]" k="41" />
<hkern u1="D" u2="\" k="52" />
<hkern u1="D" u2="Z" k="71" />
<hkern u1="D" u2="Y" k="82" />
<hkern u1="D" u2="X" k="70" />
<hkern u1="D" u2="V" k="52" />
<hkern u1="D" u2="T" k="99" />
<hkern u1="D" u2="A" k="37" />
<hkern u1="D" u2="&#x2f;" k="37" />
<hkern u1="D" u2="&#x2a;" k="56" />
<hkern u1="D" u2="&#x29;" k="41" />
<hkern u1="D" u2="&#x27;" k="56" />
<hkern u1="D" u2="&#x26;" k="37" />
<hkern u1="D" u2="&#x22;" k="56" />
<hkern u1="F" u2="&#x2206;" k="135" />
<hkern u1="F" u2="&#x2026;" k="184" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x153;" k="71" />
<hkern u1="F" u2="&#x144;" k="61" />
<hkern u1="F" u2="&#x119;" k="71" />
<hkern u1="F" u2="&#x107;" k="71" />
<hkern u1="F" u2="&#x105;" k="71" />
<hkern u1="F" u2="&#x104;" k="135" />
<hkern u1="F" u2="&#xfc;" k="61" />
<hkern u1="F" u2="&#xfb;" k="61" />
<hkern u1="F" u2="&#xfa;" k="61" />
<hkern u1="F" u2="&#xf9;" k="61" />
<hkern u1="F" u2="&#xf8;" k="71" />
<hkern u1="F" u2="&#xf6;" k="71" />
<hkern u1="F" u2="&#xf5;" k="71" />
<hkern u1="F" u2="&#xf4;" k="71" />
<hkern u1="F" u2="&#xf3;" k="71" />
<hkern u1="F" u2="&#xf2;" k="71" />
<hkern u1="F" u2="&#xf1;" k="61" />
<hkern u1="F" u2="&#xf0;" k="71" />
<hkern u1="F" u2="&#xeb;" k="71" />
<hkern u1="F" u2="&#xea;" k="71" />
<hkern u1="F" u2="&#xe9;" k="71" />
<hkern u1="F" u2="&#xe8;" k="71" />
<hkern u1="F" u2="&#xe7;" k="71" />
<hkern u1="F" u2="&#xe6;" k="71" />
<hkern u1="F" u2="&#xe5;" k="71" />
<hkern u1="F" u2="&#xe4;" k="71" />
<hkern u1="F" u2="&#xe3;" k="71" />
<hkern u1="F" u2="&#xe2;" k="71" />
<hkern u1="F" u2="&#xe1;" k="71" />
<hkern u1="F" u2="&#xe0;" k="71" />
<hkern u1="F" u2="&#xc6;" k="135" />
<hkern u1="F" u2="&#xc5;" k="135" />
<hkern u1="F" u2="&#xc4;" k="135" />
<hkern u1="F" u2="&#xc3;" k="135" />
<hkern u1="F" u2="&#xc2;" k="135" />
<hkern u1="F" u2="&#xc1;" k="135" />
<hkern u1="F" u2="&#xc0;" k="135" />
<hkern u1="F" u2="&#xb5;" k="61" />
<hkern u1="F" u2="u" k="61" />
<hkern u1="F" u2="r" k="61" />
<hkern u1="F" u2="q" k="71" />
<hkern u1="F" u2="p" k="61" />
<hkern u1="F" u2="o" k="71" />
<hkern u1="F" u2="n" k="61" />
<hkern u1="F" u2="m" k="61" />
<hkern u1="F" u2="e" k="71" />
<hkern u1="F" u2="d" k="71" />
<hkern u1="F" u2="c" k="71" />
<hkern u1="F" u2="a" k="71" />
<hkern u1="F" u2="J" k="203" />
<hkern u1="F" u2="A" k="135" />
<hkern u1="F" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x3b;" k="61" />
<hkern u1="F" u2="&#x3a;" k="61" />
<hkern u1="F" u2="&#x2f;" k="135" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#x26;" k="135" />
<hkern u1="J" u2="&#x2206;" k="41" />
<hkern u1="J" u2="&#x104;" k="41" />
<hkern u1="J" u2="&#xc6;" k="41" />
<hkern u1="J" u2="&#xc5;" k="41" />
<hkern u1="J" u2="&#xc4;" k="41" />
<hkern u1="J" u2="&#xc3;" k="41" />
<hkern u1="J" u2="&#xc2;" k="41" />
<hkern u1="J" u2="&#xc1;" k="41" />
<hkern u1="J" u2="&#xc0;" k="41" />
<hkern u1="J" u2="A" k="41" />
<hkern u1="J" u2="&#x2f;" k="41" />
<hkern u1="J" u2="&#x26;" k="41" />
<hkern u1="K" u2="&#x203a;" k="72" />
<hkern u1="K" u2="&#x2039;" k="72" />
<hkern u1="K" u2="&#x2022;" k="72" />
<hkern u1="K" u2="&#x201d;" k="-32" />
<hkern u1="K" u2="&#x201c;" k="-32" />
<hkern u1="K" u2="&#x2019;" k="-32" />
<hkern u1="K" u2="&#x2018;" k="-32" />
<hkern u1="K" u2="&#x2014;" k="72" />
<hkern u1="K" u2="&#x2013;" k="72" />
<hkern u1="K" u2="&#x152;" k="104" />
<hkern u1="K" u2="&#x106;" k="104" />
<hkern u1="K" u2="&#xff;" k="63" />
<hkern u1="K" u2="&#xfd;" k="63" />
<hkern u1="K" u2="&#xd8;" k="104" />
<hkern u1="K" u2="&#xd6;" k="104" />
<hkern u1="K" u2="&#xd5;" k="104" />
<hkern u1="K" u2="&#xd4;" k="104" />
<hkern u1="K" u2="&#xd3;" k="104" />
<hkern u1="K" u2="&#xd2;" k="104" />
<hkern u1="K" u2="&#xc7;" k="104" />
<hkern u1="K" u2="&#xbb;" k="72" />
<hkern u1="K" u2="&#xba;" k="-32" />
<hkern u1="K" u2="&#xb7;" k="72" />
<hkern u1="K" u2="&#xb0;" k="-32" />
<hkern u1="K" u2="&#xae;" k="104" />
<hkern u1="K" u2="&#xab;" k="72" />
<hkern u1="K" u2="&#xaa;" k="-32" />
<hkern u1="K" u2="&#xa9;" k="104" />
<hkern u1="K" u2="y" k="63" />
<hkern u1="K" u2="w" k="46" />
<hkern u1="K" u2="v" k="63" />
<hkern u1="K" u2="t" k="115" />
<hkern u1="K" u2="f" k="52" />
<hkern u1="K" u2="Q" k="104" />
<hkern u1="K" u2="O" k="104" />
<hkern u1="K" u2="G" k="104" />
<hkern u1="K" u2="C" k="104" />
<hkern u1="K" u2="&#x40;" k="104" />
<hkern u1="K" u2="&#x2d;" k="72" />
<hkern u1="K" u2="&#x2a;" k="-32" />
<hkern u1="K" u2="&#x27;" k="-32" />
<hkern u1="K" u2="&#x22;" k="-32" />
<hkern u1="L" u2="&#x203a;" k="202" />
<hkern u1="L" u2="&#x2039;" k="202" />
<hkern u1="L" u2="&#x2022;" k="202" />
<hkern u1="L" u2="&#x201d;" k="263" />
<hkern u1="L" u2="&#x201c;" k="263" />
<hkern u1="L" u2="&#x2019;" k="263" />
<hkern u1="L" u2="&#x2018;" k="263" />
<hkern u1="L" u2="&#x2014;" k="202" />
<hkern u1="L" u2="&#x2013;" k="202" />
<hkern u1="L" u2="&#x178;" k="217" />
<hkern u1="L" u2="&#x152;" k="93" />
<hkern u1="L" u2="&#x106;" k="93" />
<hkern u1="L" u2="&#xff;" k="135" />
<hkern u1="L" u2="&#xfd;" k="135" />
<hkern u1="L" u2="&#xdd;" k="217" />
<hkern u1="L" u2="&#xd8;" k="93" />
<hkern u1="L" u2="&#xd6;" k="93" />
<hkern u1="L" u2="&#xd5;" k="93" />
<hkern u1="L" u2="&#xd4;" k="93" />
<hkern u1="L" u2="&#xd3;" k="93" />
<hkern u1="L" u2="&#xd2;" k="93" />
<hkern u1="L" u2="&#xc7;" k="93" />
<hkern u1="L" u2="&#xbb;" k="202" />
<hkern u1="L" u2="&#xba;" k="263" />
<hkern u1="L" u2="&#xb7;" k="202" />
<hkern u1="L" u2="&#xb0;" k="263" />
<hkern u1="L" u2="&#xae;" k="93" />
<hkern u1="L" u2="&#xab;" k="202" />
<hkern u1="L" u2="&#xaa;" k="263" />
<hkern u1="L" u2="&#xa9;" k="93" />
<hkern u1="L" u2="y" k="135" />
<hkern u1="L" u2="w" k="87" />
<hkern u1="L" u2="v" k="135" />
<hkern u1="L" u2="\" k="186" />
<hkern u1="L" u2="Y" k="217" />
<hkern u1="L" u2="W" k="156" />
<hkern u1="L" u2="V" k="186" />
<hkern u1="L" u2="T" k="205" />
<hkern u1="L" u2="Q" k="93" />
<hkern u1="L" u2="O" k="93" />
<hkern u1="L" u2="G" k="93" />
<hkern u1="L" u2="C" k="93" />
<hkern u1="L" u2="&#x40;" k="93" />
<hkern u1="L" u2="&#x2d;" k="202" />
<hkern u1="L" u2="&#x2a;" k="263" />
<hkern u1="L" u2="&#x27;" k="263" />
<hkern u1="L" u2="&#x22;" k="263" />
<hkern u1="O" u2="&#x2206;" k="37" />
<hkern u1="O" u2="&#x201d;" k="56" />
<hkern u1="O" u2="&#x201c;" k="56" />
<hkern u1="O" u2="&#x2019;" k="56" />
<hkern u1="O" u2="&#x2018;" k="56" />
<hkern u1="O" u2="&#x17d;" k="71" />
<hkern u1="O" u2="&#x17b;" k="71" />
<hkern u1="O" u2="&#x179;" k="71" />
<hkern u1="O" u2="&#x178;" k="82" />
<hkern u1="O" u2="&#x104;" k="37" />
<hkern u1="O" u2="&#xdd;" k="82" />
<hkern u1="O" u2="&#xc6;" k="37" />
<hkern u1="O" u2="&#xc5;" k="37" />
<hkern u1="O" u2="&#xc4;" k="37" />
<hkern u1="O" u2="&#xc3;" k="37" />
<hkern u1="O" u2="&#xc2;" k="37" />
<hkern u1="O" u2="&#xc1;" k="37" />
<hkern u1="O" u2="&#xc0;" k="37" />
<hkern u1="O" u2="&#xba;" k="56" />
<hkern u1="O" u2="&#xb0;" k="56" />
<hkern u1="O" u2="&#xaa;" k="56" />
<hkern u1="O" u2="&#x7d;" k="41" />
<hkern u1="O" u2="]" k="41" />
<hkern u1="O" u2="\" k="52" />
<hkern u1="O" u2="Z" k="71" />
<hkern u1="O" u2="Y" k="82" />
<hkern u1="O" u2="X" k="70" />
<hkern u1="O" u2="V" k="52" />
<hkern u1="O" u2="T" k="99" />
<hkern u1="O" u2="A" k="37" />
<hkern u1="O" u2="&#x2f;" k="37" />
<hkern u1="O" u2="&#x2a;" k="56" />
<hkern u1="O" u2="&#x29;" k="41" />
<hkern u1="O" u2="&#x27;" k="56" />
<hkern u1="O" u2="&#x26;" k="37" />
<hkern u1="O" u2="&#x22;" k="56" />
<hkern u1="P" u2="&#x2206;" k="141" />
<hkern u1="P" u2="&#x2026;" k="194" />
<hkern u1="P" u2="&#x201e;" k="194" />
<hkern u1="P" u2="&#x201a;" k="194" />
<hkern u1="P" u2="&#x153;" k="31" />
<hkern u1="P" u2="&#x119;" k="31" />
<hkern u1="P" u2="&#x107;" k="31" />
<hkern u1="P" u2="&#x105;" k="31" />
<hkern u1="P" u2="&#x104;" k="141" />
<hkern u1="P" u2="&#xf8;" k="31" />
<hkern u1="P" u2="&#xf6;" k="31" />
<hkern u1="P" u2="&#xf5;" k="31" />
<hkern u1="P" u2="&#xf4;" k="31" />
<hkern u1="P" u2="&#xf3;" k="31" />
<hkern u1="P" u2="&#xf2;" k="31" />
<hkern u1="P" u2="&#xf0;" k="31" />
<hkern u1="P" u2="&#xeb;" k="31" />
<hkern u1="P" u2="&#xea;" k="31" />
<hkern u1="P" u2="&#xe9;" k="31" />
<hkern u1="P" u2="&#xe8;" k="31" />
<hkern u1="P" u2="&#xe7;" k="31" />
<hkern u1="P" u2="&#xe6;" k="31" />
<hkern u1="P" u2="&#xe5;" k="31" />
<hkern u1="P" u2="&#xe4;" k="31" />
<hkern u1="P" u2="&#xe3;" k="31" />
<hkern u1="P" u2="&#xe2;" k="31" />
<hkern u1="P" u2="&#xe1;" k="31" />
<hkern u1="P" u2="&#xe0;" k="31" />
<hkern u1="P" u2="&#xc6;" k="141" />
<hkern u1="P" u2="&#xc5;" k="141" />
<hkern u1="P" u2="&#xc4;" k="141" />
<hkern u1="P" u2="&#xc3;" k="141" />
<hkern u1="P" u2="&#xc2;" k="141" />
<hkern u1="P" u2="&#xc1;" k="141" />
<hkern u1="P" u2="&#xc0;" k="141" />
<hkern u1="P" u2="q" k="31" />
<hkern u1="P" u2="o" k="31" />
<hkern u1="P" u2="e" k="31" />
<hkern u1="P" u2="d" k="31" />
<hkern u1="P" u2="c" k="31" />
<hkern u1="P" u2="a" k="31" />
<hkern u1="P" u2="J" k="186" />
<hkern u1="P" u2="A" k="141" />
<hkern u1="P" u2="&#x2f;" k="141" />
<hkern u1="P" u2="&#x2e;" k="194" />
<hkern u1="P" u2="&#x2c;" k="194" />
<hkern u1="P" u2="&#x26;" k="141" />
<hkern u1="Q" u2="&#x2206;" k="37" />
<hkern u1="Q" u2="&#x201d;" k="56" />
<hkern u1="Q" u2="&#x201c;" k="56" />
<hkern u1="Q" u2="&#x2019;" k="56" />
<hkern u1="Q" u2="&#x2018;" k="56" />
<hkern u1="Q" u2="&#x17d;" k="71" />
<hkern u1="Q" u2="&#x17b;" k="71" />
<hkern u1="Q" u2="&#x179;" k="71" />
<hkern u1="Q" u2="&#x178;" k="82" />
<hkern u1="Q" u2="&#x104;" k="37" />
<hkern u1="Q" u2="&#xdd;" k="82" />
<hkern u1="Q" u2="&#xc6;" k="37" />
<hkern u1="Q" u2="&#xc5;" k="37" />
<hkern u1="Q" u2="&#xc4;" k="37" />
<hkern u1="Q" u2="&#xc3;" k="37" />
<hkern u1="Q" u2="&#xc2;" k="37" />
<hkern u1="Q" u2="&#xc1;" k="37" />
<hkern u1="Q" u2="&#xc0;" k="37" />
<hkern u1="Q" u2="&#xba;" k="56" />
<hkern u1="Q" u2="&#xb0;" k="56" />
<hkern u1="Q" u2="&#xaa;" k="56" />
<hkern u1="Q" u2="&#x7d;" k="41" />
<hkern u1="Q" u2="]" k="41" />
<hkern u1="Q" u2="\" k="52" />
<hkern u1="Q" u2="Z" k="71" />
<hkern u1="Q" u2="Y" k="82" />
<hkern u1="Q" u2="X" k="70" />
<hkern u1="Q" u2="V" k="52" />
<hkern u1="Q" u2="T" k="99" />
<hkern u1="Q" u2="A" k="37" />
<hkern u1="Q" u2="&#x2f;" k="37" />
<hkern u1="Q" u2="&#x2a;" k="56" />
<hkern u1="Q" u2="&#x29;" k="41" />
<hkern u1="Q" u2="&#x27;" k="56" />
<hkern u1="Q" u2="&#x26;" k="37" />
<hkern u1="Q" u2="&#x22;" k="56" />
<hkern u1="R" u2="&#x152;" k="41" />
<hkern u1="R" u2="&#x106;" k="41" />
<hkern u1="R" u2="&#xdc;" k="43" />
<hkern u1="R" u2="&#xdb;" k="43" />
<hkern u1="R" u2="&#xda;" k="43" />
<hkern u1="R" u2="&#xd9;" k="43" />
<hkern u1="R" u2="&#xd8;" k="41" />
<hkern u1="R" u2="&#xd6;" k="41" />
<hkern u1="R" u2="&#xd5;" k="41" />
<hkern u1="R" u2="&#xd4;" k="41" />
<hkern u1="R" u2="&#xd3;" k="41" />
<hkern u1="R" u2="&#xd2;" k="41" />
<hkern u1="R" u2="&#xc7;" k="41" />
<hkern u1="R" u2="&#xae;" k="41" />
<hkern u1="R" u2="&#xa9;" k="41" />
<hkern u1="R" u2="U" k="43" />
<hkern u1="R" u2="T" k="52" />
<hkern u1="R" u2="Q" k="41" />
<hkern u1="R" u2="O" k="41" />
<hkern u1="R" u2="G" k="41" />
<hkern u1="R" u2="C" k="41" />
<hkern u1="R" u2="&#x40;" k="41" />
<hkern u1="T" u2="&#x2206;" k="135" />
<hkern u1="T" u2="&#x203a;" k="184" />
<hkern u1="T" u2="&#x2039;" k="184" />
<hkern u1="T" u2="&#x2026;" k="184" />
<hkern u1="T" u2="&#x2022;" k="184" />
<hkern u1="T" u2="&#x201e;" k="184" />
<hkern u1="T" u2="&#x201a;" k="184" />
<hkern u1="T" u2="&#x2014;" k="184" />
<hkern u1="T" u2="&#x2013;" k="184" />
<hkern u1="T" u2="&#x17e;" k="165" />
<hkern u1="T" u2="&#x17c;" k="165" />
<hkern u1="T" u2="&#x17a;" k="165" />
<hkern u1="T" u2="&#x161;" k="186" />
<hkern u1="T" u2="&#x15b;" k="186" />
<hkern u1="T" u2="&#x153;" k="214" />
<hkern u1="T" u2="&#x152;" k="99" />
<hkern u1="T" u2="&#x144;" k="158" />
<hkern u1="T" u2="&#x119;" k="214" />
<hkern u1="T" u2="&#x107;" k="214" />
<hkern u1="T" u2="&#x106;" k="99" />
<hkern u1="T" u2="&#x105;" k="214" />
<hkern u1="T" u2="&#x104;" k="135" />
<hkern u1="T" u2="&#xff;" k="173" />
<hkern u1="T" u2="&#xfd;" k="173" />
<hkern u1="T" u2="&#xfc;" k="158" />
<hkern u1="T" u2="&#xfb;" k="158" />
<hkern u1="T" u2="&#xfa;" k="158" />
<hkern u1="T" u2="&#xf9;" k="158" />
<hkern u1="T" u2="&#xf8;" k="214" />
<hkern u1="T" u2="&#xf6;" k="214" />
<hkern u1="T" u2="&#xf5;" k="214" />
<hkern u1="T" u2="&#xf4;" k="214" />
<hkern u1="T" u2="&#xf3;" k="214" />
<hkern u1="T" u2="&#xf2;" k="214" />
<hkern u1="T" u2="&#xf1;" k="158" />
<hkern u1="T" u2="&#xf0;" k="214" />
<hkern u1="T" u2="&#xeb;" k="214" />
<hkern u1="T" u2="&#xea;" k="214" />
<hkern u1="T" u2="&#xe9;" k="214" />
<hkern u1="T" u2="&#xe8;" k="214" />
<hkern u1="T" u2="&#xe7;" k="214" />
<hkern u1="T" u2="&#xe6;" k="214" />
<hkern u1="T" u2="&#xe5;" k="214" />
<hkern u1="T" u2="&#xe4;" k="214" />
<hkern u1="T" u2="&#xe3;" k="214" />
<hkern u1="T" u2="&#xe2;" k="214" />
<hkern u1="T" u2="&#xe1;" k="214" />
<hkern u1="T" u2="&#xe0;" k="214" />
<hkern u1="T" u2="&#xd8;" k="99" />
<hkern u1="T" u2="&#xd6;" k="99" />
<hkern u1="T" u2="&#xd5;" k="99" />
<hkern u1="T" u2="&#xd4;" k="99" />
<hkern u1="T" u2="&#xd3;" k="99" />
<hkern u1="T" u2="&#xd2;" k="99" />
<hkern u1="T" u2="&#xc7;" k="99" />
<hkern u1="T" u2="&#xc6;" k="135" />
<hkern u1="T" u2="&#xc5;" k="135" />
<hkern u1="T" u2="&#xc4;" k="135" />
<hkern u1="T" u2="&#xc3;" k="135" />
<hkern u1="T" u2="&#xc2;" k="135" />
<hkern u1="T" u2="&#xc1;" k="135" />
<hkern u1="T" u2="&#xc0;" k="135" />
<hkern u1="T" u2="&#xbb;" k="184" />
<hkern u1="T" u2="&#xb7;" k="184" />
<hkern u1="T" u2="&#xb5;" k="158" />
<hkern u1="T" u2="&#xae;" k="99" />
<hkern u1="T" u2="&#xab;" k="184" />
<hkern u1="T" u2="&#xa9;" k="99" />
<hkern u1="T" u2="z" k="165" />
<hkern u1="T" u2="y" k="184" />
<hkern u1="T" u2="x" k="170" />
<hkern u1="T" u2="w" k="132" />
<hkern u1="T" u2="v" k="173" />
<hkern u1="T" u2="u" k="158" />
<hkern u1="T" u2="s" k="186" />
<hkern u1="T" u2="r" k="158" />
<hkern u1="T" u2="q" k="214" />
<hkern u1="T" u2="p" k="158" />
<hkern u1="T" u2="o" k="214" />
<hkern u1="T" u2="n" k="158" />
<hkern u1="T" u2="m" k="158" />
<hkern u1="T" u2="g" k="191" />
<hkern u1="T" u2="e" k="214" />
<hkern u1="T" u2="d" k="214" />
<hkern u1="T" u2="c" k="214" />
<hkern u1="T" u2="a" k="214" />
<hkern u1="T" u2="Q" k="99" />
<hkern u1="T" u2="O" k="99" />
<hkern u1="T" u2="J" k="205" />
<hkern u1="T" u2="G" k="99" />
<hkern u1="T" u2="C" k="99" />
<hkern u1="T" u2="A" k="135" />
<hkern u1="T" u2="&#x40;" k="99" />
<hkern u1="T" u2="&#x3f;" k="-40" />
<hkern u1="T" u2="&#x3b;" k="158" />
<hkern u1="T" u2="&#x3a;" k="158" />
<hkern u1="T" u2="&#x2f;" k="135" />
<hkern u1="T" u2="&#x2e;" k="184" />
<hkern u1="T" u2="&#x2d;" k="184" />
<hkern u1="T" u2="&#x2c;" k="184" />
<hkern u1="T" u2="&#x26;" k="135" />
<hkern u1="U" u2="&#x2206;" k="41" />
<hkern u1="U" u2="&#x104;" k="41" />
<hkern u1="U" u2="&#xc6;" k="41" />
<hkern u1="U" u2="&#xc5;" k="41" />
<hkern u1="U" u2="&#xc4;" k="41" />
<hkern u1="U" u2="&#xc3;" k="41" />
<hkern u1="U" u2="&#xc2;" k="41" />
<hkern u1="U" u2="&#xc1;" k="41" />
<hkern u1="U" u2="&#xc0;" k="41" />
<hkern u1="U" u2="A" k="41" />
<hkern u1="U" u2="&#x2f;" k="41" />
<hkern u1="U" u2="&#x26;" k="41" />
<hkern u1="V" u2="&#x2206;" k="117" />
<hkern u1="V" u2="&#x203a;" k="125" />
<hkern u1="V" u2="&#x2039;" k="125" />
<hkern u1="V" u2="&#x2026;" k="196" />
<hkern u1="V" u2="&#x2022;" k="125" />
<hkern u1="V" u2="&#x201e;" k="196" />
<hkern u1="V" u2="&#x201d;" k="-55" />
<hkern u1="V" u2="&#x201c;" k="-55" />
<hkern u1="V" u2="&#x201a;" k="196" />
<hkern u1="V" u2="&#x2019;" k="-55" />
<hkern u1="V" u2="&#x2018;" k="-55" />
<hkern u1="V" u2="&#x2014;" k="125" />
<hkern u1="V" u2="&#x2013;" k="125" />
<hkern u1="V" u2="&#x17e;" k="74" />
<hkern u1="V" u2="&#x17c;" k="74" />
<hkern u1="V" u2="&#x17a;" k="74" />
<hkern u1="V" u2="&#x161;" k="115" />
<hkern u1="V" u2="&#x15b;" k="115" />
<hkern u1="V" u2="&#x153;" k="120" />
<hkern u1="V" u2="&#x152;" k="41" />
<hkern u1="V" u2="&#x144;" k="87" />
<hkern u1="V" u2="&#x119;" k="120" />
<hkern u1="V" u2="&#x107;" k="120" />
<hkern u1="V" u2="&#x106;" k="41" />
<hkern u1="V" u2="&#x105;" k="120" />
<hkern u1="V" u2="&#x104;" k="117" />
<hkern u1="V" u2="&#xff;" k="37" />
<hkern u1="V" u2="&#xfd;" k="37" />
<hkern u1="V" u2="&#xfc;" k="87" />
<hkern u1="V" u2="&#xfb;" k="87" />
<hkern u1="V" u2="&#xfa;" k="87" />
<hkern u1="V" u2="&#xf9;" k="87" />
<hkern u1="V" u2="&#xf8;" k="120" />
<hkern u1="V" u2="&#xf6;" k="120" />
<hkern u1="V" u2="&#xf5;" k="120" />
<hkern u1="V" u2="&#xf4;" k="120" />
<hkern u1="V" u2="&#xf3;" k="120" />
<hkern u1="V" u2="&#xf2;" k="120" />
<hkern u1="V" u2="&#xf1;" k="87" />
<hkern u1="V" u2="&#xf0;" k="120" />
<hkern u1="V" u2="&#xeb;" k="120" />
<hkern u1="V" u2="&#xea;" k="120" />
<hkern u1="V" u2="&#xe9;" k="120" />
<hkern u1="V" u2="&#xe8;" k="120" />
<hkern u1="V" u2="&#xe7;" k="120" />
<hkern u1="V" u2="&#xe6;" k="120" />
<hkern u1="V" u2="&#xe5;" k="120" />
<hkern u1="V" u2="&#xe4;" k="120" />
<hkern u1="V" u2="&#xe3;" k="120" />
<hkern u1="V" u2="&#xe2;" k="120" />
<hkern u1="V" u2="&#xe1;" k="120" />
<hkern u1="V" u2="&#xe0;" k="120" />
<hkern u1="V" u2="&#xd8;" k="41" />
<hkern u1="V" u2="&#xd6;" k="41" />
<hkern u1="V" u2="&#xd5;" k="41" />
<hkern u1="V" u2="&#xd4;" k="41" />
<hkern u1="V" u2="&#xd3;" k="41" />
<hkern u1="V" u2="&#xd2;" k="41" />
<hkern u1="V" u2="&#xc7;" k="41" />
<hkern u1="V" u2="&#xc6;" k="117" />
<hkern u1="V" u2="&#xc5;" k="117" />
<hkern u1="V" u2="&#xc4;" k="117" />
<hkern u1="V" u2="&#xc3;" k="117" />
<hkern u1="V" u2="&#xc2;" k="117" />
<hkern u1="V" u2="&#xc1;" k="117" />
<hkern u1="V" u2="&#xc0;" k="117" />
<hkern u1="V" u2="&#xbb;" k="125" />
<hkern u1="V" u2="&#xba;" k="-55" />
<hkern u1="V" u2="&#xb7;" k="125" />
<hkern u1="V" u2="&#xb5;" k="87" />
<hkern u1="V" u2="&#xb0;" k="-55" />
<hkern u1="V" u2="&#xae;" k="41" />
<hkern u1="V" u2="&#xab;" k="125" />
<hkern u1="V" u2="&#xaa;" k="-55" />
<hkern u1="V" u2="&#xa9;" k="41" />
<hkern u1="V" u2="z" k="74" />
<hkern u1="V" u2="y" k="37" />
<hkern u1="V" u2="w" k="37" />
<hkern u1="V" u2="v" k="37" />
<hkern u1="V" u2="u" k="87" />
<hkern u1="V" u2="s" k="115" />
<hkern u1="V" u2="r" k="87" />
<hkern u1="V" u2="q" k="120" />
<hkern u1="V" u2="p" k="87" />
<hkern u1="V" u2="o" k="120" />
<hkern u1="V" u2="n" k="87" />
<hkern u1="V" u2="m" k="87" />
<hkern u1="V" u2="e" k="120" />
<hkern u1="V" u2="d" k="120" />
<hkern u1="V" u2="c" k="120" />
<hkern u1="V" u2="a" k="120" />
<hkern u1="V" u2="Q" k="41" />
<hkern u1="V" u2="O" k="41" />
<hkern u1="V" u2="J" k="155" />
<hkern u1="V" u2="G" k="41" />
<hkern u1="V" u2="C" k="41" />
<hkern u1="V" u2="A" k="117" />
<hkern u1="V" u2="&#x40;" k="41" />
<hkern u1="V" u2="&#x3f;" k="-59" />
<hkern u1="V" u2="&#x3b;" k="87" />
<hkern u1="V" u2="&#x3a;" k="87" />
<hkern u1="V" u2="&#x2f;" k="117" />
<hkern u1="V" u2="&#x2e;" k="196" />
<hkern u1="V" u2="&#x2d;" k="125" />
<hkern u1="V" u2="&#x2c;" k="196" />
<hkern u1="V" u2="&#x2a;" k="-55" />
<hkern u1="V" u2="&#x27;" k="-55" />
<hkern u1="V" u2="&#x26;" k="117" />
<hkern u1="V" u2="&#x22;" k="-55" />
<hkern u1="W" u2="&#x2206;" k="74" />
<hkern u1="W" u2="&#x2026;" k="102" />
<hkern u1="W" u2="&#x201e;" k="102" />
<hkern u1="W" u2="&#x201d;" k="-59" />
<hkern u1="W" u2="&#x201c;" k="-59" />
<hkern u1="W" u2="&#x201a;" k="102" />
<hkern u1="W" u2="&#x2019;" k="-59" />
<hkern u1="W" u2="&#x2018;" k="-59" />
<hkern u1="W" u2="&#x161;" k="88" />
<hkern u1="W" u2="&#x15b;" k="88" />
<hkern u1="W" u2="&#x153;" k="108" />
<hkern u1="W" u2="&#x144;" k="68" />
<hkern u1="W" u2="&#x119;" k="108" />
<hkern u1="W" u2="&#x107;" k="108" />
<hkern u1="W" u2="&#x105;" k="108" />
<hkern u1="W" u2="&#x104;" k="74" />
<hkern u1="W" u2="&#xfc;" k="68" />
<hkern u1="W" u2="&#xfb;" k="68" />
<hkern u1="W" u2="&#xfa;" k="68" />
<hkern u1="W" u2="&#xf9;" k="68" />
<hkern u1="W" u2="&#xf8;" k="108" />
<hkern u1="W" u2="&#xf6;" k="108" />
<hkern u1="W" u2="&#xf5;" k="108" />
<hkern u1="W" u2="&#xf4;" k="108" />
<hkern u1="W" u2="&#xf3;" k="108" />
<hkern u1="W" u2="&#xf2;" k="108" />
<hkern u1="W" u2="&#xf1;" k="68" />
<hkern u1="W" u2="&#xf0;" k="108" />
<hkern u1="W" u2="&#xeb;" k="108" />
<hkern u1="W" u2="&#xea;" k="108" />
<hkern u1="W" u2="&#xe9;" k="108" />
<hkern u1="W" u2="&#xe8;" k="108" />
<hkern u1="W" u2="&#xe7;" k="108" />
<hkern u1="W" u2="&#xe6;" k="108" />
<hkern u1="W" u2="&#xe5;" k="108" />
<hkern u1="W" u2="&#xe4;" k="108" />
<hkern u1="W" u2="&#xe3;" k="108" />
<hkern u1="W" u2="&#xe2;" k="108" />
<hkern u1="W" u2="&#xe1;" k="108" />
<hkern u1="W" u2="&#xe0;" k="108" />
<hkern u1="W" u2="&#xc6;" k="74" />
<hkern u1="W" u2="&#xc5;" k="74" />
<hkern u1="W" u2="&#xc4;" k="74" />
<hkern u1="W" u2="&#xc3;" k="74" />
<hkern u1="W" u2="&#xc2;" k="74" />
<hkern u1="W" u2="&#xc1;" k="74" />
<hkern u1="W" u2="&#xc0;" k="74" />
<hkern u1="W" u2="&#xba;" k="-59" />
<hkern u1="W" u2="&#xb5;" k="68" />
<hkern u1="W" u2="&#xb0;" k="-59" />
<hkern u1="W" u2="&#xaa;" k="-59" />
<hkern u1="W" u2="u" k="68" />
<hkern u1="W" u2="s" k="88" />
<hkern u1="W" u2="r" k="68" />
<hkern u1="W" u2="q" k="108" />
<hkern u1="W" u2="p" k="68" />
<hkern u1="W" u2="o" k="108" />
<hkern u1="W" u2="n" k="68" />
<hkern u1="W" u2="m" k="68" />
<hkern u1="W" u2="g" k="86" />
<hkern u1="W" u2="e" k="108" />
<hkern u1="W" u2="d" k="108" />
<hkern u1="W" u2="c" k="108" />
<hkern u1="W" u2="a" k="108" />
<hkern u1="W" u2="J" k="104" />
<hkern u1="W" u2="A" k="74" />
<hkern u1="W" u2="&#x3b;" k="68" />
<hkern u1="W" u2="&#x3a;" k="68" />
<hkern u1="W" u2="&#x2f;" k="74" />
<hkern u1="W" u2="&#x2e;" k="102" />
<hkern u1="W" u2="&#x2c;" k="102" />
<hkern u1="W" u2="&#x2a;" k="-59" />
<hkern u1="W" u2="&#x27;" k="-59" />
<hkern u1="W" u2="&#x26;" k="74" />
<hkern u1="W" u2="&#x22;" k="-59" />
<hkern u1="X" u2="&#x203a;" k="72" />
<hkern u1="X" u2="&#x2039;" k="72" />
<hkern u1="X" u2="&#x2022;" k="72" />
<hkern u1="X" u2="&#x201d;" k="-32" />
<hkern u1="X" u2="&#x201c;" k="-32" />
<hkern u1="X" u2="&#x2019;" k="-32" />
<hkern u1="X" u2="&#x2018;" k="-32" />
<hkern u1="X" u2="&#x2014;" k="72" />
<hkern u1="X" u2="&#x2013;" k="72" />
<hkern u1="X" u2="&#x152;" k="104" />
<hkern u1="X" u2="&#x106;" k="104" />
<hkern u1="X" u2="&#xff;" k="63" />
<hkern u1="X" u2="&#xfd;" k="63" />
<hkern u1="X" u2="&#xd8;" k="104" />
<hkern u1="X" u2="&#xd6;" k="104" />
<hkern u1="X" u2="&#xd5;" k="104" />
<hkern u1="X" u2="&#xd4;" k="104" />
<hkern u1="X" u2="&#xd3;" k="104" />
<hkern u1="X" u2="&#xd2;" k="104" />
<hkern u1="X" u2="&#xc7;" k="104" />
<hkern u1="X" u2="&#xbb;" k="72" />
<hkern u1="X" u2="&#xba;" k="-32" />
<hkern u1="X" u2="&#xb7;" k="72" />
<hkern u1="X" u2="&#xb0;" k="-32" />
<hkern u1="X" u2="&#xae;" k="104" />
<hkern u1="X" u2="&#xab;" k="72" />
<hkern u1="X" u2="&#xaa;" k="-32" />
<hkern u1="X" u2="&#xa9;" k="104" />
<hkern u1="X" u2="y" k="63" />
<hkern u1="X" u2="w" k="46" />
<hkern u1="X" u2="v" k="63" />
<hkern u1="X" u2="t" k="115" />
<hkern u1="X" u2="f" k="52" />
<hkern u1="X" u2="Q" k="104" />
<hkern u1="X" u2="O" k="104" />
<hkern u1="X" u2="G" k="104" />
<hkern u1="X" u2="C" k="104" />
<hkern u1="X" u2="&#x40;" k="104" />
<hkern u1="X" u2="&#x2d;" k="72" />
<hkern u1="X" u2="&#x2a;" k="-32" />
<hkern u1="X" u2="&#x27;" k="-32" />
<hkern u1="X" u2="&#x22;" k="-32" />
<hkern u1="Y" u2="&#x2206;" k="145" />
<hkern u1="Y" u2="&#x203a;" k="186" />
<hkern u1="Y" u2="&#x2039;" k="186" />
<hkern u1="Y" u2="&#x2026;" k="222" />
<hkern u1="Y" u2="&#x2022;" k="186" />
<hkern u1="Y" u2="&#x201e;" k="222" />
<hkern u1="Y" u2="&#x201d;" k="-56" />
<hkern u1="Y" u2="&#x201c;" k="-56" />
<hkern u1="Y" u2="&#x201a;" k="222" />
<hkern u1="Y" u2="&#x2019;" k="-56" />
<hkern u1="Y" u2="&#x2018;" k="-56" />
<hkern u1="Y" u2="&#x2014;" k="186" />
<hkern u1="Y" u2="&#x2013;" k="186" />
<hkern u1="Y" u2="&#x17e;" k="102" />
<hkern u1="Y" u2="&#x17c;" k="102" />
<hkern u1="Y" u2="&#x17a;" k="102" />
<hkern u1="Y" u2="&#x161;" k="196" />
<hkern u1="Y" u2="&#x15b;" k="196" />
<hkern u1="Y" u2="&#x153;" k="196" />
<hkern u1="Y" u2="&#x152;" k="71" />
<hkern u1="Y" u2="&#x144;" k="118" />
<hkern u1="Y" u2="&#x119;" k="196" />
<hkern u1="Y" u2="&#x107;" k="196" />
<hkern u1="Y" u2="&#x106;" k="71" />
<hkern u1="Y" u2="&#x105;" k="196" />
<hkern u1="Y" u2="&#x104;" k="145" />
<hkern u1="Y" u2="&#xfc;" k="118" />
<hkern u1="Y" u2="&#xfb;" k="118" />
<hkern u1="Y" u2="&#xfa;" k="118" />
<hkern u1="Y" u2="&#xf9;" k="118" />
<hkern u1="Y" u2="&#xf8;" k="196" />
<hkern u1="Y" u2="&#xf6;" k="196" />
<hkern u1="Y" u2="&#xf5;" k="196" />
<hkern u1="Y" u2="&#xf4;" k="196" />
<hkern u1="Y" u2="&#xf3;" k="196" />
<hkern u1="Y" u2="&#xf2;" k="196" />
<hkern u1="Y" u2="&#xf1;" k="118" />
<hkern u1="Y" u2="&#xf0;" k="196" />
<hkern u1="Y" u2="&#xeb;" k="196" />
<hkern u1="Y" u2="&#xea;" k="196" />
<hkern u1="Y" u2="&#xe9;" k="196" />
<hkern u1="Y" u2="&#xe8;" k="196" />
<hkern u1="Y" u2="&#xe7;" k="196" />
<hkern u1="Y" u2="&#xe6;" k="196" />
<hkern u1="Y" u2="&#xe5;" k="196" />
<hkern u1="Y" u2="&#xe4;" k="196" />
<hkern u1="Y" u2="&#xe3;" k="196" />
<hkern u1="Y" u2="&#xe2;" k="196" />
<hkern u1="Y" u2="&#xe1;" k="196" />
<hkern u1="Y" u2="&#xe0;" k="196" />
<hkern u1="Y" u2="&#xd8;" k="71" />
<hkern u1="Y" u2="&#xd6;" k="71" />
<hkern u1="Y" u2="&#xd5;" k="71" />
<hkern u1="Y" u2="&#xd4;" k="71" />
<hkern u1="Y" u2="&#xd3;" k="71" />
<hkern u1="Y" u2="&#xd2;" k="71" />
<hkern u1="Y" u2="&#xc7;" k="71" />
<hkern u1="Y" u2="&#xc6;" k="145" />
<hkern u1="Y" u2="&#xc5;" k="145" />
<hkern u1="Y" u2="&#xc4;" k="145" />
<hkern u1="Y" u2="&#xc3;" k="145" />
<hkern u1="Y" u2="&#xc2;" k="145" />
<hkern u1="Y" u2="&#xc1;" k="145" />
<hkern u1="Y" u2="&#xc0;" k="145" />
<hkern u1="Y" u2="&#xbb;" k="186" />
<hkern u1="Y" u2="&#xba;" k="-56" />
<hkern u1="Y" u2="&#xb7;" k="186" />
<hkern u1="Y" u2="&#xb5;" k="118" />
<hkern u1="Y" u2="&#xb0;" k="-56" />
<hkern u1="Y" u2="&#xae;" k="71" />
<hkern u1="Y" u2="&#xab;" k="186" />
<hkern u1="Y" u2="&#xaa;" k="-56" />
<hkern u1="Y" u2="&#xa9;" k="71" />
<hkern u1="Y" u2="z" k="102" />
<hkern u1="Y" u2="u" k="118" />
<hkern u1="Y" u2="s" k="196" />
<hkern u1="Y" u2="r" k="118" />
<hkern u1="Y" u2="q" k="196" />
<hkern u1="Y" u2="p" k="118" />
<hkern u1="Y" u2="o" k="196" />
<hkern u1="Y" u2="n" k="118" />
<hkern u1="Y" u2="m" k="118" />
<hkern u1="Y" u2="g" k="173" />
<hkern u1="Y" u2="e" k="196" />
<hkern u1="Y" u2="d" k="196" />
<hkern u1="Y" u2="c" k="196" />
<hkern u1="Y" u2="a" k="196" />
<hkern u1="Y" u2="Q" k="71" />
<hkern u1="Y" u2="O" k="71" />
<hkern u1="Y" u2="J" k="205" />
<hkern u1="Y" u2="G" k="71" />
<hkern u1="Y" u2="C" k="71" />
<hkern u1="Y" u2="A" k="145" />
<hkern u1="Y" u2="&#x40;" k="71" />
<hkern u1="Y" u2="&#x3f;" k="-51" />
<hkern u1="Y" u2="&#x3b;" k="118" />
<hkern u1="Y" u2="&#x3a;" k="118" />
<hkern u1="Y" u2="&#x2f;" k="145" />
<hkern u1="Y" u2="&#x2e;" k="222" />
<hkern u1="Y" u2="&#x2d;" k="186" />
<hkern u1="Y" u2="&#x2c;" k="222" />
<hkern u1="Y" u2="&#x2a;" k="-56" />
<hkern u1="Y" u2="&#x27;" k="-56" />
<hkern u1="Y" u2="&#x26;" k="145" />
<hkern u1="Y" u2="&#x22;" k="-56" />
<hkern u1="Z" u2="&#x203a;" k="87" />
<hkern u1="Z" u2="&#x2039;" k="87" />
<hkern u1="Z" u2="&#x2022;" k="87" />
<hkern u1="Z" u2="&#x2014;" k="87" />
<hkern u1="Z" u2="&#x2013;" k="87" />
<hkern u1="Z" u2="&#x152;" k="59" />
<hkern u1="Z" u2="&#x106;" k="59" />
<hkern u1="Z" u2="&#xd8;" k="59" />
<hkern u1="Z" u2="&#xd6;" k="59" />
<hkern u1="Z" u2="&#xd5;" k="59" />
<hkern u1="Z" u2="&#xd4;" k="59" />
<hkern u1="Z" u2="&#xd3;" k="59" />
<hkern u1="Z" u2="&#xd2;" k="59" />
<hkern u1="Z" u2="&#xc7;" k="59" />
<hkern u1="Z" u2="&#xbb;" k="87" />
<hkern u1="Z" u2="&#xb7;" k="87" />
<hkern u1="Z" u2="&#xae;" k="59" />
<hkern u1="Z" u2="&#xab;" k="87" />
<hkern u1="Z" u2="&#xa9;" k="59" />
<hkern u1="Z" u2="Q" k="59" />
<hkern u1="Z" u2="O" k="59" />
<hkern u1="Z" u2="G" k="59" />
<hkern u1="Z" u2="C" k="59" />
<hkern u1="Z" u2="&#x40;" k="59" />
<hkern u1="Z" u2="&#x3f;" k="-36" />
<hkern u1="Z" u2="&#x2d;" k="87" />
<hkern u1="[" u2="&#x153;" k="32" />
<hkern u1="[" u2="&#x152;" k="41" />
<hkern u1="[" u2="&#x119;" k="32" />
<hkern u1="[" u2="&#x107;" k="32" />
<hkern u1="[" u2="&#x106;" k="41" />
<hkern u1="[" u2="&#x105;" k="32" />
<hkern u1="[" u2="&#xf8;" k="32" />
<hkern u1="[" u2="&#xf6;" k="32" />
<hkern u1="[" u2="&#xf5;" k="32" />
<hkern u1="[" u2="&#xf4;" k="32" />
<hkern u1="[" u2="&#xf3;" k="32" />
<hkern u1="[" u2="&#xf2;" k="32" />
<hkern u1="[" u2="&#xf0;" k="32" />
<hkern u1="[" u2="&#xeb;" k="32" />
<hkern u1="[" u2="&#xea;" k="32" />
<hkern u1="[" u2="&#xe9;" k="32" />
<hkern u1="[" u2="&#xe8;" k="32" />
<hkern u1="[" u2="&#xe7;" k="32" />
<hkern u1="[" u2="&#xe6;" k="32" />
<hkern u1="[" u2="&#xe5;" k="32" />
<hkern u1="[" u2="&#xe4;" k="32" />
<hkern u1="[" u2="&#xe3;" k="32" />
<hkern u1="[" u2="&#xe2;" k="32" />
<hkern u1="[" u2="&#xe1;" k="32" />
<hkern u1="[" u2="&#xe0;" k="32" />
<hkern u1="[" u2="&#xd8;" k="41" />
<hkern u1="[" u2="&#xd6;" k="41" />
<hkern u1="[" u2="&#xd5;" k="41" />
<hkern u1="[" u2="&#xd4;" k="41" />
<hkern u1="[" u2="&#xd3;" k="41" />
<hkern u1="[" u2="&#xd2;" k="41" />
<hkern u1="[" u2="&#xc7;" k="41" />
<hkern u1="[" u2="&#xae;" k="41" />
<hkern u1="[" u2="&#xa9;" k="41" />
<hkern u1="[" u2="q" k="32" />
<hkern u1="[" u2="o" k="32" />
<hkern u1="[" u2="e" k="32" />
<hkern u1="[" u2="d" k="32" />
<hkern u1="[" u2="c" k="32" />
<hkern u1="[" u2="a" k="32" />
<hkern u1="[" u2="Q" k="41" />
<hkern u1="[" u2="O" k="41" />
<hkern u1="[" u2="G" k="41" />
<hkern u1="[" u2="C" k="41" />
<hkern u1="[" u2="&#x40;" k="41" />
<hkern u1="\" u2="&#x203a;" k="53" />
<hkern u1="\" u2="&#x2039;" k="53" />
<hkern u1="\" u2="&#x2022;" k="53" />
<hkern u1="\" u2="&#x201d;" k="186" />
<hkern u1="\" u2="&#x201c;" k="186" />
<hkern u1="\" u2="&#x2019;" k="186" />
<hkern u1="\" u2="&#x2018;" k="186" />
<hkern u1="\" u2="&#x2014;" k="53" />
<hkern u1="\" u2="&#x2013;" k="53" />
<hkern u1="\" u2="&#x178;" k="156" />
<hkern u1="\" u2="&#x152;" k="48" />
<hkern u1="\" u2="&#x106;" k="48" />
<hkern u1="\" u2="&#xff;" k="48" />
<hkern u1="\" u2="&#xfd;" k="48" />
<hkern u1="\" u2="&#xdd;" k="156" />
<hkern u1="\" u2="&#xdc;" k="41" />
<hkern u1="\" u2="&#xdb;" k="41" />
<hkern u1="\" u2="&#xda;" k="41" />
<hkern u1="\" u2="&#xd9;" k="41" />
<hkern u1="\" u2="&#xd8;" k="48" />
<hkern u1="\" u2="&#xd6;" k="48" />
<hkern u1="\" u2="&#xd5;" k="48" />
<hkern u1="\" u2="&#xd4;" k="48" />
<hkern u1="\" u2="&#xd3;" k="48" />
<hkern u1="\" u2="&#xd2;" k="48" />
<hkern u1="\" u2="&#xc7;" k="48" />
<hkern u1="\" u2="&#xbb;" k="53" />
<hkern u1="\" u2="&#xba;" k="186" />
<hkern u1="\" u2="&#xb7;" k="53" />
<hkern u1="\" u2="&#xb0;" k="186" />
<hkern u1="\" u2="&#xae;" k="48" />
<hkern u1="\" u2="&#xab;" k="53" />
<hkern u1="\" u2="&#xaa;" k="186" />
<hkern u1="\" u2="&#xa9;" k="48" />
<hkern u1="\" u2="y" k="48" />
<hkern u1="\" u2="w" k="33" />
<hkern u1="\" u2="v" k="48" />
<hkern u1="\" u2="t" k="58" />
<hkern u1="\" u2="\" k="117" />
<hkern u1="\" u2="Y" k="156" />
<hkern u1="\" u2="W" k="85" />
<hkern u1="\" u2="V" k="117" />
<hkern u1="\" u2="U" k="41" />
<hkern u1="\" u2="T" k="135" />
<hkern u1="\" u2="Q" k="48" />
<hkern u1="\" u2="O" k="48" />
<hkern u1="\" u2="J" k="-63" />
<hkern u1="\" u2="G" k="48" />
<hkern u1="\" u2="C" k="48" />
<hkern u1="\" u2="&#x40;" k="48" />
<hkern u1="\" u2="&#x2d;" k="53" />
<hkern u1="\" u2="&#x2a;" k="186" />
<hkern u1="\" u2="&#x27;" k="186" />
<hkern u1="\" u2="&#x22;" k="186" />
<hkern u1="b" u2="&#x201d;" k="82" />
<hkern u1="b" u2="&#x201c;" k="82" />
<hkern u1="b" u2="&#x2019;" k="82" />
<hkern u1="b" u2="&#x2018;" k="82" />
<hkern u1="b" u2="&#xba;" k="82" />
<hkern u1="b" u2="&#xb0;" k="82" />
<hkern u1="b" u2="&#xaa;" k="82" />
<hkern u1="b" u2="&#x7d;" k="32" />
<hkern u1="b" u2="x" k="52" />
<hkern u1="b" u2="]" k="32" />
<hkern u1="b" u2="&#x2a;" k="82" />
<hkern u1="b" u2="&#x29;" k="32" />
<hkern u1="b" u2="&#x27;" k="82" />
<hkern u1="b" u2="&#x22;" k="82" />
<hkern u1="e" u2="&#x201d;" k="82" />
<hkern u1="e" u2="&#x201c;" k="82" />
<hkern u1="e" u2="&#x2019;" k="82" />
<hkern u1="e" u2="&#x2018;" k="82" />
<hkern u1="e" u2="&#xba;" k="82" />
<hkern u1="e" u2="&#xb0;" k="82" />
<hkern u1="e" u2="&#xaa;" k="82" />
<hkern u1="e" u2="&#x7d;" k="32" />
<hkern u1="e" u2="x" k="52" />
<hkern u1="e" u2="]" k="32" />
<hkern u1="e" u2="&#x2a;" k="82" />
<hkern u1="e" u2="&#x29;" k="32" />
<hkern u1="e" u2="&#x27;" k="82" />
<hkern u1="e" u2="&#x22;" k="82" />
<hkern u1="f" u2="&#x2026;" k="132" />
<hkern u1="f" u2="&#x201e;" k="132" />
<hkern u1="f" u2="&#x201d;" k="-71" />
<hkern u1="f" u2="&#x201c;" k="-71" />
<hkern u1="f" u2="&#x201a;" k="132" />
<hkern u1="f" u2="&#x2019;" k="-71" />
<hkern u1="f" u2="&#x2018;" k="-71" />
<hkern u1="f" u2="&#xba;" k="-71" />
<hkern u1="f" u2="&#xb0;" k="-71" />
<hkern u1="f" u2="&#xaa;" k="-71" />
<hkern u1="f" u2="&#x2e;" k="132" />
<hkern u1="f" u2="&#x2c;" k="132" />
<hkern u1="f" u2="&#x2a;" k="-71" />
<hkern u1="f" u2="&#x27;" k="-71" />
<hkern u1="f" u2="&#x22;" k="-71" />
<hkern u1="h" u2="&#x201d;" k="61" />
<hkern u1="h" u2="&#x201c;" k="61" />
<hkern u1="h" u2="&#x2019;" k="61" />
<hkern u1="h" u2="&#x2018;" k="61" />
<hkern u1="h" u2="&#xff;" k="32" />
<hkern u1="h" u2="&#xfd;" k="32" />
<hkern u1="h" u2="&#xba;" k="61" />
<hkern u1="h" u2="&#xb0;" k="61" />
<hkern u1="h" u2="&#xaa;" k="61" />
<hkern u1="h" u2="y" k="43" />
<hkern u1="h" u2="v" k="32" />
<hkern u1="h" u2="&#x2a;" k="61" />
<hkern u1="h" u2="&#x27;" k="61" />
<hkern u1="h" u2="&#x22;" k="61" />
<hkern u1="k" u2="&#x153;" k="52" />
<hkern u1="k" u2="&#x119;" k="52" />
<hkern u1="k" u2="&#x107;" k="52" />
<hkern u1="k" u2="&#x105;" k="52" />
<hkern u1="k" u2="&#xf8;" k="52" />
<hkern u1="k" u2="&#xf6;" k="52" />
<hkern u1="k" u2="&#xf5;" k="52" />
<hkern u1="k" u2="&#xf4;" k="52" />
<hkern u1="k" u2="&#xf3;" k="52" />
<hkern u1="k" u2="&#xf2;" k="52" />
<hkern u1="k" u2="&#xf0;" k="52" />
<hkern u1="k" u2="&#xeb;" k="52" />
<hkern u1="k" u2="&#xea;" k="52" />
<hkern u1="k" u2="&#xe9;" k="52" />
<hkern u1="k" u2="&#xe8;" k="52" />
<hkern u1="k" u2="&#xe7;" k="52" />
<hkern u1="k" u2="&#xe6;" k="52" />
<hkern u1="k" u2="&#xe5;" k="52" />
<hkern u1="k" u2="&#xe4;" k="52" />
<hkern u1="k" u2="&#xe3;" k="52" />
<hkern u1="k" u2="&#xe2;" k="52" />
<hkern u1="k" u2="&#xe1;" k="52" />
<hkern u1="k" u2="&#xe0;" k="52" />
<hkern u1="k" u2="q" k="52" />
<hkern u1="k" u2="o" k="52" />
<hkern u1="k" u2="e" k="52" />
<hkern u1="k" u2="d" k="52" />
<hkern u1="k" u2="c" k="52" />
<hkern u1="k" u2="a" k="52" />
<hkern u1="m" u2="&#x201d;" k="61" />
<hkern u1="m" u2="&#x201c;" k="61" />
<hkern u1="m" u2="&#x2019;" k="61" />
<hkern u1="m" u2="&#x2018;" k="61" />
<hkern u1="m" u2="&#xff;" k="32" />
<hkern u1="m" u2="&#xfd;" k="32" />
<hkern u1="m" u2="&#xba;" k="61" />
<hkern u1="m" u2="&#xb0;" k="61" />
<hkern u1="m" u2="&#xaa;" k="61" />
<hkern u1="m" u2="y" k="43" />
<hkern u1="m" u2="v" k="32" />
<hkern u1="m" u2="&#x2a;" k="61" />
<hkern u1="m" u2="&#x27;" k="61" />
<hkern u1="m" u2="&#x22;" k="61" />
<hkern u1="n" u2="&#x201d;" k="61" />
<hkern u1="n" u2="&#x201c;" k="61" />
<hkern u1="n" u2="&#x2019;" k="61" />
<hkern u1="n" u2="&#x2018;" k="61" />
<hkern u1="n" u2="&#xff;" k="32" />
<hkern u1="n" u2="&#xfd;" k="32" />
<hkern u1="n" u2="&#xba;" k="61" />
<hkern u1="n" u2="&#xb0;" k="61" />
<hkern u1="n" u2="&#xaa;" k="61" />
<hkern u1="n" u2="y" k="43" />
<hkern u1="n" u2="v" k="32" />
<hkern u1="n" u2="&#x2a;" k="61" />
<hkern u1="n" u2="&#x27;" k="61" />
<hkern u1="n" u2="&#x22;" k="61" />
<hkern u1="o" u2="&#x201d;" k="82" />
<hkern u1="o" u2="&#x201c;" k="82" />
<hkern u1="o" u2="&#x2019;" k="82" />
<hkern u1="o" u2="&#x2018;" k="82" />
<hkern u1="o" u2="&#xba;" k="82" />
<hkern u1="o" u2="&#xb0;" k="82" />
<hkern u1="o" u2="&#xaa;" k="82" />
<hkern u1="o" u2="&#x7d;" k="32" />
<hkern u1="o" u2="x" k="52" />
<hkern u1="o" u2="]" k="32" />
<hkern u1="o" u2="&#x2a;" k="82" />
<hkern u1="o" u2="&#x29;" k="32" />
<hkern u1="o" u2="&#x27;" k="82" />
<hkern u1="o" u2="&#x22;" k="82" />
<hkern u1="p" u2="&#x201d;" k="82" />
<hkern u1="p" u2="&#x201c;" k="82" />
<hkern u1="p" u2="&#x2019;" k="82" />
<hkern u1="p" u2="&#x2018;" k="82" />
<hkern u1="p" u2="&#xba;" k="82" />
<hkern u1="p" u2="&#xb0;" k="82" />
<hkern u1="p" u2="&#xaa;" k="82" />
<hkern u1="p" u2="&#x7d;" k="32" />
<hkern u1="p" u2="x" k="52" />
<hkern u1="p" u2="]" k="32" />
<hkern u1="p" u2="&#x2a;" k="82" />
<hkern u1="p" u2="&#x29;" k="32" />
<hkern u1="p" u2="&#x27;" k="82" />
<hkern u1="p" u2="&#x22;" k="82" />
<hkern u1="r" u2="&#x2026;" k="156" />
<hkern u1="r" u2="&#x201e;" k="156" />
<hkern u1="r" u2="&#x201a;" k="156" />
<hkern u1="r" u2="&#x153;" k="41" />
<hkern u1="r" u2="&#x119;" k="41" />
<hkern u1="r" u2="&#x107;" k="41" />
<hkern u1="r" u2="&#x105;" k="41" />
<hkern u1="r" u2="&#xf8;" k="41" />
<hkern u1="r" u2="&#xf6;" k="41" />
<hkern u1="r" u2="&#xf5;" k="41" />
<hkern u1="r" u2="&#xf4;" k="41" />
<hkern u1="r" u2="&#xf3;" k="41" />
<hkern u1="r" u2="&#xf2;" k="41" />
<hkern u1="r" u2="&#xf0;" k="41" />
<hkern u1="r" u2="&#xeb;" k="41" />
<hkern u1="r" u2="&#xea;" k="41" />
<hkern u1="r" u2="&#xe9;" k="41" />
<hkern u1="r" u2="&#xe8;" k="41" />
<hkern u1="r" u2="&#xe7;" k="41" />
<hkern u1="r" u2="&#xe6;" k="41" />
<hkern u1="r" u2="&#xe5;" k="41" />
<hkern u1="r" u2="&#xe4;" k="41" />
<hkern u1="r" u2="&#xe3;" k="41" />
<hkern u1="r" u2="&#xe2;" k="41" />
<hkern u1="r" u2="&#xe1;" k="41" />
<hkern u1="r" u2="&#xe0;" k="41" />
<hkern u1="r" u2="q" k="41" />
<hkern u1="r" u2="o" k="41" />
<hkern u1="r" u2="e" k="41" />
<hkern u1="r" u2="d" k="41" />
<hkern u1="r" u2="c" k="41" />
<hkern u1="r" u2="a" k="41" />
<hkern u1="r" u2="&#x2e;" k="156" />
<hkern u1="r" u2="&#x2c;" k="156" />
<hkern u1="v" u2="&#x2206;" k="48" />
<hkern u1="v" u2="&#x2026;" k="145" />
<hkern u1="v" u2="&#x201e;" k="145" />
<hkern u1="v" u2="&#x201a;" k="145" />
<hkern u1="v" u2="&#x153;" k="27" />
<hkern u1="v" u2="&#x119;" k="27" />
<hkern u1="v" u2="&#x107;" k="27" />
<hkern u1="v" u2="&#x105;" k="27" />
<hkern u1="v" u2="&#x104;" k="48" />
<hkern u1="v" u2="&#xf8;" k="27" />
<hkern u1="v" u2="&#xf6;" k="27" />
<hkern u1="v" u2="&#xf5;" k="27" />
<hkern u1="v" u2="&#xf4;" k="27" />
<hkern u1="v" u2="&#xf3;" k="27" />
<hkern u1="v" u2="&#xf2;" k="27" />
<hkern u1="v" u2="&#xf0;" k="27" />
<hkern u1="v" u2="&#xeb;" k="27" />
<hkern u1="v" u2="&#xea;" k="27" />
<hkern u1="v" u2="&#xe9;" k="27" />
<hkern u1="v" u2="&#xe8;" k="27" />
<hkern u1="v" u2="&#xe7;" k="27" />
<hkern u1="v" u2="&#xe6;" k="27" />
<hkern u1="v" u2="&#xe5;" k="27" />
<hkern u1="v" u2="&#xe4;" k="27" />
<hkern u1="v" u2="&#xe3;" k="27" />
<hkern u1="v" u2="&#xe2;" k="27" />
<hkern u1="v" u2="&#xe1;" k="27" />
<hkern u1="v" u2="&#xe0;" k="27" />
<hkern u1="v" u2="&#xc6;" k="48" />
<hkern u1="v" u2="&#xc5;" k="48" />
<hkern u1="v" u2="&#xc4;" k="48" />
<hkern u1="v" u2="&#xc3;" k="48" />
<hkern u1="v" u2="&#xc2;" k="48" />
<hkern u1="v" u2="&#xc1;" k="48" />
<hkern u1="v" u2="&#xc0;" k="48" />
<hkern u1="v" u2="q" k="27" />
<hkern u1="v" u2="o" k="27" />
<hkern u1="v" u2="e" k="27" />
<hkern u1="v" u2="d" k="27" />
<hkern u1="v" u2="c" k="27" />
<hkern u1="v" u2="a" k="27" />
<hkern u1="v" u2="A" k="48" />
<hkern u1="v" u2="&#x2f;" k="48" />
<hkern u1="v" u2="&#x2e;" k="145" />
<hkern u1="v" u2="&#x2c;" k="145" />
<hkern u1="v" u2="&#x26;" k="48" />
<hkern u1="w" u2="&#x2206;" k="33" />
<hkern u1="w" u2="&#x2026;" k="85" />
<hkern u1="w" u2="&#x201e;" k="85" />
<hkern u1="w" u2="&#x201a;" k="85" />
<hkern u1="w" u2="&#x104;" k="33" />
<hkern u1="w" u2="&#xc6;" k="33" />
<hkern u1="w" u2="&#xc5;" k="33" />
<hkern u1="w" u2="&#xc4;" k="33" />
<hkern u1="w" u2="&#xc3;" k="33" />
<hkern u1="w" u2="&#xc2;" k="33" />
<hkern u1="w" u2="&#xc1;" k="33" />
<hkern u1="w" u2="&#xc0;" k="33" />
<hkern u1="w" u2="A" k="33" />
<hkern u1="w" u2="&#x2f;" k="33" />
<hkern u1="w" u2="&#x2e;" k="85" />
<hkern u1="w" u2="&#x2c;" k="85" />
<hkern u1="w" u2="&#x26;" k="33" />
<hkern u1="x" u2="&#x153;" k="52" />
<hkern u1="x" u2="&#x119;" k="52" />
<hkern u1="x" u2="&#x107;" k="52" />
<hkern u1="x" u2="&#x105;" k="52" />
<hkern u1="x" u2="&#xf8;" k="52" />
<hkern u1="x" u2="&#xf6;" k="52" />
<hkern u1="x" u2="&#xf5;" k="52" />
<hkern u1="x" u2="&#xf4;" k="52" />
<hkern u1="x" u2="&#xf3;" k="52" />
<hkern u1="x" u2="&#xf2;" k="52" />
<hkern u1="x" u2="&#xf0;" k="52" />
<hkern u1="x" u2="&#xeb;" k="52" />
<hkern u1="x" u2="&#xea;" k="52" />
<hkern u1="x" u2="&#xe9;" k="52" />
<hkern u1="x" u2="&#xe8;" k="52" />
<hkern u1="x" u2="&#xe7;" k="52" />
<hkern u1="x" u2="&#xe6;" k="52" />
<hkern u1="x" u2="&#xe5;" k="52" />
<hkern u1="x" u2="&#xe4;" k="52" />
<hkern u1="x" u2="&#xe3;" k="52" />
<hkern u1="x" u2="&#xe2;" k="52" />
<hkern u1="x" u2="&#xe1;" k="52" />
<hkern u1="x" u2="&#xe0;" k="52" />
<hkern u1="x" u2="q" k="52" />
<hkern u1="x" u2="o" k="52" />
<hkern u1="x" u2="e" k="52" />
<hkern u1="x" u2="d" k="52" />
<hkern u1="x" u2="c" k="52" />
<hkern u1="x" u2="a" k="52" />
<hkern u1="y" u2="&#x2206;" k="48" />
<hkern u1="y" u2="&#x2026;" k="156" />
<hkern u1="y" u2="&#x201e;" k="156" />
<hkern u1="y" u2="&#x201a;" k="156" />
<hkern u1="y" u2="&#x153;" k="27" />
<hkern u1="y" u2="&#x119;" k="27" />
<hkern u1="y" u2="&#x107;" k="27" />
<hkern u1="y" u2="&#x105;" k="27" />
<hkern u1="y" u2="&#x104;" k="48" />
<hkern u1="y" u2="&#xf8;" k="27" />
<hkern u1="y" u2="&#xf6;" k="27" />
<hkern u1="y" u2="&#xf5;" k="27" />
<hkern u1="y" u2="&#xf4;" k="27" />
<hkern u1="y" u2="&#xf3;" k="27" />
<hkern u1="y" u2="&#xf2;" k="27" />
<hkern u1="y" u2="&#xf0;" k="27" />
<hkern u1="y" u2="&#xeb;" k="27" />
<hkern u1="y" u2="&#xea;" k="27" />
<hkern u1="y" u2="&#xe9;" k="27" />
<hkern u1="y" u2="&#xe8;" k="27" />
<hkern u1="y" u2="&#xe7;" k="27" />
<hkern u1="y" u2="&#xe6;" k="27" />
<hkern u1="y" u2="&#xe5;" k="27" />
<hkern u1="y" u2="&#xe4;" k="27" />
<hkern u1="y" u2="&#xe3;" k="27" />
<hkern u1="y" u2="&#xe2;" k="27" />
<hkern u1="y" u2="&#xe1;" k="27" />
<hkern u1="y" u2="&#xe0;" k="27" />
<hkern u1="y" u2="&#xc6;" k="48" />
<hkern u1="y" u2="&#xc5;" k="48" />
<hkern u1="y" u2="&#xc4;" k="48" />
<hkern u1="y" u2="&#xc3;" k="48" />
<hkern u1="y" u2="&#xc2;" k="48" />
<hkern u1="y" u2="&#xc1;" k="48" />
<hkern u1="y" u2="&#xc0;" k="48" />
<hkern u1="y" u2="q" k="27" />
<hkern u1="y" u2="o" k="27" />
<hkern u1="y" u2="e" k="27" />
<hkern u1="y" u2="d" k="27" />
<hkern u1="y" u2="c" k="27" />
<hkern u1="y" u2="a" k="27" />
<hkern u1="y" u2="A" k="48" />
<hkern u1="y" u2="&#x2f;" k="48" />
<hkern u1="y" u2="&#x2e;" k="156" />
<hkern u1="y" u2="&#x2c;" k="156" />
<hkern u1="y" u2="&#x26;" k="48" />
<hkern u1="&#x7b;" u2="&#x153;" k="32" />
<hkern u1="&#x7b;" u2="&#x152;" k="41" />
<hkern u1="&#x7b;" u2="&#x119;" k="32" />
<hkern u1="&#x7b;" u2="&#x107;" k="32" />
<hkern u1="&#x7b;" u2="&#x106;" k="41" />
<hkern u1="&#x7b;" u2="&#x105;" k="32" />
<hkern u1="&#x7b;" u2="&#xf8;" k="32" />
<hkern u1="&#x7b;" u2="&#xf6;" k="32" />
<hkern u1="&#x7b;" u2="&#xf5;" k="32" />
<hkern u1="&#x7b;" u2="&#xf4;" k="32" />
<hkern u1="&#x7b;" u2="&#xf3;" k="32" />
<hkern u1="&#x7b;" u2="&#xf2;" k="32" />
<hkern u1="&#x7b;" u2="&#xf0;" k="32" />
<hkern u1="&#x7b;" u2="&#xeb;" k="32" />
<hkern u1="&#x7b;" u2="&#xea;" k="32" />
<hkern u1="&#x7b;" u2="&#xe9;" k="32" />
<hkern u1="&#x7b;" u2="&#xe8;" k="32" />
<hkern u1="&#x7b;" u2="&#xe7;" k="32" />
<hkern u1="&#x7b;" u2="&#xe6;" k="32" />
<hkern u1="&#x7b;" u2="&#xe5;" k="32" />
<hkern u1="&#x7b;" u2="&#xe4;" k="32" />
<hkern u1="&#x7b;" u2="&#xe3;" k="32" />
<hkern u1="&#x7b;" u2="&#xe2;" k="32" />
<hkern u1="&#x7b;" u2="&#xe1;" k="32" />
<hkern u1="&#x7b;" u2="&#xe0;" k="32" />
<hkern u1="&#x7b;" u2="&#xd8;" k="41" />
<hkern u1="&#x7b;" u2="&#xd6;" k="41" />
<hkern u1="&#x7b;" u2="&#xd5;" k="41" />
<hkern u1="&#x7b;" u2="&#xd4;" k="41" />
<hkern u1="&#x7b;" u2="&#xd3;" k="41" />
<hkern u1="&#x7b;" u2="&#xd2;" k="41" />
<hkern u1="&#x7b;" u2="&#xc7;" k="41" />
<hkern u1="&#x7b;" u2="&#xae;" k="41" />
<hkern u1="&#x7b;" u2="&#xa9;" k="41" />
<hkern u1="&#x7b;" u2="q" k="32" />
<hkern u1="&#x7b;" u2="o" k="32" />
<hkern u1="&#x7b;" u2="e" k="32" />
<hkern u1="&#x7b;" u2="d" k="32" />
<hkern u1="&#x7b;" u2="c" k="32" />
<hkern u1="&#x7b;" u2="a" k="32" />
<hkern u1="&#x7b;" u2="Q" k="41" />
<hkern u1="&#x7b;" u2="O" k="41" />
<hkern u1="&#x7b;" u2="G" k="41" />
<hkern u1="&#x7b;" u2="C" k="41" />
<hkern u1="&#x7b;" u2="&#x40;" k="41" />
<hkern u1="&#xa9;" u2="&#x2206;" k="37" />
<hkern u1="&#xa9;" u2="&#x201d;" k="56" />
<hkern u1="&#xa9;" u2="&#x201c;" k="56" />
<hkern u1="&#xa9;" u2="&#x2019;" k="56" />
<hkern u1="&#xa9;" u2="&#x2018;" k="56" />
<hkern u1="&#xa9;" u2="&#x17d;" k="71" />
<hkern u1="&#xa9;" u2="&#x17b;" k="71" />
<hkern u1="&#xa9;" u2="&#x179;" k="71" />
<hkern u1="&#xa9;" u2="&#x178;" k="82" />
<hkern u1="&#xa9;" u2="&#x104;" k="37" />
<hkern u1="&#xa9;" u2="&#xdd;" k="82" />
<hkern u1="&#xa9;" u2="&#xc6;" k="37" />
<hkern u1="&#xa9;" u2="&#xc5;" k="37" />
<hkern u1="&#xa9;" u2="&#xc4;" k="37" />
<hkern u1="&#xa9;" u2="&#xc3;" k="37" />
<hkern u1="&#xa9;" u2="&#xc2;" k="37" />
<hkern u1="&#xa9;" u2="&#xc1;" k="37" />
<hkern u1="&#xa9;" u2="&#xc0;" k="37" />
<hkern u1="&#xa9;" u2="&#xba;" k="56" />
<hkern u1="&#xa9;" u2="&#xb0;" k="56" />
<hkern u1="&#xa9;" u2="&#xaa;" k="56" />
<hkern u1="&#xa9;" u2="&#x7d;" k="41" />
<hkern u1="&#xa9;" u2="]" k="41" />
<hkern u1="&#xa9;" u2="\" k="52" />
<hkern u1="&#xa9;" u2="Z" k="71" />
<hkern u1="&#xa9;" u2="Y" k="82" />
<hkern u1="&#xa9;" u2="X" k="70" />
<hkern u1="&#xa9;" u2="V" k="52" />
<hkern u1="&#xa9;" u2="T" k="99" />
<hkern u1="&#xa9;" u2="A" k="37" />
<hkern u1="&#xa9;" u2="&#x2f;" k="37" />
<hkern u1="&#xa9;" u2="&#x2a;" k="56" />
<hkern u1="&#xa9;" u2="&#x29;" k="41" />
<hkern u1="&#xa9;" u2="&#x27;" k="56" />
<hkern u1="&#xa9;" u2="&#x26;" k="37" />
<hkern u1="&#xa9;" u2="&#x22;" k="56" />
<hkern u1="&#xaa;" u2="&#x2206;" k="175" />
<hkern u1="&#xaa;" u2="&#x203a;" k="85" />
<hkern u1="&#xaa;" u2="&#x2039;" k="85" />
<hkern u1="&#xaa;" u2="&#x2026;" k="94" />
<hkern u1="&#xaa;" u2="&#x2022;" k="85" />
<hkern u1="&#xaa;" u2="&#x201e;" k="94" />
<hkern u1="&#xaa;" u2="&#x201a;" k="94" />
<hkern u1="&#xaa;" u2="&#x2014;" k="85" />
<hkern u1="&#xaa;" u2="&#x2013;" k="85" />
<hkern u1="&#xaa;" u2="&#x178;" k="-46" />
<hkern u1="&#xaa;" u2="&#x153;" k="93" />
<hkern u1="&#xaa;" u2="&#x119;" k="93" />
<hkern u1="&#xaa;" u2="&#x107;" k="93" />
<hkern u1="&#xaa;" u2="&#x105;" k="93" />
<hkern u1="&#xaa;" u2="&#x104;" k="175" />
<hkern u1="&#xaa;" u2="&#xf8;" k="93" />
<hkern u1="&#xaa;" u2="&#xf6;" k="93" />
<hkern u1="&#xaa;" u2="&#xf5;" k="93" />
<hkern u1="&#xaa;" u2="&#xf4;" k="93" />
<hkern u1="&#xaa;" u2="&#xf3;" k="93" />
<hkern u1="&#xaa;" u2="&#xf2;" k="93" />
<hkern u1="&#xaa;" u2="&#xf0;" k="93" />
<hkern u1="&#xaa;" u2="&#xeb;" k="93" />
<hkern u1="&#xaa;" u2="&#xea;" k="93" />
<hkern u1="&#xaa;" u2="&#xe9;" k="93" />
<hkern u1="&#xaa;" u2="&#xe8;" k="93" />
<hkern u1="&#xaa;" u2="&#xe7;" k="93" />
<hkern u1="&#xaa;" u2="&#xe6;" k="93" />
<hkern u1="&#xaa;" u2="&#xe5;" k="93" />
<hkern u1="&#xaa;" u2="&#xe4;" k="93" />
<hkern u1="&#xaa;" u2="&#xe3;" k="93" />
<hkern u1="&#xaa;" u2="&#xe2;" k="93" />
<hkern u1="&#xaa;" u2="&#xe1;" k="93" />
<hkern u1="&#xaa;" u2="&#xe0;" k="93" />
<hkern u1="&#xaa;" u2="&#xdd;" k="-46" />
<hkern u1="&#xaa;" u2="&#xc6;" k="175" />
<hkern u1="&#xaa;" u2="&#xc5;" k="175" />
<hkern u1="&#xaa;" u2="&#xc4;" k="175" />
<hkern u1="&#xaa;" u2="&#xc3;" k="175" />
<hkern u1="&#xaa;" u2="&#xc2;" k="175" />
<hkern u1="&#xaa;" u2="&#xc1;" k="175" />
<hkern u1="&#xaa;" u2="&#xc0;" k="175" />
<hkern u1="&#xaa;" u2="&#xbb;" k="85" />
<hkern u1="&#xaa;" u2="&#xb7;" k="85" />
<hkern u1="&#xaa;" u2="&#xab;" k="85" />
<hkern u1="&#xaa;" u2="q" k="93" />
<hkern u1="&#xaa;" u2="o" k="93" />
<hkern u1="&#xaa;" u2="e" k="93" />
<hkern u1="&#xaa;" u2="d" k="93" />
<hkern u1="&#xaa;" u2="c" k="93" />
<hkern u1="&#xaa;" u2="a" k="93" />
<hkern u1="&#xaa;" u2="\" k="-55" />
<hkern u1="&#xaa;" u2="Y" k="-46" />
<hkern u1="&#xaa;" u2="W" k="-49" />
<hkern u1="&#xaa;" u2="V" k="-55" />
<hkern u1="&#xaa;" u2="A" k="175" />
<hkern u1="&#xaa;" u2="&#x2f;" k="175" />
<hkern u1="&#xaa;" u2="&#x2e;" k="94" />
<hkern u1="&#xaa;" u2="&#x2d;" k="85" />
<hkern u1="&#xaa;" u2="&#x2c;" k="94" />
<hkern u1="&#xaa;" u2="&#x26;" k="175" />
<hkern u1="&#xab;" u2="&#x2206;" k="53" />
<hkern u1="&#xab;" u2="&#x2026;" k="166" />
<hkern u1="&#xab;" u2="&#x201e;" k="166" />
<hkern u1="&#xab;" u2="&#x201d;" k="85" />
<hkern u1="&#xab;" u2="&#x201c;" k="85" />
<hkern u1="&#xab;" u2="&#x201a;" k="166" />
<hkern u1="&#xab;" u2="&#x2019;" k="85" />
<hkern u1="&#xab;" u2="&#x2018;" k="85" />
<hkern u1="&#xab;" u2="&#x17d;" k="56" />
<hkern u1="&#xab;" u2="&#x17b;" k="56" />
<hkern u1="&#xab;" u2="&#x179;" k="56" />
<hkern u1="&#xab;" u2="&#x178;" k="197" />
<hkern u1="&#xab;" u2="&#x104;" k="53" />
<hkern u1="&#xab;" u2="&#xdd;" k="197" />
<hkern u1="&#xab;" u2="&#xc6;" k="53" />
<hkern u1="&#xab;" u2="&#xc5;" k="53" />
<hkern u1="&#xab;" u2="&#xc4;" k="53" />
<hkern u1="&#xab;" u2="&#xc3;" k="53" />
<hkern u1="&#xab;" u2="&#xc2;" k="53" />
<hkern u1="&#xab;" u2="&#xc1;" k="53" />
<hkern u1="&#xab;" u2="&#xc0;" k="53" />
<hkern u1="&#xab;" u2="&#xba;" k="85" />
<hkern u1="&#xab;" u2="&#xb0;" k="85" />
<hkern u1="&#xab;" u2="&#xaa;" k="85" />
<hkern u1="&#xab;" u2="\" k="125" />
<hkern u1="&#xab;" u2="Z" k="56" />
<hkern u1="&#xab;" u2="Y" k="197" />
<hkern u1="&#xab;" u2="X" k="72" />
<hkern u1="&#xab;" u2="V" k="125" />
<hkern u1="&#xab;" u2="T" k="196" />
<hkern u1="&#xab;" u2="A" k="53" />
<hkern u1="&#xab;" u2="&#x2f;" k="53" />
<hkern u1="&#xab;" u2="&#x2e;" k="166" />
<hkern u1="&#xab;" u2="&#x2c;" k="166" />
<hkern u1="&#xab;" u2="&#x2a;" k="85" />
<hkern u1="&#xab;" u2="&#x27;" k="85" />
<hkern u1="&#xab;" u2="&#x26;" k="53" />
<hkern u1="&#xab;" u2="&#x22;" k="85" />
<hkern u1="&#xae;" u2="&#x2206;" k="37" />
<hkern u1="&#xae;" u2="&#x201d;" k="56" />
<hkern u1="&#xae;" u2="&#x201c;" k="56" />
<hkern u1="&#xae;" u2="&#x2019;" k="56" />
<hkern u1="&#xae;" u2="&#x2018;" k="56" />
<hkern u1="&#xae;" u2="&#x17d;" k="71" />
<hkern u1="&#xae;" u2="&#x17b;" k="71" />
<hkern u1="&#xae;" u2="&#x179;" k="71" />
<hkern u1="&#xae;" u2="&#x178;" k="82" />
<hkern u1="&#xae;" u2="&#x104;" k="37" />
<hkern u1="&#xae;" u2="&#xdd;" k="82" />
<hkern u1="&#xae;" u2="&#xc6;" k="37" />
<hkern u1="&#xae;" u2="&#xc5;" k="37" />
<hkern u1="&#xae;" u2="&#xc4;" k="37" />
<hkern u1="&#xae;" u2="&#xc3;" k="37" />
<hkern u1="&#xae;" u2="&#xc2;" k="37" />
<hkern u1="&#xae;" u2="&#xc1;" k="37" />
<hkern u1="&#xae;" u2="&#xc0;" k="37" />
<hkern u1="&#xae;" u2="&#xba;" k="56" />
<hkern u1="&#xae;" u2="&#xb0;" k="56" />
<hkern u1="&#xae;" u2="&#xaa;" k="56" />
<hkern u1="&#xae;" u2="&#x7d;" k="41" />
<hkern u1="&#xae;" u2="]" k="41" />
<hkern u1="&#xae;" u2="\" k="52" />
<hkern u1="&#xae;" u2="Z" k="71" />
<hkern u1="&#xae;" u2="Y" k="82" />
<hkern u1="&#xae;" u2="X" k="70" />
<hkern u1="&#xae;" u2="V" k="52" />
<hkern u1="&#xae;" u2="T" k="99" />
<hkern u1="&#xae;" u2="A" k="37" />
<hkern u1="&#xae;" u2="&#x2f;" k="37" />
<hkern u1="&#xae;" u2="&#x2a;" k="56" />
<hkern u1="&#xae;" u2="&#x29;" k="41" />
<hkern u1="&#xae;" u2="&#x27;" k="56" />
<hkern u1="&#xae;" u2="&#x26;" k="37" />
<hkern u1="&#xae;" u2="&#x22;" k="56" />
<hkern u1="&#xb0;" u2="&#x2206;" k="175" />
<hkern u1="&#xb0;" u2="&#x203a;" k="85" />
<hkern u1="&#xb0;" u2="&#x2039;" k="85" />
<hkern u1="&#xb0;" u2="&#x2026;" k="94" />
<hkern u1="&#xb0;" u2="&#x2022;" k="85" />
<hkern u1="&#xb0;" u2="&#x201e;" k="94" />
<hkern u1="&#xb0;" u2="&#x201a;" k="94" />
<hkern u1="&#xb0;" u2="&#x2014;" k="85" />
<hkern u1="&#xb0;" u2="&#x2013;" k="85" />
<hkern u1="&#xb0;" u2="&#x178;" k="-46" />
<hkern u1="&#xb0;" u2="&#x153;" k="93" />
<hkern u1="&#xb0;" u2="&#x119;" k="93" />
<hkern u1="&#xb0;" u2="&#x107;" k="93" />
<hkern u1="&#xb0;" u2="&#x105;" k="93" />
<hkern u1="&#xb0;" u2="&#x104;" k="175" />
<hkern u1="&#xb0;" u2="&#xf8;" k="93" />
<hkern u1="&#xb0;" u2="&#xf6;" k="93" />
<hkern u1="&#xb0;" u2="&#xf5;" k="93" />
<hkern u1="&#xb0;" u2="&#xf4;" k="93" />
<hkern u1="&#xb0;" u2="&#xf3;" k="93" />
<hkern u1="&#xb0;" u2="&#xf2;" k="93" />
<hkern u1="&#xb0;" u2="&#xf0;" k="93" />
<hkern u1="&#xb0;" u2="&#xeb;" k="93" />
<hkern u1="&#xb0;" u2="&#xea;" k="93" />
<hkern u1="&#xb0;" u2="&#xe9;" k="93" />
<hkern u1="&#xb0;" u2="&#xe8;" k="93" />
<hkern u1="&#xb0;" u2="&#xe7;" k="93" />
<hkern u1="&#xb0;" u2="&#xe6;" k="93" />
<hkern u1="&#xb0;" u2="&#xe5;" k="93" />
<hkern u1="&#xb0;" u2="&#xe4;" k="93" />
<hkern u1="&#xb0;" u2="&#xe3;" k="93" />
<hkern u1="&#xb0;" u2="&#xe2;" k="93" />
<hkern u1="&#xb0;" u2="&#xe1;" k="93" />
<hkern u1="&#xb0;" u2="&#xe0;" k="93" />
<hkern u1="&#xb0;" u2="&#xdd;" k="-46" />
<hkern u1="&#xb0;" u2="&#xc6;" k="175" />
<hkern u1="&#xb0;" u2="&#xc5;" k="175" />
<hkern u1="&#xb0;" u2="&#xc4;" k="175" />
<hkern u1="&#xb0;" u2="&#xc3;" k="175" />
<hkern u1="&#xb0;" u2="&#xc2;" k="175" />
<hkern u1="&#xb0;" u2="&#xc1;" k="175" />
<hkern u1="&#xb0;" u2="&#xc0;" k="175" />
<hkern u1="&#xb0;" u2="&#xbb;" k="85" />
<hkern u1="&#xb0;" u2="&#xb7;" k="85" />
<hkern u1="&#xb0;" u2="&#xab;" k="85" />
<hkern u1="&#xb0;" u2="q" k="93" />
<hkern u1="&#xb0;" u2="o" k="93" />
<hkern u1="&#xb0;" u2="e" k="93" />
<hkern u1="&#xb0;" u2="d" k="93" />
<hkern u1="&#xb0;" u2="c" k="93" />
<hkern u1="&#xb0;" u2="a" k="93" />
<hkern u1="&#xb0;" u2="\" k="-55" />
<hkern u1="&#xb0;" u2="Y" k="-46" />
<hkern u1="&#xb0;" u2="W" k="-49" />
<hkern u1="&#xb0;" u2="V" k="-55" />
<hkern u1="&#xb0;" u2="A" k="175" />
<hkern u1="&#xb0;" u2="&#x2f;" k="175" />
<hkern u1="&#xb0;" u2="&#x2e;" k="94" />
<hkern u1="&#xb0;" u2="&#x2d;" k="85" />
<hkern u1="&#xb0;" u2="&#x2c;" k="94" />
<hkern u1="&#xb0;" u2="&#x26;" k="175" />
<hkern u1="&#xb7;" u2="&#x2206;" k="53" />
<hkern u1="&#xb7;" u2="&#x2026;" k="166" />
<hkern u1="&#xb7;" u2="&#x201e;" k="166" />
<hkern u1="&#xb7;" u2="&#x201d;" k="85" />
<hkern u1="&#xb7;" u2="&#x201c;" k="85" />
<hkern u1="&#xb7;" u2="&#x201a;" k="166" />
<hkern u1="&#xb7;" u2="&#x2019;" k="85" />
<hkern u1="&#xb7;" u2="&#x2018;" k="85" />
<hkern u1="&#xb7;" u2="&#x17d;" k="56" />
<hkern u1="&#xb7;" u2="&#x17b;" k="56" />
<hkern u1="&#xb7;" u2="&#x179;" k="56" />
<hkern u1="&#xb7;" u2="&#x178;" k="197" />
<hkern u1="&#xb7;" u2="&#x104;" k="53" />
<hkern u1="&#xb7;" u2="&#xdd;" k="197" />
<hkern u1="&#xb7;" u2="&#xc6;" k="53" />
<hkern u1="&#xb7;" u2="&#xc5;" k="53" />
<hkern u1="&#xb7;" u2="&#xc4;" k="53" />
<hkern u1="&#xb7;" u2="&#xc3;" k="53" />
<hkern u1="&#xb7;" u2="&#xc2;" k="53" />
<hkern u1="&#xb7;" u2="&#xc1;" k="53" />
<hkern u1="&#xb7;" u2="&#xc0;" k="53" />
<hkern u1="&#xb7;" u2="&#xba;" k="85" />
<hkern u1="&#xb7;" u2="&#xb0;" k="85" />
<hkern u1="&#xb7;" u2="&#xaa;" k="85" />
<hkern u1="&#xb7;" u2="\" k="125" />
<hkern u1="&#xb7;" u2="Z" k="56" />
<hkern u1="&#xb7;" u2="Y" k="197" />
<hkern u1="&#xb7;" u2="X" k="72" />
<hkern u1="&#xb7;" u2="V" k="125" />
<hkern u1="&#xb7;" u2="T" k="196" />
<hkern u1="&#xb7;" u2="A" k="53" />
<hkern u1="&#xb7;" u2="&#x2f;" k="53" />
<hkern u1="&#xb7;" u2="&#x2e;" k="166" />
<hkern u1="&#xb7;" u2="&#x2c;" k="166" />
<hkern u1="&#xb7;" u2="&#x2a;" k="85" />
<hkern u1="&#xb7;" u2="&#x27;" k="85" />
<hkern u1="&#xb7;" u2="&#x26;" k="53" />
<hkern u1="&#xb7;" u2="&#x22;" k="85" />
<hkern u1="&#xba;" u2="&#x2206;" k="175" />
<hkern u1="&#xba;" u2="&#x203a;" k="85" />
<hkern u1="&#xba;" u2="&#x2039;" k="85" />
<hkern u1="&#xba;" u2="&#x2026;" k="94" />
<hkern u1="&#xba;" u2="&#x2022;" k="85" />
<hkern u1="&#xba;" u2="&#x201e;" k="94" />
<hkern u1="&#xba;" u2="&#x201a;" k="94" />
<hkern u1="&#xba;" u2="&#x2014;" k="85" />
<hkern u1="&#xba;" u2="&#x2013;" k="85" />
<hkern u1="&#xba;" u2="&#x178;" k="-46" />
<hkern u1="&#xba;" u2="&#x153;" k="93" />
<hkern u1="&#xba;" u2="&#x119;" k="93" />
<hkern u1="&#xba;" u2="&#x107;" k="93" />
<hkern u1="&#xba;" u2="&#x105;" k="93" />
<hkern u1="&#xba;" u2="&#x104;" k="175" />
<hkern u1="&#xba;" u2="&#xf8;" k="93" />
<hkern u1="&#xba;" u2="&#xf6;" k="93" />
<hkern u1="&#xba;" u2="&#xf5;" k="93" />
<hkern u1="&#xba;" u2="&#xf4;" k="93" />
<hkern u1="&#xba;" u2="&#xf3;" k="93" />
<hkern u1="&#xba;" u2="&#xf2;" k="93" />
<hkern u1="&#xba;" u2="&#xf0;" k="93" />
<hkern u1="&#xba;" u2="&#xeb;" k="93" />
<hkern u1="&#xba;" u2="&#xea;" k="93" />
<hkern u1="&#xba;" u2="&#xe9;" k="93" />
<hkern u1="&#xba;" u2="&#xe8;" k="93" />
<hkern u1="&#xba;" u2="&#xe7;" k="93" />
<hkern u1="&#xba;" u2="&#xe6;" k="93" />
<hkern u1="&#xba;" u2="&#xe5;" k="93" />
<hkern u1="&#xba;" u2="&#xe4;" k="93" />
<hkern u1="&#xba;" u2="&#xe3;" k="93" />
<hkern u1="&#xba;" u2="&#xe2;" k="93" />
<hkern u1="&#xba;" u2="&#xe1;" k="93" />
<hkern u1="&#xba;" u2="&#xe0;" k="93" />
<hkern u1="&#xba;" u2="&#xdd;" k="-46" />
<hkern u1="&#xba;" u2="&#xc6;" k="175" />
<hkern u1="&#xba;" u2="&#xc5;" k="175" />
<hkern u1="&#xba;" u2="&#xc4;" k="175" />
<hkern u1="&#xba;" u2="&#xc3;" k="175" />
<hkern u1="&#xba;" u2="&#xc2;" k="175" />
<hkern u1="&#xba;" u2="&#xc1;" k="175" />
<hkern u1="&#xba;" u2="&#xc0;" k="175" />
<hkern u1="&#xba;" u2="&#xbb;" k="85" />
<hkern u1="&#xba;" u2="&#xb7;" k="85" />
<hkern u1="&#xba;" u2="&#xab;" k="85" />
<hkern u1="&#xba;" u2="q" k="93" />
<hkern u1="&#xba;" u2="o" k="93" />
<hkern u1="&#xba;" u2="e" k="93" />
<hkern u1="&#xba;" u2="d" k="93" />
<hkern u1="&#xba;" u2="c" k="93" />
<hkern u1="&#xba;" u2="a" k="93" />
<hkern u1="&#xba;" u2="\" k="-55" />
<hkern u1="&#xba;" u2="Y" k="-46" />
<hkern u1="&#xba;" u2="W" k="-49" />
<hkern u1="&#xba;" u2="V" k="-55" />
<hkern u1="&#xba;" u2="A" k="175" />
<hkern u1="&#xba;" u2="&#x2f;" k="175" />
<hkern u1="&#xba;" u2="&#x2e;" k="94" />
<hkern u1="&#xba;" u2="&#x2d;" k="85" />
<hkern u1="&#xba;" u2="&#x2c;" k="94" />
<hkern u1="&#xba;" u2="&#x26;" k="175" />
<hkern u1="&#xbb;" u2="&#x2206;" k="53" />
<hkern u1="&#xbb;" u2="&#x2026;" k="166" />
<hkern u1="&#xbb;" u2="&#x201e;" k="166" />
<hkern u1="&#xbb;" u2="&#x201d;" k="85" />
<hkern u1="&#xbb;" u2="&#x201c;" k="85" />
<hkern u1="&#xbb;" u2="&#x201a;" k="166" />
<hkern u1="&#xbb;" u2="&#x2019;" k="85" />
<hkern u1="&#xbb;" u2="&#x2018;" k="85" />
<hkern u1="&#xbb;" u2="&#x17d;" k="56" />
<hkern u1="&#xbb;" u2="&#x17b;" k="56" />
<hkern u1="&#xbb;" u2="&#x179;" k="56" />
<hkern u1="&#xbb;" u2="&#x178;" k="197" />
<hkern u1="&#xbb;" u2="&#x104;" k="53" />
<hkern u1="&#xbb;" u2="&#xdd;" k="197" />
<hkern u1="&#xbb;" u2="&#xc6;" k="53" />
<hkern u1="&#xbb;" u2="&#xc5;" k="53" />
<hkern u1="&#xbb;" u2="&#xc4;" k="53" />
<hkern u1="&#xbb;" u2="&#xc3;" k="53" />
<hkern u1="&#xbb;" u2="&#xc2;" k="53" />
<hkern u1="&#xbb;" u2="&#xc1;" k="53" />
<hkern u1="&#xbb;" u2="&#xc0;" k="53" />
<hkern u1="&#xbb;" u2="&#xba;" k="85" />
<hkern u1="&#xbb;" u2="&#xb0;" k="85" />
<hkern u1="&#xbb;" u2="&#xaa;" k="85" />
<hkern u1="&#xbb;" u2="\" k="125" />
<hkern u1="&#xbb;" u2="Z" k="56" />
<hkern u1="&#xbb;" u2="Y" k="197" />
<hkern u1="&#xbb;" u2="X" k="72" />
<hkern u1="&#xbb;" u2="V" k="125" />
<hkern u1="&#xbb;" u2="T" k="196" />
<hkern u1="&#xbb;" u2="A" k="53" />
<hkern u1="&#xbb;" u2="&#x2f;" k="53" />
<hkern u1="&#xbb;" u2="&#x2e;" k="166" />
<hkern u1="&#xbb;" u2="&#x2c;" k="166" />
<hkern u1="&#xbb;" u2="&#x2a;" k="85" />
<hkern u1="&#xbb;" u2="&#x27;" k="85" />
<hkern u1="&#xbb;" u2="&#x26;" k="53" />
<hkern u1="&#xbb;" u2="&#x22;" k="85" />
<hkern u1="&#xc0;" u2="&#x203a;" k="53" />
<hkern u1="&#xc0;" u2="&#x2039;" k="53" />
<hkern u1="&#xc0;" u2="&#x2022;" k="53" />
<hkern u1="&#xc0;" u2="&#x201d;" k="186" />
<hkern u1="&#xc0;" u2="&#x201c;" k="186" />
<hkern u1="&#xc0;" u2="&#x2019;" k="186" />
<hkern u1="&#xc0;" u2="&#x2018;" k="186" />
<hkern u1="&#xc0;" u2="&#x2014;" k="53" />
<hkern u1="&#xc0;" u2="&#x2013;" k="53" />
<hkern u1="&#xc0;" u2="&#x178;" k="156" />
<hkern u1="&#xc0;" u2="&#x152;" k="48" />
<hkern u1="&#xc0;" u2="&#x106;" k="48" />
<hkern u1="&#xc0;" u2="&#xff;" k="48" />
<hkern u1="&#xc0;" u2="&#xfd;" k="48" />
<hkern u1="&#xc0;" u2="&#xdd;" k="156" />
<hkern u1="&#xc0;" u2="&#xdc;" k="41" />
<hkern u1="&#xc0;" u2="&#xdb;" k="41" />
<hkern u1="&#xc0;" u2="&#xda;" k="41" />
<hkern u1="&#xc0;" u2="&#xd9;" k="41" />
<hkern u1="&#xc0;" u2="&#xd8;" k="48" />
<hkern u1="&#xc0;" u2="&#xd6;" k="48" />
<hkern u1="&#xc0;" u2="&#xd5;" k="48" />
<hkern u1="&#xc0;" u2="&#xd4;" k="48" />
<hkern u1="&#xc0;" u2="&#xd3;" k="48" />
<hkern u1="&#xc0;" u2="&#xd2;" k="48" />
<hkern u1="&#xc0;" u2="&#xc7;" k="48" />
<hkern u1="&#xc0;" u2="&#xbb;" k="53" />
<hkern u1="&#xc0;" u2="&#xba;" k="186" />
<hkern u1="&#xc0;" u2="&#xb7;" k="53" />
<hkern u1="&#xc0;" u2="&#xb0;" k="186" />
<hkern u1="&#xc0;" u2="&#xae;" k="48" />
<hkern u1="&#xc0;" u2="&#xab;" k="53" />
<hkern u1="&#xc0;" u2="&#xaa;" k="186" />
<hkern u1="&#xc0;" u2="&#xa9;" k="48" />
<hkern u1="&#xc0;" u2="y" k="48" />
<hkern u1="&#xc0;" u2="w" k="33" />
<hkern u1="&#xc0;" u2="v" k="48" />
<hkern u1="&#xc0;" u2="t" k="58" />
<hkern u1="&#xc0;" u2="\" k="117" />
<hkern u1="&#xc0;" u2="Y" k="156" />
<hkern u1="&#xc0;" u2="W" k="85" />
<hkern u1="&#xc0;" u2="V" k="117" />
<hkern u1="&#xc0;" u2="U" k="41" />
<hkern u1="&#xc0;" u2="T" k="135" />
<hkern u1="&#xc0;" u2="Q" k="48" />
<hkern u1="&#xc0;" u2="O" k="48" />
<hkern u1="&#xc0;" u2="J" k="-63" />
<hkern u1="&#xc0;" u2="G" k="48" />
<hkern u1="&#xc0;" u2="C" k="48" />
<hkern u1="&#xc0;" u2="&#x40;" k="48" />
<hkern u1="&#xc0;" u2="&#x2d;" k="53" />
<hkern u1="&#xc0;" u2="&#x2a;" k="186" />
<hkern u1="&#xc0;" u2="&#x27;" k="186" />
<hkern u1="&#xc0;" u2="&#x22;" k="186" />
<hkern u1="&#xc1;" u2="&#x203a;" k="53" />
<hkern u1="&#xc1;" u2="&#x2039;" k="53" />
<hkern u1="&#xc1;" u2="&#x2022;" k="53" />
<hkern u1="&#xc1;" u2="&#x201d;" k="186" />
<hkern u1="&#xc1;" u2="&#x201c;" k="186" />
<hkern u1="&#xc1;" u2="&#x2019;" k="186" />
<hkern u1="&#xc1;" u2="&#x2018;" k="186" />
<hkern u1="&#xc1;" u2="&#x2014;" k="53" />
<hkern u1="&#xc1;" u2="&#x2013;" k="53" />
<hkern u1="&#xc1;" u2="&#x178;" k="156" />
<hkern u1="&#xc1;" u2="&#x152;" k="48" />
<hkern u1="&#xc1;" u2="&#x106;" k="48" />
<hkern u1="&#xc1;" u2="&#xff;" k="48" />
<hkern u1="&#xc1;" u2="&#xfd;" k="48" />
<hkern u1="&#xc1;" u2="&#xdd;" k="156" />
<hkern u1="&#xc1;" u2="&#xdc;" k="41" />
<hkern u1="&#xc1;" u2="&#xdb;" k="41" />
<hkern u1="&#xc1;" u2="&#xda;" k="41" />
<hkern u1="&#xc1;" u2="&#xd9;" k="41" />
<hkern u1="&#xc1;" u2="&#xd8;" k="48" />
<hkern u1="&#xc1;" u2="&#xd6;" k="48" />
<hkern u1="&#xc1;" u2="&#xd5;" k="48" />
<hkern u1="&#xc1;" u2="&#xd4;" k="48" />
<hkern u1="&#xc1;" u2="&#xd3;" k="48" />
<hkern u1="&#xc1;" u2="&#xd2;" k="48" />
<hkern u1="&#xc1;" u2="&#xc7;" k="48" />
<hkern u1="&#xc1;" u2="&#xbb;" k="53" />
<hkern u1="&#xc1;" u2="&#xba;" k="186" />
<hkern u1="&#xc1;" u2="&#xb7;" k="53" />
<hkern u1="&#xc1;" u2="&#xb0;" k="186" />
<hkern u1="&#xc1;" u2="&#xae;" k="48" />
<hkern u1="&#xc1;" u2="&#xab;" k="53" />
<hkern u1="&#xc1;" u2="&#xaa;" k="186" />
<hkern u1="&#xc1;" u2="&#xa9;" k="48" />
<hkern u1="&#xc1;" u2="y" k="48" />
<hkern u1="&#xc1;" u2="w" k="33" />
<hkern u1="&#xc1;" u2="v" k="48" />
<hkern u1="&#xc1;" u2="t" k="58" />
<hkern u1="&#xc1;" u2="\" k="117" />
<hkern u1="&#xc1;" u2="Y" k="156" />
<hkern u1="&#xc1;" u2="W" k="85" />
<hkern u1="&#xc1;" u2="V" k="117" />
<hkern u1="&#xc1;" u2="U" k="41" />
<hkern u1="&#xc1;" u2="T" k="135" />
<hkern u1="&#xc1;" u2="Q" k="48" />
<hkern u1="&#xc1;" u2="O" k="48" />
<hkern u1="&#xc1;" u2="J" k="-63" />
<hkern u1="&#xc1;" u2="G" k="48" />
<hkern u1="&#xc1;" u2="C" k="48" />
<hkern u1="&#xc1;" u2="&#x40;" k="48" />
<hkern u1="&#xc1;" u2="&#x2d;" k="53" />
<hkern u1="&#xc1;" u2="&#x2a;" k="186" />
<hkern u1="&#xc1;" u2="&#x27;" k="186" />
<hkern u1="&#xc1;" u2="&#x22;" k="186" />
<hkern u1="&#xc2;" u2="&#x203a;" k="53" />
<hkern u1="&#xc2;" u2="&#x2039;" k="53" />
<hkern u1="&#xc2;" u2="&#x2022;" k="53" />
<hkern u1="&#xc2;" u2="&#x201d;" k="186" />
<hkern u1="&#xc2;" u2="&#x201c;" k="186" />
<hkern u1="&#xc2;" u2="&#x2019;" k="186" />
<hkern u1="&#xc2;" u2="&#x2018;" k="186" />
<hkern u1="&#xc2;" u2="&#x2014;" k="53" />
<hkern u1="&#xc2;" u2="&#x2013;" k="53" />
<hkern u1="&#xc2;" u2="&#x178;" k="156" />
<hkern u1="&#xc2;" u2="&#x152;" k="48" />
<hkern u1="&#xc2;" u2="&#x106;" k="48" />
<hkern u1="&#xc2;" u2="&#xff;" k="48" />
<hkern u1="&#xc2;" u2="&#xfd;" k="48" />
<hkern u1="&#xc2;" u2="&#xdd;" k="156" />
<hkern u1="&#xc2;" u2="&#xdc;" k="41" />
<hkern u1="&#xc2;" u2="&#xdb;" k="41" />
<hkern u1="&#xc2;" u2="&#xda;" k="41" />
<hkern u1="&#xc2;" u2="&#xd9;" k="41" />
<hkern u1="&#xc2;" u2="&#xd8;" k="48" />
<hkern u1="&#xc2;" u2="&#xd6;" k="48" />
<hkern u1="&#xc2;" u2="&#xd5;" k="48" />
<hkern u1="&#xc2;" u2="&#xd4;" k="48" />
<hkern u1="&#xc2;" u2="&#xd3;" k="48" />
<hkern u1="&#xc2;" u2="&#xd2;" k="48" />
<hkern u1="&#xc2;" u2="&#xc7;" k="48" />
<hkern u1="&#xc2;" u2="&#xbb;" k="53" />
<hkern u1="&#xc2;" u2="&#xba;" k="186" />
<hkern u1="&#xc2;" u2="&#xb7;" k="53" />
<hkern u1="&#xc2;" u2="&#xb0;" k="186" />
<hkern u1="&#xc2;" u2="&#xae;" k="48" />
<hkern u1="&#xc2;" u2="&#xab;" k="53" />
<hkern u1="&#xc2;" u2="&#xaa;" k="186" />
<hkern u1="&#xc2;" u2="&#xa9;" k="48" />
<hkern u1="&#xc2;" u2="y" k="48" />
<hkern u1="&#xc2;" u2="w" k="33" />
<hkern u1="&#xc2;" u2="v" k="48" />
<hkern u1="&#xc2;" u2="t" k="58" />
<hkern u1="&#xc2;" u2="\" k="117" />
<hkern u1="&#xc2;" u2="Y" k="156" />
<hkern u1="&#xc2;" u2="W" k="85" />
<hkern u1="&#xc2;" u2="V" k="117" />
<hkern u1="&#xc2;" u2="U" k="41" />
<hkern u1="&#xc2;" u2="T" k="135" />
<hkern u1="&#xc2;" u2="Q" k="48" />
<hkern u1="&#xc2;" u2="O" k="48" />
<hkern u1="&#xc2;" u2="J" k="-63" />
<hkern u1="&#xc2;" u2="G" k="48" />
<hkern u1="&#xc2;" u2="C" k="48" />
<hkern u1="&#xc2;" u2="&#x40;" k="48" />
<hkern u1="&#xc2;" u2="&#x2d;" k="53" />
<hkern u1="&#xc2;" u2="&#x2a;" k="186" />
<hkern u1="&#xc2;" u2="&#x27;" k="186" />
<hkern u1="&#xc2;" u2="&#x22;" k="186" />
<hkern u1="&#xc3;" u2="&#x203a;" k="53" />
<hkern u1="&#xc3;" u2="&#x2039;" k="53" />
<hkern u1="&#xc3;" u2="&#x2022;" k="53" />
<hkern u1="&#xc3;" u2="&#x201d;" k="186" />
<hkern u1="&#xc3;" u2="&#x201c;" k="186" />
<hkern u1="&#xc3;" u2="&#x2019;" k="186" />
<hkern u1="&#xc3;" u2="&#x2018;" k="186" />
<hkern u1="&#xc3;" u2="&#x2014;" k="53" />
<hkern u1="&#xc3;" u2="&#x2013;" k="53" />
<hkern u1="&#xc3;" u2="&#x178;" k="156" />
<hkern u1="&#xc3;" u2="&#x152;" k="48" />
<hkern u1="&#xc3;" u2="&#x106;" k="48" />
<hkern u1="&#xc3;" u2="&#xff;" k="48" />
<hkern u1="&#xc3;" u2="&#xfd;" k="48" />
<hkern u1="&#xc3;" u2="&#xdd;" k="156" />
<hkern u1="&#xc3;" u2="&#xdc;" k="41" />
<hkern u1="&#xc3;" u2="&#xdb;" k="41" />
<hkern u1="&#xc3;" u2="&#xda;" k="41" />
<hkern u1="&#xc3;" u2="&#xd9;" k="41" />
<hkern u1="&#xc3;" u2="&#xd8;" k="48" />
<hkern u1="&#xc3;" u2="&#xd6;" k="48" />
<hkern u1="&#xc3;" u2="&#xd5;" k="48" />
<hkern u1="&#xc3;" u2="&#xd4;" k="48" />
<hkern u1="&#xc3;" u2="&#xd3;" k="48" />
<hkern u1="&#xc3;" u2="&#xd2;" k="48" />
<hkern u1="&#xc3;" u2="&#xc7;" k="48" />
<hkern u1="&#xc3;" u2="&#xbb;" k="53" />
<hkern u1="&#xc3;" u2="&#xba;" k="186" />
<hkern u1="&#xc3;" u2="&#xb7;" k="53" />
<hkern u1="&#xc3;" u2="&#xb0;" k="186" />
<hkern u1="&#xc3;" u2="&#xae;" k="48" />
<hkern u1="&#xc3;" u2="&#xab;" k="53" />
<hkern u1="&#xc3;" u2="&#xaa;" k="186" />
<hkern u1="&#xc3;" u2="&#xa9;" k="48" />
<hkern u1="&#xc3;" u2="y" k="48" />
<hkern u1="&#xc3;" u2="w" k="33" />
<hkern u1="&#xc3;" u2="v" k="48" />
<hkern u1="&#xc3;" u2="t" k="58" />
<hkern u1="&#xc3;" u2="\" k="117" />
<hkern u1="&#xc3;" u2="Y" k="156" />
<hkern u1="&#xc3;" u2="W" k="85" />
<hkern u1="&#xc3;" u2="V" k="117" />
<hkern u1="&#xc3;" u2="U" k="41" />
<hkern u1="&#xc3;" u2="T" k="135" />
<hkern u1="&#xc3;" u2="Q" k="48" />
<hkern u1="&#xc3;" u2="O" k="48" />
<hkern u1="&#xc3;" u2="J" k="-63" />
<hkern u1="&#xc3;" u2="G" k="48" />
<hkern u1="&#xc3;" u2="C" k="48" />
<hkern u1="&#xc3;" u2="&#x40;" k="48" />
<hkern u1="&#xc3;" u2="&#x2d;" k="53" />
<hkern u1="&#xc3;" u2="&#x2a;" k="186" />
<hkern u1="&#xc3;" u2="&#x27;" k="186" />
<hkern u1="&#xc3;" u2="&#x22;" k="186" />
<hkern u1="&#xc4;" u2="&#x203a;" k="53" />
<hkern u1="&#xc4;" u2="&#x2039;" k="53" />
<hkern u1="&#xc4;" u2="&#x2022;" k="53" />
<hkern u1="&#xc4;" u2="&#x201d;" k="186" />
<hkern u1="&#xc4;" u2="&#x201c;" k="186" />
<hkern u1="&#xc4;" u2="&#x2019;" k="186" />
<hkern u1="&#xc4;" u2="&#x2018;" k="186" />
<hkern u1="&#xc4;" u2="&#x2014;" k="53" />
<hkern u1="&#xc4;" u2="&#x2013;" k="53" />
<hkern u1="&#xc4;" u2="&#x178;" k="156" />
<hkern u1="&#xc4;" u2="&#x152;" k="48" />
<hkern u1="&#xc4;" u2="&#x106;" k="48" />
<hkern u1="&#xc4;" u2="&#xff;" k="48" />
<hkern u1="&#xc4;" u2="&#xfd;" k="48" />
<hkern u1="&#xc4;" u2="&#xdd;" k="156" />
<hkern u1="&#xc4;" u2="&#xdc;" k="41" />
<hkern u1="&#xc4;" u2="&#xdb;" k="41" />
<hkern u1="&#xc4;" u2="&#xda;" k="41" />
<hkern u1="&#xc4;" u2="&#xd9;" k="41" />
<hkern u1="&#xc4;" u2="&#xd8;" k="48" />
<hkern u1="&#xc4;" u2="&#xd6;" k="48" />
<hkern u1="&#xc4;" u2="&#xd5;" k="48" />
<hkern u1="&#xc4;" u2="&#xd4;" k="48" />
<hkern u1="&#xc4;" u2="&#xd3;" k="48" />
<hkern u1="&#xc4;" u2="&#xd2;" k="48" />
<hkern u1="&#xc4;" u2="&#xc7;" k="48" />
<hkern u1="&#xc4;" u2="&#xbb;" k="53" />
<hkern u1="&#xc4;" u2="&#xba;" k="186" />
<hkern u1="&#xc4;" u2="&#xb7;" k="53" />
<hkern u1="&#xc4;" u2="&#xb0;" k="186" />
<hkern u1="&#xc4;" u2="&#xae;" k="48" />
<hkern u1="&#xc4;" u2="&#xab;" k="53" />
<hkern u1="&#xc4;" u2="&#xaa;" k="186" />
<hkern u1="&#xc4;" u2="&#xa9;" k="48" />
<hkern u1="&#xc4;" u2="y" k="48" />
<hkern u1="&#xc4;" u2="w" k="33" />
<hkern u1="&#xc4;" u2="v" k="48" />
<hkern u1="&#xc4;" u2="t" k="58" />
<hkern u1="&#xc4;" u2="\" k="117" />
<hkern u1="&#xc4;" u2="Y" k="156" />
<hkern u1="&#xc4;" u2="W" k="85" />
<hkern u1="&#xc4;" u2="V" k="117" />
<hkern u1="&#xc4;" u2="U" k="41" />
<hkern u1="&#xc4;" u2="T" k="135" />
<hkern u1="&#xc4;" u2="Q" k="48" />
<hkern u1="&#xc4;" u2="O" k="48" />
<hkern u1="&#xc4;" u2="J" k="-63" />
<hkern u1="&#xc4;" u2="G" k="48" />
<hkern u1="&#xc4;" u2="C" k="48" />
<hkern u1="&#xc4;" u2="&#x40;" k="48" />
<hkern u1="&#xc4;" u2="&#x2d;" k="53" />
<hkern u1="&#xc4;" u2="&#x2a;" k="186" />
<hkern u1="&#xc4;" u2="&#x27;" k="186" />
<hkern u1="&#xc4;" u2="&#x22;" k="186" />
<hkern u1="&#xc5;" u2="&#x203a;" k="53" />
<hkern u1="&#xc5;" u2="&#x2039;" k="53" />
<hkern u1="&#xc5;" u2="&#x2022;" k="53" />
<hkern u1="&#xc5;" u2="&#x201d;" k="186" />
<hkern u1="&#xc5;" u2="&#x201c;" k="186" />
<hkern u1="&#xc5;" u2="&#x2019;" k="186" />
<hkern u1="&#xc5;" u2="&#x2018;" k="186" />
<hkern u1="&#xc5;" u2="&#x2014;" k="53" />
<hkern u1="&#xc5;" u2="&#x2013;" k="53" />
<hkern u1="&#xc5;" u2="&#x178;" k="156" />
<hkern u1="&#xc5;" u2="&#x152;" k="48" />
<hkern u1="&#xc5;" u2="&#x106;" k="48" />
<hkern u1="&#xc5;" u2="&#xff;" k="48" />
<hkern u1="&#xc5;" u2="&#xfd;" k="48" />
<hkern u1="&#xc5;" u2="&#xdd;" k="156" />
<hkern u1="&#xc5;" u2="&#xdc;" k="41" />
<hkern u1="&#xc5;" u2="&#xdb;" k="41" />
<hkern u1="&#xc5;" u2="&#xda;" k="41" />
<hkern u1="&#xc5;" u2="&#xd9;" k="41" />
<hkern u1="&#xc5;" u2="&#xd8;" k="48" />
<hkern u1="&#xc5;" u2="&#xd6;" k="48" />
<hkern u1="&#xc5;" u2="&#xd5;" k="48" />
<hkern u1="&#xc5;" u2="&#xd4;" k="48" />
<hkern u1="&#xc5;" u2="&#xd3;" k="48" />
<hkern u1="&#xc5;" u2="&#xd2;" k="48" />
<hkern u1="&#xc5;" u2="&#xc7;" k="48" />
<hkern u1="&#xc5;" u2="&#xbb;" k="53" />
<hkern u1="&#xc5;" u2="&#xba;" k="186" />
<hkern u1="&#xc5;" u2="&#xb7;" k="53" />
<hkern u1="&#xc5;" u2="&#xb0;" k="186" />
<hkern u1="&#xc5;" u2="&#xae;" k="48" />
<hkern u1="&#xc5;" u2="&#xab;" k="53" />
<hkern u1="&#xc5;" u2="&#xaa;" k="186" />
<hkern u1="&#xc5;" u2="&#xa9;" k="48" />
<hkern u1="&#xc5;" u2="y" k="48" />
<hkern u1="&#xc5;" u2="w" k="33" />
<hkern u1="&#xc5;" u2="v" k="48" />
<hkern u1="&#xc5;" u2="t" k="58" />
<hkern u1="&#xc5;" u2="\" k="117" />
<hkern u1="&#xc5;" u2="Y" k="156" />
<hkern u1="&#xc5;" u2="W" k="85" />
<hkern u1="&#xc5;" u2="V" k="117" />
<hkern u1="&#xc5;" u2="U" k="41" />
<hkern u1="&#xc5;" u2="T" k="135" />
<hkern u1="&#xc5;" u2="Q" k="48" />
<hkern u1="&#xc5;" u2="O" k="48" />
<hkern u1="&#xc5;" u2="J" k="-63" />
<hkern u1="&#xc5;" u2="G" k="48" />
<hkern u1="&#xc5;" u2="C" k="48" />
<hkern u1="&#xc5;" u2="&#x40;" k="48" />
<hkern u1="&#xc5;" u2="&#x2d;" k="53" />
<hkern u1="&#xc5;" u2="&#x2a;" k="186" />
<hkern u1="&#xc5;" u2="&#x27;" k="186" />
<hkern u1="&#xc5;" u2="&#x22;" k="186" />
<hkern u1="&#xc7;" u2="&#x203a;" k="131" />
<hkern u1="&#xc7;" u2="&#x2039;" k="131" />
<hkern u1="&#xc7;" u2="&#x2022;" k="131" />
<hkern u1="&#xc7;" u2="&#x2014;" k="131" />
<hkern u1="&#xc7;" u2="&#x2013;" k="131" />
<hkern u1="&#xc7;" u2="&#xbb;" k="131" />
<hkern u1="&#xc7;" u2="&#xb7;" k="131" />
<hkern u1="&#xc7;" u2="&#xab;" k="131" />
<hkern u1="&#xc7;" u2="&#x2d;" k="131" />
<hkern u1="&#xd0;" u2="&#x2206;" k="37" />
<hkern u1="&#xd0;" u2="&#x201d;" k="56" />
<hkern u1="&#xd0;" u2="&#x201c;" k="56" />
<hkern u1="&#xd0;" u2="&#x2019;" k="56" />
<hkern u1="&#xd0;" u2="&#x2018;" k="56" />
<hkern u1="&#xd0;" u2="&#x17d;" k="71" />
<hkern u1="&#xd0;" u2="&#x17b;" k="71" />
<hkern u1="&#xd0;" u2="&#x179;" k="71" />
<hkern u1="&#xd0;" u2="&#x178;" k="82" />
<hkern u1="&#xd0;" u2="&#x104;" k="37" />
<hkern u1="&#xd0;" u2="&#xdd;" k="82" />
<hkern u1="&#xd0;" u2="&#xc6;" k="37" />
<hkern u1="&#xd0;" u2="&#xc5;" k="37" />
<hkern u1="&#xd0;" u2="&#xc4;" k="37" />
<hkern u1="&#xd0;" u2="&#xc3;" k="37" />
<hkern u1="&#xd0;" u2="&#xc2;" k="37" />
<hkern u1="&#xd0;" u2="&#xc1;" k="37" />
<hkern u1="&#xd0;" u2="&#xc0;" k="37" />
<hkern u1="&#xd0;" u2="&#xba;" k="56" />
<hkern u1="&#xd0;" u2="&#xb0;" k="56" />
<hkern u1="&#xd0;" u2="&#xaa;" k="56" />
<hkern u1="&#xd0;" u2="&#x7d;" k="41" />
<hkern u1="&#xd0;" u2="]" k="41" />
<hkern u1="&#xd0;" u2="\" k="52" />
<hkern u1="&#xd0;" u2="Z" k="71" />
<hkern u1="&#xd0;" u2="Y" k="82" />
<hkern u1="&#xd0;" u2="X" k="70" />
<hkern u1="&#xd0;" u2="V" k="52" />
<hkern u1="&#xd0;" u2="T" k="99" />
<hkern u1="&#xd0;" u2="A" k="37" />
<hkern u1="&#xd0;" u2="&#x2f;" k="37" />
<hkern u1="&#xd0;" u2="&#x2a;" k="56" />
<hkern u1="&#xd0;" u2="&#x29;" k="41" />
<hkern u1="&#xd0;" u2="&#x27;" k="56" />
<hkern u1="&#xd0;" u2="&#x26;" k="37" />
<hkern u1="&#xd0;" u2="&#x22;" k="56" />
<hkern u1="&#xd2;" u2="&#x2206;" k="37" />
<hkern u1="&#xd2;" u2="&#x201d;" k="56" />
<hkern u1="&#xd2;" u2="&#x201c;" k="56" />
<hkern u1="&#xd2;" u2="&#x2019;" k="56" />
<hkern u1="&#xd2;" u2="&#x2018;" k="56" />
<hkern u1="&#xd2;" u2="&#x17d;" k="71" />
<hkern u1="&#xd2;" u2="&#x17b;" k="71" />
<hkern u1="&#xd2;" u2="&#x179;" k="71" />
<hkern u1="&#xd2;" u2="&#x178;" k="82" />
<hkern u1="&#xd2;" u2="&#x104;" k="37" />
<hkern u1="&#xd2;" u2="&#xdd;" k="82" />
<hkern u1="&#xd2;" u2="&#xc6;" k="37" />
<hkern u1="&#xd2;" u2="&#xc5;" k="37" />
<hkern u1="&#xd2;" u2="&#xc4;" k="37" />
<hkern u1="&#xd2;" u2="&#xc3;" k="37" />
<hkern u1="&#xd2;" u2="&#xc2;" k="37" />
<hkern u1="&#xd2;" u2="&#xc1;" k="37" />
<hkern u1="&#xd2;" u2="&#xc0;" k="37" />
<hkern u1="&#xd2;" u2="&#xba;" k="56" />
<hkern u1="&#xd2;" u2="&#xb0;" k="56" />
<hkern u1="&#xd2;" u2="&#xaa;" k="56" />
<hkern u1="&#xd2;" u2="&#x7d;" k="41" />
<hkern u1="&#xd2;" u2="]" k="41" />
<hkern u1="&#xd2;" u2="\" k="52" />
<hkern u1="&#xd2;" u2="Z" k="71" />
<hkern u1="&#xd2;" u2="Y" k="82" />
<hkern u1="&#xd2;" u2="X" k="70" />
<hkern u1="&#xd2;" u2="V" k="52" />
<hkern u1="&#xd2;" u2="T" k="99" />
<hkern u1="&#xd2;" u2="A" k="37" />
<hkern u1="&#xd2;" u2="&#x2f;" k="37" />
<hkern u1="&#xd2;" u2="&#x2a;" k="56" />
<hkern u1="&#xd2;" u2="&#x29;" k="41" />
<hkern u1="&#xd2;" u2="&#x27;" k="56" />
<hkern u1="&#xd2;" u2="&#x26;" k="37" />
<hkern u1="&#xd2;" u2="&#x22;" k="56" />
<hkern u1="&#xd3;" u2="&#x2206;" k="37" />
<hkern u1="&#xd3;" u2="&#x201d;" k="56" />
<hkern u1="&#xd3;" u2="&#x201c;" k="56" />
<hkern u1="&#xd3;" u2="&#x2019;" k="56" />
<hkern u1="&#xd3;" u2="&#x2018;" k="56" />
<hkern u1="&#xd3;" u2="&#x17d;" k="71" />
<hkern u1="&#xd3;" u2="&#x17b;" k="71" />
<hkern u1="&#xd3;" u2="&#x179;" k="71" />
<hkern u1="&#xd3;" u2="&#x178;" k="82" />
<hkern u1="&#xd3;" u2="&#x104;" k="37" />
<hkern u1="&#xd3;" u2="&#xdd;" k="82" />
<hkern u1="&#xd3;" u2="&#xc6;" k="37" />
<hkern u1="&#xd3;" u2="&#xc5;" k="37" />
<hkern u1="&#xd3;" u2="&#xc4;" k="37" />
<hkern u1="&#xd3;" u2="&#xc3;" k="37" />
<hkern u1="&#xd3;" u2="&#xc2;" k="37" />
<hkern u1="&#xd3;" u2="&#xc1;" k="37" />
<hkern u1="&#xd3;" u2="&#xc0;" k="37" />
<hkern u1="&#xd3;" u2="&#xba;" k="56" />
<hkern u1="&#xd3;" u2="&#xb0;" k="56" />
<hkern u1="&#xd3;" u2="&#xaa;" k="56" />
<hkern u1="&#xd3;" u2="&#x7d;" k="41" />
<hkern u1="&#xd3;" u2="]" k="41" />
<hkern u1="&#xd3;" u2="\" k="52" />
<hkern u1="&#xd3;" u2="Z" k="71" />
<hkern u1="&#xd3;" u2="Y" k="82" />
<hkern u1="&#xd3;" u2="X" k="70" />
<hkern u1="&#xd3;" u2="V" k="52" />
<hkern u1="&#xd3;" u2="T" k="99" />
<hkern u1="&#xd3;" u2="A" k="37" />
<hkern u1="&#xd3;" u2="&#x2f;" k="37" />
<hkern u1="&#xd3;" u2="&#x2a;" k="56" />
<hkern u1="&#xd3;" u2="&#x29;" k="41" />
<hkern u1="&#xd3;" u2="&#x27;" k="56" />
<hkern u1="&#xd3;" u2="&#x26;" k="37" />
<hkern u1="&#xd3;" u2="&#x22;" k="56" />
<hkern u1="&#xd4;" u2="&#x2206;" k="37" />
<hkern u1="&#xd4;" u2="&#x201d;" k="56" />
<hkern u1="&#xd4;" u2="&#x201c;" k="56" />
<hkern u1="&#xd4;" u2="&#x2019;" k="56" />
<hkern u1="&#xd4;" u2="&#x2018;" k="56" />
<hkern u1="&#xd4;" u2="&#x17d;" k="71" />
<hkern u1="&#xd4;" u2="&#x17b;" k="71" />
<hkern u1="&#xd4;" u2="&#x179;" k="71" />
<hkern u1="&#xd4;" u2="&#x178;" k="82" />
<hkern u1="&#xd4;" u2="&#x104;" k="37" />
<hkern u1="&#xd4;" u2="&#xdd;" k="82" />
<hkern u1="&#xd4;" u2="&#xc6;" k="37" />
<hkern u1="&#xd4;" u2="&#xc5;" k="37" />
<hkern u1="&#xd4;" u2="&#xc4;" k="37" />
<hkern u1="&#xd4;" u2="&#xc3;" k="37" />
<hkern u1="&#xd4;" u2="&#xc2;" k="37" />
<hkern u1="&#xd4;" u2="&#xc1;" k="37" />
<hkern u1="&#xd4;" u2="&#xc0;" k="37" />
<hkern u1="&#xd4;" u2="&#xba;" k="56" />
<hkern u1="&#xd4;" u2="&#xb0;" k="56" />
<hkern u1="&#xd4;" u2="&#xaa;" k="56" />
<hkern u1="&#xd4;" u2="&#x7d;" k="41" />
<hkern u1="&#xd4;" u2="]" k="41" />
<hkern u1="&#xd4;" u2="\" k="52" />
<hkern u1="&#xd4;" u2="Z" k="71" />
<hkern u1="&#xd4;" u2="Y" k="82" />
<hkern u1="&#xd4;" u2="X" k="70" />
<hkern u1="&#xd4;" u2="V" k="52" />
<hkern u1="&#xd4;" u2="T" k="99" />
<hkern u1="&#xd4;" u2="A" k="37" />
<hkern u1="&#xd4;" u2="&#x2f;" k="37" />
<hkern u1="&#xd4;" u2="&#x2a;" k="56" />
<hkern u1="&#xd4;" u2="&#x29;" k="41" />
<hkern u1="&#xd4;" u2="&#x27;" k="56" />
<hkern u1="&#xd4;" u2="&#x26;" k="37" />
<hkern u1="&#xd4;" u2="&#x22;" k="56" />
<hkern u1="&#xd5;" u2="&#x2206;" k="37" />
<hkern u1="&#xd5;" u2="&#x201d;" k="56" />
<hkern u1="&#xd5;" u2="&#x201c;" k="56" />
<hkern u1="&#xd5;" u2="&#x2019;" k="56" />
<hkern u1="&#xd5;" u2="&#x2018;" k="56" />
<hkern u1="&#xd5;" u2="&#x17d;" k="71" />
<hkern u1="&#xd5;" u2="&#x17b;" k="71" />
<hkern u1="&#xd5;" u2="&#x179;" k="71" />
<hkern u1="&#xd5;" u2="&#x178;" k="82" />
<hkern u1="&#xd5;" u2="&#x104;" k="37" />
<hkern u1="&#xd5;" u2="&#xdd;" k="82" />
<hkern u1="&#xd5;" u2="&#xc6;" k="37" />
<hkern u1="&#xd5;" u2="&#xc5;" k="37" />
<hkern u1="&#xd5;" u2="&#xc4;" k="37" />
<hkern u1="&#xd5;" u2="&#xc3;" k="37" />
<hkern u1="&#xd5;" u2="&#xc2;" k="37" />
<hkern u1="&#xd5;" u2="&#xc1;" k="37" />
<hkern u1="&#xd5;" u2="&#xc0;" k="37" />
<hkern u1="&#xd5;" u2="&#xba;" k="56" />
<hkern u1="&#xd5;" u2="&#xb0;" k="56" />
<hkern u1="&#xd5;" u2="&#xaa;" k="56" />
<hkern u1="&#xd5;" u2="&#x7d;" k="41" />
<hkern u1="&#xd5;" u2="]" k="41" />
<hkern u1="&#xd5;" u2="\" k="52" />
<hkern u1="&#xd5;" u2="Z" k="71" />
<hkern u1="&#xd5;" u2="Y" k="82" />
<hkern u1="&#xd5;" u2="X" k="70" />
<hkern u1="&#xd5;" u2="V" k="52" />
<hkern u1="&#xd5;" u2="T" k="99" />
<hkern u1="&#xd5;" u2="A" k="37" />
<hkern u1="&#xd5;" u2="&#x2f;" k="37" />
<hkern u1="&#xd5;" u2="&#x2a;" k="56" />
<hkern u1="&#xd5;" u2="&#x29;" k="41" />
<hkern u1="&#xd5;" u2="&#x27;" k="56" />
<hkern u1="&#xd5;" u2="&#x26;" k="37" />
<hkern u1="&#xd5;" u2="&#x22;" k="56" />
<hkern u1="&#xd6;" u2="&#x2206;" k="37" />
<hkern u1="&#xd6;" u2="&#x201d;" k="56" />
<hkern u1="&#xd6;" u2="&#x201c;" k="56" />
<hkern u1="&#xd6;" u2="&#x2019;" k="56" />
<hkern u1="&#xd6;" u2="&#x2018;" k="56" />
<hkern u1="&#xd6;" u2="&#x17d;" k="71" />
<hkern u1="&#xd6;" u2="&#x17b;" k="71" />
<hkern u1="&#xd6;" u2="&#x179;" k="71" />
<hkern u1="&#xd6;" u2="&#x178;" k="82" />
<hkern u1="&#xd6;" u2="&#x104;" k="37" />
<hkern u1="&#xd6;" u2="&#xdd;" k="82" />
<hkern u1="&#xd6;" u2="&#xc6;" k="37" />
<hkern u1="&#xd6;" u2="&#xc5;" k="37" />
<hkern u1="&#xd6;" u2="&#xc4;" k="37" />
<hkern u1="&#xd6;" u2="&#xc3;" k="37" />
<hkern u1="&#xd6;" u2="&#xc2;" k="37" />
<hkern u1="&#xd6;" u2="&#xc1;" k="37" />
<hkern u1="&#xd6;" u2="&#xc0;" k="37" />
<hkern u1="&#xd6;" u2="&#xba;" k="56" />
<hkern u1="&#xd6;" u2="&#xb0;" k="56" />
<hkern u1="&#xd6;" u2="&#xaa;" k="56" />
<hkern u1="&#xd6;" u2="&#x7d;" k="41" />
<hkern u1="&#xd6;" u2="]" k="41" />
<hkern u1="&#xd6;" u2="\" k="52" />
<hkern u1="&#xd6;" u2="Z" k="71" />
<hkern u1="&#xd6;" u2="Y" k="82" />
<hkern u1="&#xd6;" u2="X" k="70" />
<hkern u1="&#xd6;" u2="V" k="52" />
<hkern u1="&#xd6;" u2="T" k="99" />
<hkern u1="&#xd6;" u2="A" k="37" />
<hkern u1="&#xd6;" u2="&#x2f;" k="37" />
<hkern u1="&#xd6;" u2="&#x2a;" k="56" />
<hkern u1="&#xd6;" u2="&#x29;" k="41" />
<hkern u1="&#xd6;" u2="&#x27;" k="56" />
<hkern u1="&#xd6;" u2="&#x26;" k="37" />
<hkern u1="&#xd6;" u2="&#x22;" k="56" />
<hkern u1="&#xd9;" u2="&#x2206;" k="41" />
<hkern u1="&#xd9;" u2="&#x104;" k="41" />
<hkern u1="&#xd9;" u2="&#xc6;" k="41" />
<hkern u1="&#xd9;" u2="&#xc5;" k="41" />
<hkern u1="&#xd9;" u2="&#xc4;" k="41" />
<hkern u1="&#xd9;" u2="&#xc3;" k="41" />
<hkern u1="&#xd9;" u2="&#xc2;" k="41" />
<hkern u1="&#xd9;" u2="&#xc1;" k="41" />
<hkern u1="&#xd9;" u2="&#xc0;" k="41" />
<hkern u1="&#xd9;" u2="A" k="41" />
<hkern u1="&#xd9;" u2="&#x2f;" k="41" />
<hkern u1="&#xd9;" u2="&#x26;" k="41" />
<hkern u1="&#xda;" u2="&#x2206;" k="41" />
<hkern u1="&#xda;" u2="&#x104;" k="41" />
<hkern u1="&#xda;" u2="&#xc6;" k="41" />
<hkern u1="&#xda;" u2="&#xc5;" k="41" />
<hkern u1="&#xda;" u2="&#xc4;" k="41" />
<hkern u1="&#xda;" u2="&#xc3;" k="41" />
<hkern u1="&#xda;" u2="&#xc2;" k="41" />
<hkern u1="&#xda;" u2="&#xc1;" k="41" />
<hkern u1="&#xda;" u2="&#xc0;" k="41" />
<hkern u1="&#xda;" u2="A" k="41" />
<hkern u1="&#xda;" u2="&#x2f;" k="41" />
<hkern u1="&#xda;" u2="&#x26;" k="41" />
<hkern u1="&#xdb;" u2="&#x2206;" k="41" />
<hkern u1="&#xdb;" u2="&#x104;" k="41" />
<hkern u1="&#xdb;" u2="&#xc6;" k="41" />
<hkern u1="&#xdb;" u2="&#xc5;" k="41" />
<hkern u1="&#xdb;" u2="&#xc4;" k="41" />
<hkern u1="&#xdb;" u2="&#xc3;" k="41" />
<hkern u1="&#xdb;" u2="&#xc2;" k="41" />
<hkern u1="&#xdb;" u2="&#xc1;" k="41" />
<hkern u1="&#xdb;" u2="&#xc0;" k="41" />
<hkern u1="&#xdb;" u2="A" k="41" />
<hkern u1="&#xdb;" u2="&#x2f;" k="41" />
<hkern u1="&#xdb;" u2="&#x26;" k="41" />
<hkern u1="&#xdc;" u2="&#x2206;" k="41" />
<hkern u1="&#xdc;" u2="&#x104;" k="41" />
<hkern u1="&#xdc;" u2="&#xc6;" k="41" />
<hkern u1="&#xdc;" u2="&#xc5;" k="41" />
<hkern u1="&#xdc;" u2="&#xc4;" k="41" />
<hkern u1="&#xdc;" u2="&#xc3;" k="41" />
<hkern u1="&#xdc;" u2="&#xc2;" k="41" />
<hkern u1="&#xdc;" u2="&#xc1;" k="41" />
<hkern u1="&#xdc;" u2="&#xc0;" k="41" />
<hkern u1="&#xdc;" u2="A" k="41" />
<hkern u1="&#xdc;" u2="&#x2f;" k="41" />
<hkern u1="&#xdc;" u2="&#x26;" k="41" />
<hkern u1="&#xdd;" u2="&#x2206;" k="145" />
<hkern u1="&#xdd;" u2="&#x203a;" k="186" />
<hkern u1="&#xdd;" u2="&#x2039;" k="186" />
<hkern u1="&#xdd;" u2="&#x2026;" k="222" />
<hkern u1="&#xdd;" u2="&#x2022;" k="186" />
<hkern u1="&#xdd;" u2="&#x201e;" k="222" />
<hkern u1="&#xdd;" u2="&#x201d;" k="-56" />
<hkern u1="&#xdd;" u2="&#x201c;" k="-56" />
<hkern u1="&#xdd;" u2="&#x201a;" k="222" />
<hkern u1="&#xdd;" u2="&#x2019;" k="-56" />
<hkern u1="&#xdd;" u2="&#x2018;" k="-56" />
<hkern u1="&#xdd;" u2="&#x2014;" k="186" />
<hkern u1="&#xdd;" u2="&#x2013;" k="186" />
<hkern u1="&#xdd;" u2="&#x17e;" k="102" />
<hkern u1="&#xdd;" u2="&#x17c;" k="102" />
<hkern u1="&#xdd;" u2="&#x17a;" k="102" />
<hkern u1="&#xdd;" u2="&#x161;" k="196" />
<hkern u1="&#xdd;" u2="&#x15b;" k="196" />
<hkern u1="&#xdd;" u2="&#x153;" k="196" />
<hkern u1="&#xdd;" u2="&#x152;" k="71" />
<hkern u1="&#xdd;" u2="&#x144;" k="118" />
<hkern u1="&#xdd;" u2="&#x119;" k="196" />
<hkern u1="&#xdd;" u2="&#x107;" k="196" />
<hkern u1="&#xdd;" u2="&#x106;" k="71" />
<hkern u1="&#xdd;" u2="&#x105;" k="196" />
<hkern u1="&#xdd;" u2="&#x104;" k="145" />
<hkern u1="&#xdd;" u2="&#xfc;" k="118" />
<hkern u1="&#xdd;" u2="&#xfb;" k="118" />
<hkern u1="&#xdd;" u2="&#xfa;" k="118" />
<hkern u1="&#xdd;" u2="&#xf9;" k="118" />
<hkern u1="&#xdd;" u2="&#xf8;" k="196" />
<hkern u1="&#xdd;" u2="&#xf6;" k="196" />
<hkern u1="&#xdd;" u2="&#xf5;" k="196" />
<hkern u1="&#xdd;" u2="&#xf4;" k="196" />
<hkern u1="&#xdd;" u2="&#xf3;" k="196" />
<hkern u1="&#xdd;" u2="&#xf2;" k="196" />
<hkern u1="&#xdd;" u2="&#xf1;" k="118" />
<hkern u1="&#xdd;" u2="&#xf0;" k="196" />
<hkern u1="&#xdd;" u2="&#xeb;" k="196" />
<hkern u1="&#xdd;" u2="&#xea;" k="196" />
<hkern u1="&#xdd;" u2="&#xe9;" k="196" />
<hkern u1="&#xdd;" u2="&#xe8;" k="196" />
<hkern u1="&#xdd;" u2="&#xe7;" k="196" />
<hkern u1="&#xdd;" u2="&#xe6;" k="196" />
<hkern u1="&#xdd;" u2="&#xe5;" k="196" />
<hkern u1="&#xdd;" u2="&#xe4;" k="196" />
<hkern u1="&#xdd;" u2="&#xe3;" k="196" />
<hkern u1="&#xdd;" u2="&#xe2;" k="196" />
<hkern u1="&#xdd;" u2="&#xe1;" k="196" />
<hkern u1="&#xdd;" u2="&#xe0;" k="196" />
<hkern u1="&#xdd;" u2="&#xd8;" k="71" />
<hkern u1="&#xdd;" u2="&#xd6;" k="71" />
<hkern u1="&#xdd;" u2="&#xd5;" k="71" />
<hkern u1="&#xdd;" u2="&#xd4;" k="71" />
<hkern u1="&#xdd;" u2="&#xd3;" k="71" />
<hkern u1="&#xdd;" u2="&#xd2;" k="71" />
<hkern u1="&#xdd;" u2="&#xc7;" k="71" />
<hkern u1="&#xdd;" u2="&#xc6;" k="145" />
<hkern u1="&#xdd;" u2="&#xc5;" k="145" />
<hkern u1="&#xdd;" u2="&#xc4;" k="145" />
<hkern u1="&#xdd;" u2="&#xc3;" k="145" />
<hkern u1="&#xdd;" u2="&#xc2;" k="145" />
<hkern u1="&#xdd;" u2="&#xc1;" k="145" />
<hkern u1="&#xdd;" u2="&#xc0;" k="145" />
<hkern u1="&#xdd;" u2="&#xbb;" k="186" />
<hkern u1="&#xdd;" u2="&#xba;" k="-56" />
<hkern u1="&#xdd;" u2="&#xb7;" k="186" />
<hkern u1="&#xdd;" u2="&#xb5;" k="118" />
<hkern u1="&#xdd;" u2="&#xb0;" k="-56" />
<hkern u1="&#xdd;" u2="&#xae;" k="71" />
<hkern u1="&#xdd;" u2="&#xab;" k="186" />
<hkern u1="&#xdd;" u2="&#xaa;" k="-56" />
<hkern u1="&#xdd;" u2="&#xa9;" k="71" />
<hkern u1="&#xdd;" u2="z" k="102" />
<hkern u1="&#xdd;" u2="u" k="118" />
<hkern u1="&#xdd;" u2="s" k="196" />
<hkern u1="&#xdd;" u2="r" k="118" />
<hkern u1="&#xdd;" u2="q" k="196" />
<hkern u1="&#xdd;" u2="p" k="118" />
<hkern u1="&#xdd;" u2="o" k="196" />
<hkern u1="&#xdd;" u2="n" k="118" />
<hkern u1="&#xdd;" u2="m" k="118" />
<hkern u1="&#xdd;" u2="g" k="173" />
<hkern u1="&#xdd;" u2="e" k="196" />
<hkern u1="&#xdd;" u2="d" k="196" />
<hkern u1="&#xdd;" u2="c" k="196" />
<hkern u1="&#xdd;" u2="a" k="196" />
<hkern u1="&#xdd;" u2="Q" k="71" />
<hkern u1="&#xdd;" u2="O" k="71" />
<hkern u1="&#xdd;" u2="J" k="205" />
<hkern u1="&#xdd;" u2="G" k="71" />
<hkern u1="&#xdd;" u2="C" k="71" />
<hkern u1="&#xdd;" u2="A" k="145" />
<hkern u1="&#xdd;" u2="&#x40;" k="71" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-51" />
<hkern u1="&#xdd;" u2="&#x3b;" k="118" />
<hkern u1="&#xdd;" u2="&#x3a;" k="118" />
<hkern u1="&#xdd;" u2="&#x2f;" k="145" />
<hkern u1="&#xdd;" u2="&#x2e;" k="222" />
<hkern u1="&#xdd;" u2="&#x2d;" k="186" />
<hkern u1="&#xdd;" u2="&#x2c;" k="222" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-56" />
<hkern u1="&#xdd;" u2="&#x27;" k="-56" />
<hkern u1="&#xdd;" u2="&#x26;" k="145" />
<hkern u1="&#xdd;" u2="&#x22;" k="-56" />
<hkern u1="&#xde;" u2="&#x2206;" k="37" />
<hkern u1="&#xde;" u2="&#x201d;" k="56" />
<hkern u1="&#xde;" u2="&#x201c;" k="56" />
<hkern u1="&#xde;" u2="&#x2019;" k="56" />
<hkern u1="&#xde;" u2="&#x2018;" k="56" />
<hkern u1="&#xde;" u2="&#x17d;" k="71" />
<hkern u1="&#xde;" u2="&#x17b;" k="71" />
<hkern u1="&#xde;" u2="&#x179;" k="71" />
<hkern u1="&#xde;" u2="&#x178;" k="82" />
<hkern u1="&#xde;" u2="&#x104;" k="37" />
<hkern u1="&#xde;" u2="&#xdd;" k="82" />
<hkern u1="&#xde;" u2="&#xc6;" k="37" />
<hkern u1="&#xde;" u2="&#xc5;" k="37" />
<hkern u1="&#xde;" u2="&#xc4;" k="37" />
<hkern u1="&#xde;" u2="&#xc3;" k="37" />
<hkern u1="&#xde;" u2="&#xc2;" k="37" />
<hkern u1="&#xde;" u2="&#xc1;" k="37" />
<hkern u1="&#xde;" u2="&#xc0;" k="37" />
<hkern u1="&#xde;" u2="&#xba;" k="56" />
<hkern u1="&#xde;" u2="&#xb0;" k="56" />
<hkern u1="&#xde;" u2="&#xaa;" k="56" />
<hkern u1="&#xde;" u2="&#x7d;" k="41" />
<hkern u1="&#xde;" u2="]" k="41" />
<hkern u1="&#xde;" u2="\" k="52" />
<hkern u1="&#xde;" u2="Z" k="71" />
<hkern u1="&#xde;" u2="Y" k="82" />
<hkern u1="&#xde;" u2="X" k="70" />
<hkern u1="&#xde;" u2="V" k="52" />
<hkern u1="&#xde;" u2="T" k="99" />
<hkern u1="&#xde;" u2="A" k="37" />
<hkern u1="&#xde;" u2="&#x2f;" k="37" />
<hkern u1="&#xde;" u2="&#x2a;" k="56" />
<hkern u1="&#xde;" u2="&#x29;" k="41" />
<hkern u1="&#xde;" u2="&#x27;" k="56" />
<hkern u1="&#xde;" u2="&#x26;" k="37" />
<hkern u1="&#xde;" u2="&#x22;" k="56" />
<hkern u1="&#xe6;" u2="&#x201d;" k="82" />
<hkern u1="&#xe6;" u2="&#x201c;" k="82" />
<hkern u1="&#xe6;" u2="&#x2019;" k="82" />
<hkern u1="&#xe6;" u2="&#x2018;" k="82" />
<hkern u1="&#xe6;" u2="&#xba;" k="82" />
<hkern u1="&#xe6;" u2="&#xb0;" k="82" />
<hkern u1="&#xe6;" u2="&#xaa;" k="82" />
<hkern u1="&#xe6;" u2="&#x7d;" k="32" />
<hkern u1="&#xe6;" u2="x" k="52" />
<hkern u1="&#xe6;" u2="]" k="32" />
<hkern u1="&#xe6;" u2="&#x2a;" k="82" />
<hkern u1="&#xe6;" u2="&#x29;" k="32" />
<hkern u1="&#xe6;" u2="&#x27;" k="82" />
<hkern u1="&#xe6;" u2="&#x22;" k="82" />
<hkern u1="&#xe8;" u2="&#x201d;" k="82" />
<hkern u1="&#xe8;" u2="&#x201c;" k="82" />
<hkern u1="&#xe8;" u2="&#x2019;" k="82" />
<hkern u1="&#xe8;" u2="&#x2018;" k="82" />
<hkern u1="&#xe8;" u2="&#xba;" k="82" />
<hkern u1="&#xe8;" u2="&#xb0;" k="82" />
<hkern u1="&#xe8;" u2="&#xaa;" k="82" />
<hkern u1="&#xe8;" u2="&#x7d;" k="32" />
<hkern u1="&#xe8;" u2="x" k="52" />
<hkern u1="&#xe8;" u2="]" k="32" />
<hkern u1="&#xe8;" u2="&#x2a;" k="82" />
<hkern u1="&#xe8;" u2="&#x29;" k="32" />
<hkern u1="&#xe8;" u2="&#x27;" k="82" />
<hkern u1="&#xe8;" u2="&#x22;" k="82" />
<hkern u1="&#xe9;" u2="&#x201d;" k="82" />
<hkern u1="&#xe9;" u2="&#x201c;" k="82" />
<hkern u1="&#xe9;" u2="&#x2019;" k="82" />
<hkern u1="&#xe9;" u2="&#x2018;" k="82" />
<hkern u1="&#xe9;" u2="&#xba;" k="82" />
<hkern u1="&#xe9;" u2="&#xb0;" k="82" />
<hkern u1="&#xe9;" u2="&#xaa;" k="82" />
<hkern u1="&#xe9;" u2="&#x7d;" k="32" />
<hkern u1="&#xe9;" u2="x" k="52" />
<hkern u1="&#xe9;" u2="]" k="32" />
<hkern u1="&#xe9;" u2="&#x2a;" k="82" />
<hkern u1="&#xe9;" u2="&#x29;" k="32" />
<hkern u1="&#xe9;" u2="&#x27;" k="82" />
<hkern u1="&#xe9;" u2="&#x22;" k="82" />
<hkern u1="&#xea;" u2="&#x201d;" k="82" />
<hkern u1="&#xea;" u2="&#x201c;" k="82" />
<hkern u1="&#xea;" u2="&#x2019;" k="82" />
<hkern u1="&#xea;" u2="&#x2018;" k="82" />
<hkern u1="&#xea;" u2="&#xba;" k="82" />
<hkern u1="&#xea;" u2="&#xb0;" k="82" />
<hkern u1="&#xea;" u2="&#xaa;" k="82" />
<hkern u1="&#xea;" u2="&#x7d;" k="32" />
<hkern u1="&#xea;" u2="x" k="52" />
<hkern u1="&#xea;" u2="]" k="32" />
<hkern u1="&#xea;" u2="&#x2a;" k="82" />
<hkern u1="&#xea;" u2="&#x29;" k="32" />
<hkern u1="&#xea;" u2="&#x27;" k="82" />
<hkern u1="&#xea;" u2="&#x22;" k="82" />
<hkern u1="&#xeb;" u2="&#x201d;" k="82" />
<hkern u1="&#xeb;" u2="&#x201c;" k="82" />
<hkern u1="&#xeb;" u2="&#x2019;" k="82" />
<hkern u1="&#xeb;" u2="&#x2018;" k="82" />
<hkern u1="&#xeb;" u2="&#xba;" k="82" />
<hkern u1="&#xeb;" u2="&#xb0;" k="82" />
<hkern u1="&#xeb;" u2="&#xaa;" k="82" />
<hkern u1="&#xeb;" u2="&#x7d;" k="32" />
<hkern u1="&#xeb;" u2="x" k="52" />
<hkern u1="&#xeb;" u2="]" k="32" />
<hkern u1="&#xeb;" u2="&#x2a;" k="82" />
<hkern u1="&#xeb;" u2="&#x29;" k="32" />
<hkern u1="&#xeb;" u2="&#x27;" k="82" />
<hkern u1="&#xeb;" u2="&#x22;" k="82" />
<hkern u1="&#xf1;" u2="&#x201d;" k="61" />
<hkern u1="&#xf1;" u2="&#x201c;" k="61" />
<hkern u1="&#xf1;" u2="&#x2019;" k="61" />
<hkern u1="&#xf1;" u2="&#x2018;" k="61" />
<hkern u1="&#xf1;" u2="&#xff;" k="32" />
<hkern u1="&#xf1;" u2="&#xfd;" k="32" />
<hkern u1="&#xf1;" u2="&#xba;" k="61" />
<hkern u1="&#xf1;" u2="&#xb0;" k="61" />
<hkern u1="&#xf1;" u2="&#xaa;" k="61" />
<hkern u1="&#xf1;" u2="y" k="43" />
<hkern u1="&#xf1;" u2="v" k="32" />
<hkern u1="&#xf1;" u2="&#x2a;" k="61" />
<hkern u1="&#xf1;" u2="&#x27;" k="61" />
<hkern u1="&#xf1;" u2="&#x22;" k="61" />
<hkern u1="&#xf2;" u2="&#x201d;" k="82" />
<hkern u1="&#xf2;" u2="&#x201c;" k="82" />
<hkern u1="&#xf2;" u2="&#x2019;" k="82" />
<hkern u1="&#xf2;" u2="&#x2018;" k="82" />
<hkern u1="&#xf2;" u2="&#xba;" k="82" />
<hkern u1="&#xf2;" u2="&#xb0;" k="82" />
<hkern u1="&#xf2;" u2="&#xaa;" k="82" />
<hkern u1="&#xf2;" u2="&#x7d;" k="32" />
<hkern u1="&#xf2;" u2="x" k="52" />
<hkern u1="&#xf2;" u2="]" k="32" />
<hkern u1="&#xf2;" u2="&#x2a;" k="82" />
<hkern u1="&#xf2;" u2="&#x29;" k="32" />
<hkern u1="&#xf2;" u2="&#x27;" k="82" />
<hkern u1="&#xf2;" u2="&#x22;" k="82" />
<hkern u1="&#xf3;" u2="&#x201d;" k="82" />
<hkern u1="&#xf3;" u2="&#x201c;" k="82" />
<hkern u1="&#xf3;" u2="&#x2019;" k="82" />
<hkern u1="&#xf3;" u2="&#x2018;" k="82" />
<hkern u1="&#xf3;" u2="&#xba;" k="82" />
<hkern u1="&#xf3;" u2="&#xb0;" k="82" />
<hkern u1="&#xf3;" u2="&#xaa;" k="82" />
<hkern u1="&#xf3;" u2="&#x7d;" k="32" />
<hkern u1="&#xf3;" u2="x" k="52" />
<hkern u1="&#xf3;" u2="]" k="32" />
<hkern u1="&#xf3;" u2="&#x2a;" k="82" />
<hkern u1="&#xf3;" u2="&#x29;" k="32" />
<hkern u1="&#xf3;" u2="&#x27;" k="82" />
<hkern u1="&#xf3;" u2="&#x22;" k="82" />
<hkern u1="&#xf4;" u2="&#x201d;" k="82" />
<hkern u1="&#xf4;" u2="&#x201c;" k="82" />
<hkern u1="&#xf4;" u2="&#x2019;" k="82" />
<hkern u1="&#xf4;" u2="&#x2018;" k="82" />
<hkern u1="&#xf4;" u2="&#xba;" k="82" />
<hkern u1="&#xf4;" u2="&#xb0;" k="82" />
<hkern u1="&#xf4;" u2="&#xaa;" k="82" />
<hkern u1="&#xf4;" u2="&#x7d;" k="32" />
<hkern u1="&#xf4;" u2="x" k="52" />
<hkern u1="&#xf4;" u2="]" k="32" />
<hkern u1="&#xf4;" u2="&#x2a;" k="82" />
<hkern u1="&#xf4;" u2="&#x29;" k="32" />
<hkern u1="&#xf4;" u2="&#x27;" k="82" />
<hkern u1="&#xf4;" u2="&#x22;" k="82" />
<hkern u1="&#xf5;" u2="&#x201d;" k="82" />
<hkern u1="&#xf5;" u2="&#x201c;" k="82" />
<hkern u1="&#xf5;" u2="&#x2019;" k="82" />
<hkern u1="&#xf5;" u2="&#x2018;" k="82" />
<hkern u1="&#xf5;" u2="&#xba;" k="82" />
<hkern u1="&#xf5;" u2="&#xb0;" k="82" />
<hkern u1="&#xf5;" u2="&#xaa;" k="82" />
<hkern u1="&#xf5;" u2="&#x7d;" k="32" />
<hkern u1="&#xf5;" u2="x" k="52" />
<hkern u1="&#xf5;" u2="]" k="32" />
<hkern u1="&#xf5;" u2="&#x2a;" k="82" />
<hkern u1="&#xf5;" u2="&#x29;" k="32" />
<hkern u1="&#xf5;" u2="&#x27;" k="82" />
<hkern u1="&#xf5;" u2="&#x22;" k="82" />
<hkern u1="&#xf6;" u2="&#x201d;" k="82" />
<hkern u1="&#xf6;" u2="&#x201c;" k="82" />
<hkern u1="&#xf6;" u2="&#x2019;" k="82" />
<hkern u1="&#xf6;" u2="&#x2018;" k="82" />
<hkern u1="&#xf6;" u2="&#xba;" k="82" />
<hkern u1="&#xf6;" u2="&#xb0;" k="82" />
<hkern u1="&#xf6;" u2="&#xaa;" k="82" />
<hkern u1="&#xf6;" u2="&#x7d;" k="32" />
<hkern u1="&#xf6;" u2="x" k="52" />
<hkern u1="&#xf6;" u2="]" k="32" />
<hkern u1="&#xf6;" u2="&#x2a;" k="82" />
<hkern u1="&#xf6;" u2="&#x29;" k="32" />
<hkern u1="&#xf6;" u2="&#x27;" k="82" />
<hkern u1="&#xf6;" u2="&#x22;" k="82" />
<hkern u1="&#xf8;" u2="&#x201d;" k="82" />
<hkern u1="&#xf8;" u2="&#x201c;" k="82" />
<hkern u1="&#xf8;" u2="&#x2019;" k="82" />
<hkern u1="&#xf8;" u2="&#x2018;" k="82" />
<hkern u1="&#xf8;" u2="&#xba;" k="82" />
<hkern u1="&#xf8;" u2="&#xb0;" k="82" />
<hkern u1="&#xf8;" u2="&#xaa;" k="82" />
<hkern u1="&#xf8;" u2="&#x7d;" k="32" />
<hkern u1="&#xf8;" u2="x" k="52" />
<hkern u1="&#xf8;" u2="]" k="32" />
<hkern u1="&#xf8;" u2="&#x2a;" k="82" />
<hkern u1="&#xf8;" u2="&#x29;" k="32" />
<hkern u1="&#xf8;" u2="&#x27;" k="82" />
<hkern u1="&#xf8;" u2="&#x22;" k="82" />
<hkern u1="&#xfd;" u2="&#x2206;" k="48" />
<hkern u1="&#xfd;" u2="&#x2026;" k="145" />
<hkern u1="&#xfd;" u2="&#x201e;" k="145" />
<hkern u1="&#xfd;" u2="&#x201a;" k="145" />
<hkern u1="&#xfd;" u2="&#x153;" k="27" />
<hkern u1="&#xfd;" u2="&#x119;" k="27" />
<hkern u1="&#xfd;" u2="&#x107;" k="27" />
<hkern u1="&#xfd;" u2="&#x105;" k="27" />
<hkern u1="&#xfd;" u2="&#x104;" k="48" />
<hkern u1="&#xfd;" u2="&#xf8;" k="27" />
<hkern u1="&#xfd;" u2="&#xf6;" k="27" />
<hkern u1="&#xfd;" u2="&#xf5;" k="27" />
<hkern u1="&#xfd;" u2="&#xf4;" k="27" />
<hkern u1="&#xfd;" u2="&#xf3;" k="27" />
<hkern u1="&#xfd;" u2="&#xf2;" k="27" />
<hkern u1="&#xfd;" u2="&#xf0;" k="27" />
<hkern u1="&#xfd;" u2="&#xeb;" k="27" />
<hkern u1="&#xfd;" u2="&#xea;" k="27" />
<hkern u1="&#xfd;" u2="&#xe9;" k="27" />
<hkern u1="&#xfd;" u2="&#xe8;" k="27" />
<hkern u1="&#xfd;" u2="&#xe7;" k="27" />
<hkern u1="&#xfd;" u2="&#xe6;" k="27" />
<hkern u1="&#xfd;" u2="&#xe5;" k="27" />
<hkern u1="&#xfd;" u2="&#xe4;" k="27" />
<hkern u1="&#xfd;" u2="&#xe3;" k="27" />
<hkern u1="&#xfd;" u2="&#xe2;" k="27" />
<hkern u1="&#xfd;" u2="&#xe1;" k="27" />
<hkern u1="&#xfd;" u2="&#xe0;" k="27" />
<hkern u1="&#xfd;" u2="&#xc6;" k="48" />
<hkern u1="&#xfd;" u2="&#xc5;" k="48" />
<hkern u1="&#xfd;" u2="&#xc4;" k="48" />
<hkern u1="&#xfd;" u2="&#xc3;" k="48" />
<hkern u1="&#xfd;" u2="&#xc2;" k="48" />
<hkern u1="&#xfd;" u2="&#xc1;" k="48" />
<hkern u1="&#xfd;" u2="&#xc0;" k="48" />
<hkern u1="&#xfd;" u2="q" k="27" />
<hkern u1="&#xfd;" u2="o" k="27" />
<hkern u1="&#xfd;" u2="e" k="27" />
<hkern u1="&#xfd;" u2="d" k="27" />
<hkern u1="&#xfd;" u2="c" k="27" />
<hkern u1="&#xfd;" u2="a" k="27" />
<hkern u1="&#xfd;" u2="A" k="48" />
<hkern u1="&#xfd;" u2="&#x2f;" k="48" />
<hkern u1="&#xfd;" u2="&#x2e;" k="145" />
<hkern u1="&#xfd;" u2="&#x2c;" k="145" />
<hkern u1="&#xfd;" u2="&#x26;" k="48" />
<hkern u1="&#xfe;" u2="&#x201d;" k="82" />
<hkern u1="&#xfe;" u2="&#x201c;" k="82" />
<hkern u1="&#xfe;" u2="&#x2019;" k="82" />
<hkern u1="&#xfe;" u2="&#x2018;" k="82" />
<hkern u1="&#xfe;" u2="&#xba;" k="82" />
<hkern u1="&#xfe;" u2="&#xb0;" k="82" />
<hkern u1="&#xfe;" u2="&#xaa;" k="82" />
<hkern u1="&#xfe;" u2="&#x7d;" k="32" />
<hkern u1="&#xfe;" u2="x" k="52" />
<hkern u1="&#xfe;" u2="]" k="32" />
<hkern u1="&#xfe;" u2="&#x2a;" k="82" />
<hkern u1="&#xfe;" u2="&#x29;" k="32" />
<hkern u1="&#xfe;" u2="&#x27;" k="82" />
<hkern u1="&#xfe;" u2="&#x22;" k="82" />
<hkern u1="&#xff;" u2="&#x2206;" k="48" />
<hkern u1="&#xff;" u2="&#x2026;" k="145" />
<hkern u1="&#xff;" u2="&#x201e;" k="145" />
<hkern u1="&#xff;" u2="&#x201a;" k="145" />
<hkern u1="&#xff;" u2="&#x153;" k="27" />
<hkern u1="&#xff;" u2="&#x119;" k="27" />
<hkern u1="&#xff;" u2="&#x107;" k="27" />
<hkern u1="&#xff;" u2="&#x105;" k="27" />
<hkern u1="&#xff;" u2="&#x104;" k="48" />
<hkern u1="&#xff;" u2="&#xf8;" k="27" />
<hkern u1="&#xff;" u2="&#xf6;" k="27" />
<hkern u1="&#xff;" u2="&#xf5;" k="27" />
<hkern u1="&#xff;" u2="&#xf4;" k="27" />
<hkern u1="&#xff;" u2="&#xf3;" k="27" />
<hkern u1="&#xff;" u2="&#xf2;" k="27" />
<hkern u1="&#xff;" u2="&#xf0;" k="27" />
<hkern u1="&#xff;" u2="&#xeb;" k="27" />
<hkern u1="&#xff;" u2="&#xea;" k="27" />
<hkern u1="&#xff;" u2="&#xe9;" k="27" />
<hkern u1="&#xff;" u2="&#xe8;" k="27" />
<hkern u1="&#xff;" u2="&#xe7;" k="27" />
<hkern u1="&#xff;" u2="&#xe6;" k="27" />
<hkern u1="&#xff;" u2="&#xe5;" k="27" />
<hkern u1="&#xff;" u2="&#xe4;" k="27" />
<hkern u1="&#xff;" u2="&#xe3;" k="27" />
<hkern u1="&#xff;" u2="&#xe2;" k="27" />
<hkern u1="&#xff;" u2="&#xe1;" k="27" />
<hkern u1="&#xff;" u2="&#xe0;" k="27" />
<hkern u1="&#xff;" u2="&#xc6;" k="48" />
<hkern u1="&#xff;" u2="&#xc5;" k="48" />
<hkern u1="&#xff;" u2="&#xc4;" k="48" />
<hkern u1="&#xff;" u2="&#xc3;" k="48" />
<hkern u1="&#xff;" u2="&#xc2;" k="48" />
<hkern u1="&#xff;" u2="&#xc1;" k="48" />
<hkern u1="&#xff;" u2="&#xc0;" k="48" />
<hkern u1="&#xff;" u2="q" k="27" />
<hkern u1="&#xff;" u2="o" k="27" />
<hkern u1="&#xff;" u2="e" k="27" />
<hkern u1="&#xff;" u2="d" k="27" />
<hkern u1="&#xff;" u2="c" k="27" />
<hkern u1="&#xff;" u2="a" k="27" />
<hkern u1="&#xff;" u2="A" k="48" />
<hkern u1="&#xff;" u2="&#x2f;" k="48" />
<hkern u1="&#xff;" u2="&#x2e;" k="145" />
<hkern u1="&#xff;" u2="&#x2c;" k="145" />
<hkern u1="&#xff;" u2="&#x26;" k="48" />
<hkern u1="&#x104;" u2="&#x203a;" k="53" />
<hkern u1="&#x104;" u2="&#x2039;" k="53" />
<hkern u1="&#x104;" u2="&#x2022;" k="53" />
<hkern u1="&#x104;" u2="&#x201d;" k="186" />
<hkern u1="&#x104;" u2="&#x201c;" k="186" />
<hkern u1="&#x104;" u2="&#x2019;" k="186" />
<hkern u1="&#x104;" u2="&#x2018;" k="186" />
<hkern u1="&#x104;" u2="&#x2014;" k="53" />
<hkern u1="&#x104;" u2="&#x2013;" k="53" />
<hkern u1="&#x104;" u2="&#x178;" k="156" />
<hkern u1="&#x104;" u2="&#x152;" k="48" />
<hkern u1="&#x104;" u2="&#x106;" k="48" />
<hkern u1="&#x104;" u2="&#xff;" k="48" />
<hkern u1="&#x104;" u2="&#xfd;" k="48" />
<hkern u1="&#x104;" u2="&#xdd;" k="156" />
<hkern u1="&#x104;" u2="&#xdc;" k="41" />
<hkern u1="&#x104;" u2="&#xdb;" k="41" />
<hkern u1="&#x104;" u2="&#xda;" k="41" />
<hkern u1="&#x104;" u2="&#xd9;" k="41" />
<hkern u1="&#x104;" u2="&#xd8;" k="48" />
<hkern u1="&#x104;" u2="&#xd6;" k="48" />
<hkern u1="&#x104;" u2="&#xd5;" k="48" />
<hkern u1="&#x104;" u2="&#xd4;" k="48" />
<hkern u1="&#x104;" u2="&#xd3;" k="48" />
<hkern u1="&#x104;" u2="&#xd2;" k="48" />
<hkern u1="&#x104;" u2="&#xc7;" k="48" />
<hkern u1="&#x104;" u2="&#xbb;" k="53" />
<hkern u1="&#x104;" u2="&#xba;" k="186" />
<hkern u1="&#x104;" u2="&#xb7;" k="53" />
<hkern u1="&#x104;" u2="&#xb0;" k="186" />
<hkern u1="&#x104;" u2="&#xae;" k="48" />
<hkern u1="&#x104;" u2="&#xab;" k="53" />
<hkern u1="&#x104;" u2="&#xaa;" k="186" />
<hkern u1="&#x104;" u2="&#xa9;" k="48" />
<hkern u1="&#x104;" u2="y" k="48" />
<hkern u1="&#x104;" u2="w" k="33" />
<hkern u1="&#x104;" u2="v" k="48" />
<hkern u1="&#x104;" u2="t" k="58" />
<hkern u1="&#x104;" u2="\" k="117" />
<hkern u1="&#x104;" u2="Y" k="156" />
<hkern u1="&#x104;" u2="W" k="85" />
<hkern u1="&#x104;" u2="V" k="117" />
<hkern u1="&#x104;" u2="U" k="41" />
<hkern u1="&#x104;" u2="T" k="135" />
<hkern u1="&#x104;" u2="Q" k="48" />
<hkern u1="&#x104;" u2="O" k="48" />
<hkern u1="&#x104;" u2="J" k="-63" />
<hkern u1="&#x104;" u2="G" k="48" />
<hkern u1="&#x104;" u2="C" k="48" />
<hkern u1="&#x104;" u2="&#x40;" k="48" />
<hkern u1="&#x104;" u2="&#x2d;" k="53" />
<hkern u1="&#x104;" u2="&#x2a;" k="186" />
<hkern u1="&#x104;" u2="&#x27;" k="186" />
<hkern u1="&#x104;" u2="&#x22;" k="186" />
<hkern u1="&#x106;" u2="&#x203a;" k="131" />
<hkern u1="&#x106;" u2="&#x2039;" k="131" />
<hkern u1="&#x106;" u2="&#x2022;" k="131" />
<hkern u1="&#x106;" u2="&#x2014;" k="131" />
<hkern u1="&#x106;" u2="&#x2013;" k="131" />
<hkern u1="&#x106;" u2="&#xbb;" k="131" />
<hkern u1="&#x106;" u2="&#xb7;" k="131" />
<hkern u1="&#x106;" u2="&#xab;" k="131" />
<hkern u1="&#x106;" u2="&#x2d;" k="131" />
<hkern u1="&#x119;" u2="&#x201d;" k="82" />
<hkern u1="&#x119;" u2="&#x201c;" k="82" />
<hkern u1="&#x119;" u2="&#x2019;" k="82" />
<hkern u1="&#x119;" u2="&#x2018;" k="82" />
<hkern u1="&#x119;" u2="&#xba;" k="82" />
<hkern u1="&#x119;" u2="&#xb0;" k="82" />
<hkern u1="&#x119;" u2="&#xaa;" k="82" />
<hkern u1="&#x119;" u2="&#x7d;" k="32" />
<hkern u1="&#x119;" u2="x" k="52" />
<hkern u1="&#x119;" u2="]" k="32" />
<hkern u1="&#x119;" u2="&#x2a;" k="82" />
<hkern u1="&#x119;" u2="&#x29;" k="32" />
<hkern u1="&#x119;" u2="&#x27;" k="82" />
<hkern u1="&#x119;" u2="&#x22;" k="82" />
<hkern u1="&#x141;" u2="&#x203a;" k="120" />
<hkern u1="&#x141;" u2="&#x2039;" k="120" />
<hkern u1="&#x141;" u2="&#x2022;" k="120" />
<hkern u1="&#x141;" u2="&#x201d;" k="155" />
<hkern u1="&#x141;" u2="&#x201c;" k="155" />
<hkern u1="&#x141;" u2="&#x2019;" k="155" />
<hkern u1="&#x141;" u2="&#x2018;" k="155" />
<hkern u1="&#x141;" u2="&#x2014;" k="120" />
<hkern u1="&#x141;" u2="&#x2013;" k="120" />
<hkern u1="&#x141;" u2="&#x178;" k="156" />
<hkern u1="&#x141;" u2="&#xff;" k="82" />
<hkern u1="&#x141;" u2="&#xfd;" k="82" />
<hkern u1="&#x141;" u2="&#xdd;" k="156" />
<hkern u1="&#x141;" u2="&#xbb;" k="120" />
<hkern u1="&#x141;" u2="&#xba;" k="155" />
<hkern u1="&#x141;" u2="&#xb7;" k="120" />
<hkern u1="&#x141;" u2="&#xb0;" k="155" />
<hkern u1="&#x141;" u2="&#xab;" k="120" />
<hkern u1="&#x141;" u2="&#xaa;" k="155" />
<hkern u1="&#x141;" u2="y" k="82" />
<hkern u1="&#x141;" u2="w" k="52" />
<hkern u1="&#x141;" u2="v" k="82" />
<hkern u1="&#x141;" u2="\" k="166" />
<hkern u1="&#x141;" u2="Y" k="156" />
<hkern u1="&#x141;" u2="W" k="125" />
<hkern u1="&#x141;" u2="V" k="166" />
<hkern u1="&#x141;" u2="&#x2d;" k="120" />
<hkern u1="&#x141;" u2="&#x2a;" k="155" />
<hkern u1="&#x141;" u2="&#x27;" k="155" />
<hkern u1="&#x141;" u2="&#x22;" k="155" />
<hkern u1="&#x144;" u2="&#x201d;" k="61" />
<hkern u1="&#x144;" u2="&#x201c;" k="61" />
<hkern u1="&#x144;" u2="&#x2019;" k="61" />
<hkern u1="&#x144;" u2="&#x2018;" k="61" />
<hkern u1="&#x144;" u2="&#xff;" k="32" />
<hkern u1="&#x144;" u2="&#xfd;" k="32" />
<hkern u1="&#x144;" u2="&#xba;" k="61" />
<hkern u1="&#x144;" u2="&#xb0;" k="61" />
<hkern u1="&#x144;" u2="&#xaa;" k="61" />
<hkern u1="&#x144;" u2="y" k="43" />
<hkern u1="&#x144;" u2="v" k="32" />
<hkern u1="&#x144;" u2="&#x2a;" k="61" />
<hkern u1="&#x144;" u2="&#x27;" k="61" />
<hkern u1="&#x144;" u2="&#x22;" k="61" />
<hkern u1="&#x153;" u2="&#x201d;" k="82" />
<hkern u1="&#x153;" u2="&#x201c;" k="82" />
<hkern u1="&#x153;" u2="&#x2019;" k="82" />
<hkern u1="&#x153;" u2="&#x2018;" k="82" />
<hkern u1="&#x153;" u2="&#xba;" k="82" />
<hkern u1="&#x153;" u2="&#xb0;" k="82" />
<hkern u1="&#x153;" u2="&#xaa;" k="82" />
<hkern u1="&#x153;" u2="&#x7d;" k="32" />
<hkern u1="&#x153;" u2="x" k="52" />
<hkern u1="&#x153;" u2="]" k="32" />
<hkern u1="&#x153;" u2="&#x2a;" k="82" />
<hkern u1="&#x153;" u2="&#x29;" k="32" />
<hkern u1="&#x153;" u2="&#x27;" k="82" />
<hkern u1="&#x153;" u2="&#x22;" k="82" />
<hkern u1="&#x178;" u2="&#x2206;" k="145" />
<hkern u1="&#x178;" u2="&#x203a;" k="186" />
<hkern u1="&#x178;" u2="&#x2039;" k="186" />
<hkern u1="&#x178;" u2="&#x2026;" k="222" />
<hkern u1="&#x178;" u2="&#x2022;" k="186" />
<hkern u1="&#x178;" u2="&#x201e;" k="222" />
<hkern u1="&#x178;" u2="&#x201d;" k="-56" />
<hkern u1="&#x178;" u2="&#x201c;" k="-56" />
<hkern u1="&#x178;" u2="&#x201a;" k="222" />
<hkern u1="&#x178;" u2="&#x2019;" k="-56" />
<hkern u1="&#x178;" u2="&#x2018;" k="-56" />
<hkern u1="&#x178;" u2="&#x2014;" k="186" />
<hkern u1="&#x178;" u2="&#x2013;" k="186" />
<hkern u1="&#x178;" u2="&#x17e;" k="102" />
<hkern u1="&#x178;" u2="&#x17c;" k="102" />
<hkern u1="&#x178;" u2="&#x17a;" k="102" />
<hkern u1="&#x178;" u2="&#x161;" k="196" />
<hkern u1="&#x178;" u2="&#x15b;" k="196" />
<hkern u1="&#x178;" u2="&#x153;" k="196" />
<hkern u1="&#x178;" u2="&#x152;" k="71" />
<hkern u1="&#x178;" u2="&#x144;" k="118" />
<hkern u1="&#x178;" u2="&#x119;" k="196" />
<hkern u1="&#x178;" u2="&#x107;" k="196" />
<hkern u1="&#x178;" u2="&#x106;" k="71" />
<hkern u1="&#x178;" u2="&#x105;" k="196" />
<hkern u1="&#x178;" u2="&#x104;" k="145" />
<hkern u1="&#x178;" u2="&#xfc;" k="118" />
<hkern u1="&#x178;" u2="&#xfb;" k="118" />
<hkern u1="&#x178;" u2="&#xfa;" k="118" />
<hkern u1="&#x178;" u2="&#xf9;" k="118" />
<hkern u1="&#x178;" u2="&#xf8;" k="196" />
<hkern u1="&#x178;" u2="&#xf6;" k="196" />
<hkern u1="&#x178;" u2="&#xf5;" k="196" />
<hkern u1="&#x178;" u2="&#xf4;" k="196" />
<hkern u1="&#x178;" u2="&#xf3;" k="196" />
<hkern u1="&#x178;" u2="&#xf2;" k="196" />
<hkern u1="&#x178;" u2="&#xf1;" k="118" />
<hkern u1="&#x178;" u2="&#xf0;" k="196" />
<hkern u1="&#x178;" u2="&#xeb;" k="196" />
<hkern u1="&#x178;" u2="&#xea;" k="196" />
<hkern u1="&#x178;" u2="&#xe9;" k="196" />
<hkern u1="&#x178;" u2="&#xe8;" k="196" />
<hkern u1="&#x178;" u2="&#xe7;" k="196" />
<hkern u1="&#x178;" u2="&#xe6;" k="196" />
<hkern u1="&#x178;" u2="&#xe5;" k="196" />
<hkern u1="&#x178;" u2="&#xe4;" k="196" />
<hkern u1="&#x178;" u2="&#xe3;" k="196" />
<hkern u1="&#x178;" u2="&#xe2;" k="196" />
<hkern u1="&#x178;" u2="&#xe1;" k="196" />
<hkern u1="&#x178;" u2="&#xe0;" k="196" />
<hkern u1="&#x178;" u2="&#xd8;" k="71" />
<hkern u1="&#x178;" u2="&#xd6;" k="71" />
<hkern u1="&#x178;" u2="&#xd5;" k="71" />
<hkern u1="&#x178;" u2="&#xd4;" k="71" />
<hkern u1="&#x178;" u2="&#xd3;" k="71" />
<hkern u1="&#x178;" u2="&#xd2;" k="71" />
<hkern u1="&#x178;" u2="&#xc7;" k="71" />
<hkern u1="&#x178;" u2="&#xc6;" k="145" />
<hkern u1="&#x178;" u2="&#xc5;" k="145" />
<hkern u1="&#x178;" u2="&#xc4;" k="145" />
<hkern u1="&#x178;" u2="&#xc3;" k="145" />
<hkern u1="&#x178;" u2="&#xc2;" k="145" />
<hkern u1="&#x178;" u2="&#xc1;" k="145" />
<hkern u1="&#x178;" u2="&#xc0;" k="145" />
<hkern u1="&#x178;" u2="&#xbb;" k="186" />
<hkern u1="&#x178;" u2="&#xba;" k="-56" />
<hkern u1="&#x178;" u2="&#xb7;" k="186" />
<hkern u1="&#x178;" u2="&#xb5;" k="118" />
<hkern u1="&#x178;" u2="&#xb0;" k="-56" />
<hkern u1="&#x178;" u2="&#xae;" k="71" />
<hkern u1="&#x178;" u2="&#xab;" k="186" />
<hkern u1="&#x178;" u2="&#xaa;" k="-56" />
<hkern u1="&#x178;" u2="&#xa9;" k="71" />
<hkern u1="&#x178;" u2="z" k="102" />
<hkern u1="&#x178;" u2="u" k="118" />
<hkern u1="&#x178;" u2="s" k="196" />
<hkern u1="&#x178;" u2="r" k="118" />
<hkern u1="&#x178;" u2="q" k="196" />
<hkern u1="&#x178;" u2="p" k="118" />
<hkern u1="&#x178;" u2="o" k="196" />
<hkern u1="&#x178;" u2="n" k="118" />
<hkern u1="&#x178;" u2="m" k="118" />
<hkern u1="&#x178;" u2="g" k="173" />
<hkern u1="&#x178;" u2="e" k="196" />
<hkern u1="&#x178;" u2="d" k="196" />
<hkern u1="&#x178;" u2="c" k="196" />
<hkern u1="&#x178;" u2="a" k="196" />
<hkern u1="&#x178;" u2="Q" k="71" />
<hkern u1="&#x178;" u2="O" k="71" />
<hkern u1="&#x178;" u2="J" k="205" />
<hkern u1="&#x178;" u2="G" k="71" />
<hkern u1="&#x178;" u2="C" k="71" />
<hkern u1="&#x178;" u2="A" k="145" />
<hkern u1="&#x178;" u2="&#x40;" k="71" />
<hkern u1="&#x178;" u2="&#x3f;" k="-51" />
<hkern u1="&#x178;" u2="&#x3b;" k="118" />
<hkern u1="&#x178;" u2="&#x3a;" k="118" />
<hkern u1="&#x178;" u2="&#x2f;" k="145" />
<hkern u1="&#x178;" u2="&#x2e;" k="222" />
<hkern u1="&#x178;" u2="&#x2d;" k="186" />
<hkern u1="&#x178;" u2="&#x2c;" k="222" />
<hkern u1="&#x178;" u2="&#x2a;" k="-56" />
<hkern u1="&#x178;" u2="&#x27;" k="-56" />
<hkern u1="&#x178;" u2="&#x26;" k="145" />
<hkern u1="&#x178;" u2="&#x22;" k="-56" />
<hkern u1="&#x179;" u2="&#x203a;" k="87" />
<hkern u1="&#x179;" u2="&#x2039;" k="87" />
<hkern u1="&#x179;" u2="&#x2022;" k="87" />
<hkern u1="&#x179;" u2="&#x2014;" k="87" />
<hkern u1="&#x179;" u2="&#x2013;" k="87" />
<hkern u1="&#x179;" u2="&#x152;" k="59" />
<hkern u1="&#x179;" u2="&#x106;" k="59" />
<hkern u1="&#x179;" u2="&#xd8;" k="59" />
<hkern u1="&#x179;" u2="&#xd6;" k="59" />
<hkern u1="&#x179;" u2="&#xd5;" k="59" />
<hkern u1="&#x179;" u2="&#xd4;" k="59" />
<hkern u1="&#x179;" u2="&#xd3;" k="59" />
<hkern u1="&#x179;" u2="&#xd2;" k="59" />
<hkern u1="&#x179;" u2="&#xc7;" k="59" />
<hkern u1="&#x179;" u2="&#xbb;" k="87" />
<hkern u1="&#x179;" u2="&#xb7;" k="87" />
<hkern u1="&#x179;" u2="&#xae;" k="59" />
<hkern u1="&#x179;" u2="&#xab;" k="87" />
<hkern u1="&#x179;" u2="&#xa9;" k="59" />
<hkern u1="&#x179;" u2="Q" k="59" />
<hkern u1="&#x179;" u2="O" k="59" />
<hkern u1="&#x179;" u2="G" k="59" />
<hkern u1="&#x179;" u2="C" k="59" />
<hkern u1="&#x179;" u2="&#x40;" k="59" />
<hkern u1="&#x179;" u2="&#x3f;" k="-36" />
<hkern u1="&#x179;" u2="&#x2d;" k="87" />
<hkern u1="&#x17b;" u2="&#x203a;" k="87" />
<hkern u1="&#x17b;" u2="&#x2039;" k="87" />
<hkern u1="&#x17b;" u2="&#x2022;" k="87" />
<hkern u1="&#x17b;" u2="&#x2014;" k="87" />
<hkern u1="&#x17b;" u2="&#x2013;" k="87" />
<hkern u1="&#x17b;" u2="&#x152;" k="59" />
<hkern u1="&#x17b;" u2="&#x106;" k="59" />
<hkern u1="&#x17b;" u2="&#xd8;" k="59" />
<hkern u1="&#x17b;" u2="&#xd6;" k="59" />
<hkern u1="&#x17b;" u2="&#xd5;" k="59" />
<hkern u1="&#x17b;" u2="&#xd4;" k="59" />
<hkern u1="&#x17b;" u2="&#xd3;" k="59" />
<hkern u1="&#x17b;" u2="&#xd2;" k="59" />
<hkern u1="&#x17b;" u2="&#xc7;" k="59" />
<hkern u1="&#x17b;" u2="&#xbb;" k="87" />
<hkern u1="&#x17b;" u2="&#xb7;" k="87" />
<hkern u1="&#x17b;" u2="&#xae;" k="59" />
<hkern u1="&#x17b;" u2="&#xab;" k="87" />
<hkern u1="&#x17b;" u2="&#xa9;" k="59" />
<hkern u1="&#x17b;" u2="Q" k="59" />
<hkern u1="&#x17b;" u2="O" k="59" />
<hkern u1="&#x17b;" u2="G" k="59" />
<hkern u1="&#x17b;" u2="C" k="59" />
<hkern u1="&#x17b;" u2="&#x40;" k="59" />
<hkern u1="&#x17b;" u2="&#x3f;" k="-36" />
<hkern u1="&#x17b;" u2="&#x2d;" k="87" />
<hkern u1="&#x17d;" u2="&#x203a;" k="87" />
<hkern u1="&#x17d;" u2="&#x2039;" k="87" />
<hkern u1="&#x17d;" u2="&#x2022;" k="87" />
<hkern u1="&#x17d;" u2="&#x2014;" k="87" />
<hkern u1="&#x17d;" u2="&#x2013;" k="87" />
<hkern u1="&#x17d;" u2="&#x152;" k="59" />
<hkern u1="&#x17d;" u2="&#x106;" k="59" />
<hkern u1="&#x17d;" u2="&#xd8;" k="59" />
<hkern u1="&#x17d;" u2="&#xd6;" k="59" />
<hkern u1="&#x17d;" u2="&#xd5;" k="59" />
<hkern u1="&#x17d;" u2="&#xd4;" k="59" />
<hkern u1="&#x17d;" u2="&#xd3;" k="59" />
<hkern u1="&#x17d;" u2="&#xd2;" k="59" />
<hkern u1="&#x17d;" u2="&#xc7;" k="59" />
<hkern u1="&#x17d;" u2="&#xbb;" k="87" />
<hkern u1="&#x17d;" u2="&#xb7;" k="87" />
<hkern u1="&#x17d;" u2="&#xae;" k="59" />
<hkern u1="&#x17d;" u2="&#xab;" k="87" />
<hkern u1="&#x17d;" u2="&#xa9;" k="59" />
<hkern u1="&#x17d;" u2="Q" k="59" />
<hkern u1="&#x17d;" u2="O" k="59" />
<hkern u1="&#x17d;" u2="G" k="59" />
<hkern u1="&#x17d;" u2="C" k="59" />
<hkern u1="&#x17d;" u2="&#x40;" k="59" />
<hkern u1="&#x17d;" u2="&#x3f;" k="-36" />
<hkern u1="&#x17d;" u2="&#x2d;" k="87" />
<hkern u1="&#x2013;" u2="&#x2206;" k="53" />
<hkern u1="&#x2013;" u2="&#x2026;" k="166" />
<hkern u1="&#x2013;" u2="&#x201e;" k="166" />
<hkern u1="&#x2013;" u2="&#x201d;" k="85" />
<hkern u1="&#x2013;" u2="&#x201c;" k="85" />
<hkern u1="&#x2013;" u2="&#x201a;" k="166" />
<hkern u1="&#x2013;" u2="&#x2019;" k="85" />
<hkern u1="&#x2013;" u2="&#x2018;" k="85" />
<hkern u1="&#x2013;" u2="&#x17d;" k="56" />
<hkern u1="&#x2013;" u2="&#x17b;" k="56" />
<hkern u1="&#x2013;" u2="&#x179;" k="56" />
<hkern u1="&#x2013;" u2="&#x178;" k="197" />
<hkern u1="&#x2013;" u2="&#x104;" k="53" />
<hkern u1="&#x2013;" u2="&#xdd;" k="197" />
<hkern u1="&#x2013;" u2="&#xc6;" k="53" />
<hkern u1="&#x2013;" u2="&#xc5;" k="53" />
<hkern u1="&#x2013;" u2="&#xc4;" k="53" />
<hkern u1="&#x2013;" u2="&#xc3;" k="53" />
<hkern u1="&#x2013;" u2="&#xc2;" k="53" />
<hkern u1="&#x2013;" u2="&#xc1;" k="53" />
<hkern u1="&#x2013;" u2="&#xc0;" k="53" />
<hkern u1="&#x2013;" u2="&#xba;" k="85" />
<hkern u1="&#x2013;" u2="&#xb0;" k="85" />
<hkern u1="&#x2013;" u2="&#xaa;" k="85" />
<hkern u1="&#x2013;" u2="\" k="125" />
<hkern u1="&#x2013;" u2="Z" k="56" />
<hkern u1="&#x2013;" u2="Y" k="197" />
<hkern u1="&#x2013;" u2="X" k="72" />
<hkern u1="&#x2013;" u2="V" k="125" />
<hkern u1="&#x2013;" u2="T" k="196" />
<hkern u1="&#x2013;" u2="A" k="53" />
<hkern u1="&#x2013;" u2="&#x2f;" k="53" />
<hkern u1="&#x2013;" u2="&#x2e;" k="166" />
<hkern u1="&#x2013;" u2="&#x2c;" k="166" />
<hkern u1="&#x2013;" u2="&#x2a;" k="85" />
<hkern u1="&#x2013;" u2="&#x27;" k="85" />
<hkern u1="&#x2013;" u2="&#x26;" k="53" />
<hkern u1="&#x2013;" u2="&#x22;" k="85" />
<hkern u1="&#x2014;" u2="&#x2206;" k="53" />
<hkern u1="&#x2014;" u2="&#x2026;" k="166" />
<hkern u1="&#x2014;" u2="&#x201e;" k="166" />
<hkern u1="&#x2014;" u2="&#x201d;" k="85" />
<hkern u1="&#x2014;" u2="&#x201c;" k="85" />
<hkern u1="&#x2014;" u2="&#x201a;" k="166" />
<hkern u1="&#x2014;" u2="&#x2019;" k="85" />
<hkern u1="&#x2014;" u2="&#x2018;" k="85" />
<hkern u1="&#x2014;" u2="&#x17d;" k="56" />
<hkern u1="&#x2014;" u2="&#x17b;" k="56" />
<hkern u1="&#x2014;" u2="&#x179;" k="56" />
<hkern u1="&#x2014;" u2="&#x178;" k="197" />
<hkern u1="&#x2014;" u2="&#x104;" k="53" />
<hkern u1="&#x2014;" u2="&#xdd;" k="197" />
<hkern u1="&#x2014;" u2="&#xc6;" k="53" />
<hkern u1="&#x2014;" u2="&#xc5;" k="53" />
<hkern u1="&#x2014;" u2="&#xc4;" k="53" />
<hkern u1="&#x2014;" u2="&#xc3;" k="53" />
<hkern u1="&#x2014;" u2="&#xc2;" k="53" />
<hkern u1="&#x2014;" u2="&#xc1;" k="53" />
<hkern u1="&#x2014;" u2="&#xc0;" k="53" />
<hkern u1="&#x2014;" u2="&#xba;" k="85" />
<hkern u1="&#x2014;" u2="&#xb0;" k="85" />
<hkern u1="&#x2014;" u2="&#xaa;" k="85" />
<hkern u1="&#x2014;" u2="\" k="125" />
<hkern u1="&#x2014;" u2="Z" k="56" />
<hkern u1="&#x2014;" u2="Y" k="197" />
<hkern u1="&#x2014;" u2="X" k="72" />
<hkern u1="&#x2014;" u2="V" k="125" />
<hkern u1="&#x2014;" u2="T" k="196" />
<hkern u1="&#x2014;" u2="A" k="53" />
<hkern u1="&#x2014;" u2="&#x2f;" k="53" />
<hkern u1="&#x2014;" u2="&#x2e;" k="166" />
<hkern u1="&#x2014;" u2="&#x2c;" k="166" />
<hkern u1="&#x2014;" u2="&#x2a;" k="85" />
<hkern u1="&#x2014;" u2="&#x27;" k="85" />
<hkern u1="&#x2014;" u2="&#x26;" k="53" />
<hkern u1="&#x2014;" u2="&#x22;" k="85" />
<hkern u1="&#x2018;" u2="&#x2206;" k="175" />
<hkern u1="&#x2018;" u2="&#x203a;" k="85" />
<hkern u1="&#x2018;" u2="&#x2039;" k="85" />
<hkern u1="&#x2018;" u2="&#x2026;" k="94" />
<hkern u1="&#x2018;" u2="&#x2022;" k="85" />
<hkern u1="&#x2018;" u2="&#x201e;" k="94" />
<hkern u1="&#x2018;" u2="&#x201a;" k="94" />
<hkern u1="&#x2018;" u2="&#x2014;" k="85" />
<hkern u1="&#x2018;" u2="&#x2013;" k="85" />
<hkern u1="&#x2018;" u2="&#x178;" k="-46" />
<hkern u1="&#x2018;" u2="&#x153;" k="93" />
<hkern u1="&#x2018;" u2="&#x119;" k="93" />
<hkern u1="&#x2018;" u2="&#x107;" k="93" />
<hkern u1="&#x2018;" u2="&#x105;" k="93" />
<hkern u1="&#x2018;" u2="&#x104;" k="175" />
<hkern u1="&#x2018;" u2="&#xf8;" k="93" />
<hkern u1="&#x2018;" u2="&#xf6;" k="93" />
<hkern u1="&#x2018;" u2="&#xf5;" k="93" />
<hkern u1="&#x2018;" u2="&#xf4;" k="93" />
<hkern u1="&#x2018;" u2="&#xf3;" k="93" />
<hkern u1="&#x2018;" u2="&#xf2;" k="93" />
<hkern u1="&#x2018;" u2="&#xf0;" k="93" />
<hkern u1="&#x2018;" u2="&#xeb;" k="93" />
<hkern u1="&#x2018;" u2="&#xea;" k="93" />
<hkern u1="&#x2018;" u2="&#xe9;" k="93" />
<hkern u1="&#x2018;" u2="&#xe8;" k="93" />
<hkern u1="&#x2018;" u2="&#xe7;" k="93" />
<hkern u1="&#x2018;" u2="&#xe6;" k="93" />
<hkern u1="&#x2018;" u2="&#xe5;" k="93" />
<hkern u1="&#x2018;" u2="&#xe4;" k="93" />
<hkern u1="&#x2018;" u2="&#xe3;" k="93" />
<hkern u1="&#x2018;" u2="&#xe2;" k="93" />
<hkern u1="&#x2018;" u2="&#xe1;" k="93" />
<hkern u1="&#x2018;" u2="&#xe0;" k="93" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-46" />
<hkern u1="&#x2018;" u2="&#xc6;" k="175" />
<hkern u1="&#x2018;" u2="&#xc5;" k="175" />
<hkern u1="&#x2018;" u2="&#xc4;" k="175" />
<hkern u1="&#x2018;" u2="&#xc3;" k="175" />
<hkern u1="&#x2018;" u2="&#xc2;" k="175" />
<hkern u1="&#x2018;" u2="&#xc1;" k="175" />
<hkern u1="&#x2018;" u2="&#xc0;" k="175" />
<hkern u1="&#x2018;" u2="&#xbb;" k="85" />
<hkern u1="&#x2018;" u2="&#xb7;" k="85" />
<hkern u1="&#x2018;" u2="&#xab;" k="85" />
<hkern u1="&#x2018;" u2="q" k="93" />
<hkern u1="&#x2018;" u2="o" k="93" />
<hkern u1="&#x2018;" u2="e" k="93" />
<hkern u1="&#x2018;" u2="d" k="93" />
<hkern u1="&#x2018;" u2="c" k="93" />
<hkern u1="&#x2018;" u2="a" k="93" />
<hkern u1="&#x2018;" u2="\" k="-55" />
<hkern u1="&#x2018;" u2="Y" k="-46" />
<hkern u1="&#x2018;" u2="W" k="-49" />
<hkern u1="&#x2018;" u2="V" k="-55" />
<hkern u1="&#x2018;" u2="A" k="175" />
<hkern u1="&#x2018;" u2="&#x2f;" k="175" />
<hkern u1="&#x2018;" u2="&#x2e;" k="94" />
<hkern u1="&#x2018;" u2="&#x2d;" k="85" />
<hkern u1="&#x2018;" u2="&#x2c;" k="94" />
<hkern u1="&#x2018;" u2="&#x26;" k="175" />
<hkern u1="&#x2019;" u2="&#x2206;" k="175" />
<hkern u1="&#x2019;" u2="&#x203a;" k="85" />
<hkern u1="&#x2019;" u2="&#x2039;" k="85" />
<hkern u1="&#x2019;" u2="&#x2026;" k="94" />
<hkern u1="&#x2019;" u2="&#x2022;" k="85" />
<hkern u1="&#x2019;" u2="&#x201e;" k="94" />
<hkern u1="&#x2019;" u2="&#x201a;" k="94" />
<hkern u1="&#x2019;" u2="&#x2014;" k="85" />
<hkern u1="&#x2019;" u2="&#x2013;" k="85" />
<hkern u1="&#x2019;" u2="&#x178;" k="-46" />
<hkern u1="&#x2019;" u2="&#x153;" k="93" />
<hkern u1="&#x2019;" u2="&#x119;" k="93" />
<hkern u1="&#x2019;" u2="&#x107;" k="93" />
<hkern u1="&#x2019;" u2="&#x105;" k="93" />
<hkern u1="&#x2019;" u2="&#x104;" k="175" />
<hkern u1="&#x2019;" u2="&#xf8;" k="93" />
<hkern u1="&#x2019;" u2="&#xf6;" k="93" />
<hkern u1="&#x2019;" u2="&#xf5;" k="93" />
<hkern u1="&#x2019;" u2="&#xf4;" k="93" />
<hkern u1="&#x2019;" u2="&#xf3;" k="93" />
<hkern u1="&#x2019;" u2="&#xf2;" k="93" />
<hkern u1="&#x2019;" u2="&#xf0;" k="93" />
<hkern u1="&#x2019;" u2="&#xeb;" k="93" />
<hkern u1="&#x2019;" u2="&#xea;" k="93" />
<hkern u1="&#x2019;" u2="&#xe9;" k="93" />
<hkern u1="&#x2019;" u2="&#xe8;" k="93" />
<hkern u1="&#x2019;" u2="&#xe7;" k="93" />
<hkern u1="&#x2019;" u2="&#xe6;" k="93" />
<hkern u1="&#x2019;" u2="&#xe5;" k="93" />
<hkern u1="&#x2019;" u2="&#xe4;" k="93" />
<hkern u1="&#x2019;" u2="&#xe3;" k="93" />
<hkern u1="&#x2019;" u2="&#xe2;" k="93" />
<hkern u1="&#x2019;" u2="&#xe1;" k="93" />
<hkern u1="&#x2019;" u2="&#xe0;" k="93" />
<hkern u1="&#x2019;" u2="&#xdd;" k="-46" />
<hkern u1="&#x2019;" u2="&#xc6;" k="175" />
<hkern u1="&#x2019;" u2="&#xc5;" k="175" />
<hkern u1="&#x2019;" u2="&#xc4;" k="175" />
<hkern u1="&#x2019;" u2="&#xc3;" k="175" />
<hkern u1="&#x2019;" u2="&#xc2;" k="175" />
<hkern u1="&#x2019;" u2="&#xc1;" k="175" />
<hkern u1="&#x2019;" u2="&#xc0;" k="175" />
<hkern u1="&#x2019;" u2="&#xbb;" k="85" />
<hkern u1="&#x2019;" u2="&#xb7;" k="85" />
<hkern u1="&#x2019;" u2="&#xab;" k="85" />
<hkern u1="&#x2019;" u2="q" k="93" />
<hkern u1="&#x2019;" u2="o" k="93" />
<hkern u1="&#x2019;" u2="e" k="93" />
<hkern u1="&#x2019;" u2="d" k="93" />
<hkern u1="&#x2019;" u2="c" k="93" />
<hkern u1="&#x2019;" u2="a" k="93" />
<hkern u1="&#x2019;" u2="\" k="-55" />
<hkern u1="&#x2019;" u2="Y" k="-46" />
<hkern u1="&#x2019;" u2="W" k="-49" />
<hkern u1="&#x2019;" u2="V" k="-55" />
<hkern u1="&#x2019;" u2="A" k="175" />
<hkern u1="&#x2019;" u2="&#x2f;" k="175" />
<hkern u1="&#x2019;" u2="&#x2e;" k="94" />
<hkern u1="&#x2019;" u2="&#x2d;" k="85" />
<hkern u1="&#x2019;" u2="&#x2c;" k="94" />
<hkern u1="&#x2019;" u2="&#x26;" k="175" />
<hkern u1="&#x201a;" u2="&#x203a;" k="202" />
<hkern u1="&#x201a;" u2="&#x2039;" k="202" />
<hkern u1="&#x201a;" u2="&#x2022;" k="202" />
<hkern u1="&#x201a;" u2="&#x201d;" k="123" />
<hkern u1="&#x201a;" u2="&#x201c;" k="123" />
<hkern u1="&#x201a;" u2="&#x2019;" k="123" />
<hkern u1="&#x201a;" u2="&#x2018;" k="123" />
<hkern u1="&#x201a;" u2="&#x2014;" k="202" />
<hkern u1="&#x201a;" u2="&#x2013;" k="202" />
<hkern u1="&#x201a;" u2="&#x178;" k="202" />
<hkern u1="&#x201a;" u2="&#x152;" k="56" />
<hkern u1="&#x201a;" u2="&#x106;" k="56" />
<hkern u1="&#x201a;" u2="&#xff;" k="145" />
<hkern u1="&#x201a;" u2="&#xfd;" k="145" />
<hkern u1="&#x201a;" u2="&#xdd;" k="202" />
<hkern u1="&#x201a;" u2="&#xd8;" k="56" />
<hkern u1="&#x201a;" u2="&#xd6;" k="56" />
<hkern u1="&#x201a;" u2="&#xd5;" k="56" />
<hkern u1="&#x201a;" u2="&#xd4;" k="56" />
<hkern u1="&#x201a;" u2="&#xd3;" k="56" />
<hkern u1="&#x201a;" u2="&#xd2;" k="56" />
<hkern u1="&#x201a;" u2="&#xc7;" k="56" />
<hkern u1="&#x201a;" u2="&#xbb;" k="202" />
<hkern u1="&#x201a;" u2="&#xba;" k="123" />
<hkern u1="&#x201a;" u2="&#xb7;" k="202" />
<hkern u1="&#x201a;" u2="&#xb0;" k="123" />
<hkern u1="&#x201a;" u2="&#xae;" k="56" />
<hkern u1="&#x201a;" u2="&#xab;" k="202" />
<hkern u1="&#x201a;" u2="&#xaa;" k="123" />
<hkern u1="&#x201a;" u2="&#xa9;" k="56" />
<hkern u1="&#x201a;" u2="y" k="135" />
<hkern u1="&#x201a;" u2="w" k="74" />
<hkern u1="&#x201a;" u2="v" k="145" />
<hkern u1="&#x201a;" u2="\" k="217" />
<hkern u1="&#x201a;" u2="Y" k="202" />
<hkern u1="&#x201a;" u2="W" k="135" />
<hkern u1="&#x201a;" u2="V" k="217" />
<hkern u1="&#x201a;" u2="T" k="208" />
<hkern u1="&#x201a;" u2="Q" k="56" />
<hkern u1="&#x201a;" u2="O" k="56" />
<hkern u1="&#x201a;" u2="G" k="56" />
<hkern u1="&#x201a;" u2="C" k="56" />
<hkern u1="&#x201a;" u2="&#x40;" k="56" />
<hkern u1="&#x201a;" u2="&#x2d;" k="202" />
<hkern u1="&#x201a;" u2="&#x2a;" k="123" />
<hkern u1="&#x201a;" u2="&#x27;" k="123" />
<hkern u1="&#x201a;" u2="&#x22;" k="123" />
<hkern u1="&#x201c;" u2="&#x2206;" k="175" />
<hkern u1="&#x201c;" u2="&#x203a;" k="85" />
<hkern u1="&#x201c;" u2="&#x2039;" k="85" />
<hkern u1="&#x201c;" u2="&#x2026;" k="94" />
<hkern u1="&#x201c;" u2="&#x2022;" k="85" />
<hkern u1="&#x201c;" u2="&#x201e;" k="94" />
<hkern u1="&#x201c;" u2="&#x201a;" k="94" />
<hkern u1="&#x201c;" u2="&#x2014;" k="85" />
<hkern u1="&#x201c;" u2="&#x2013;" k="85" />
<hkern u1="&#x201c;" u2="&#x178;" k="-46" />
<hkern u1="&#x201c;" u2="&#x153;" k="93" />
<hkern u1="&#x201c;" u2="&#x119;" k="93" />
<hkern u1="&#x201c;" u2="&#x107;" k="93" />
<hkern u1="&#x201c;" u2="&#x105;" k="93" />
<hkern u1="&#x201c;" u2="&#x104;" k="175" />
<hkern u1="&#x201c;" u2="&#xf8;" k="93" />
<hkern u1="&#x201c;" u2="&#xf6;" k="93" />
<hkern u1="&#x201c;" u2="&#xf5;" k="93" />
<hkern u1="&#x201c;" u2="&#xf4;" k="93" />
<hkern u1="&#x201c;" u2="&#xf3;" k="93" />
<hkern u1="&#x201c;" u2="&#xf2;" k="93" />
<hkern u1="&#x201c;" u2="&#xf0;" k="93" />
<hkern u1="&#x201c;" u2="&#xeb;" k="93" />
<hkern u1="&#x201c;" u2="&#xea;" k="93" />
<hkern u1="&#x201c;" u2="&#xe9;" k="93" />
<hkern u1="&#x201c;" u2="&#xe8;" k="93" />
<hkern u1="&#x201c;" u2="&#xe7;" k="93" />
<hkern u1="&#x201c;" u2="&#xe6;" k="93" />
<hkern u1="&#x201c;" u2="&#xe5;" k="93" />
<hkern u1="&#x201c;" u2="&#xe4;" k="93" />
<hkern u1="&#x201c;" u2="&#xe3;" k="93" />
<hkern u1="&#x201c;" u2="&#xe2;" k="93" />
<hkern u1="&#x201c;" u2="&#xe1;" k="93" />
<hkern u1="&#x201c;" u2="&#xe0;" k="93" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-46" />
<hkern u1="&#x201c;" u2="&#xc6;" k="175" />
<hkern u1="&#x201c;" u2="&#xc5;" k="175" />
<hkern u1="&#x201c;" u2="&#xc4;" k="175" />
<hkern u1="&#x201c;" u2="&#xc3;" k="175" />
<hkern u1="&#x201c;" u2="&#xc2;" k="175" />
<hkern u1="&#x201c;" u2="&#xc1;" k="175" />
<hkern u1="&#x201c;" u2="&#xc0;" k="175" />
<hkern u1="&#x201c;" u2="&#xbb;" k="85" />
<hkern u1="&#x201c;" u2="&#xb7;" k="85" />
<hkern u1="&#x201c;" u2="&#xab;" k="85" />
<hkern u1="&#x201c;" u2="q" k="93" />
<hkern u1="&#x201c;" u2="o" k="93" />
<hkern u1="&#x201c;" u2="e" k="93" />
<hkern u1="&#x201c;" u2="d" k="93" />
<hkern u1="&#x201c;" u2="c" k="93" />
<hkern u1="&#x201c;" u2="a" k="93" />
<hkern u1="&#x201c;" u2="\" k="-55" />
<hkern u1="&#x201c;" u2="Y" k="-46" />
<hkern u1="&#x201c;" u2="W" k="-49" />
<hkern u1="&#x201c;" u2="V" k="-55" />
<hkern u1="&#x201c;" u2="A" k="175" />
<hkern u1="&#x201c;" u2="&#x2f;" k="175" />
<hkern u1="&#x201c;" u2="&#x2e;" k="94" />
<hkern u1="&#x201c;" u2="&#x2d;" k="85" />
<hkern u1="&#x201c;" u2="&#x2c;" k="94" />
<hkern u1="&#x201c;" u2="&#x26;" k="175" />
<hkern u1="&#x201d;" u2="&#x2206;" k="175" />
<hkern u1="&#x201d;" u2="&#x203a;" k="85" />
<hkern u1="&#x201d;" u2="&#x2039;" k="85" />
<hkern u1="&#x201d;" u2="&#x2026;" k="94" />
<hkern u1="&#x201d;" u2="&#x2022;" k="85" />
<hkern u1="&#x201d;" u2="&#x201e;" k="94" />
<hkern u1="&#x201d;" u2="&#x201a;" k="94" />
<hkern u1="&#x201d;" u2="&#x2014;" k="85" />
<hkern u1="&#x201d;" u2="&#x2013;" k="85" />
<hkern u1="&#x201d;" u2="&#x178;" k="-46" />
<hkern u1="&#x201d;" u2="&#x153;" k="93" />
<hkern u1="&#x201d;" u2="&#x119;" k="93" />
<hkern u1="&#x201d;" u2="&#x107;" k="93" />
<hkern u1="&#x201d;" u2="&#x105;" k="93" />
<hkern u1="&#x201d;" u2="&#x104;" k="175" />
<hkern u1="&#x201d;" u2="&#xf8;" k="93" />
<hkern u1="&#x201d;" u2="&#xf6;" k="93" />
<hkern u1="&#x201d;" u2="&#xf5;" k="93" />
<hkern u1="&#x201d;" u2="&#xf4;" k="93" />
<hkern u1="&#x201d;" u2="&#xf3;" k="93" />
<hkern u1="&#x201d;" u2="&#xf2;" k="93" />
<hkern u1="&#x201d;" u2="&#xf0;" k="93" />
<hkern u1="&#x201d;" u2="&#xeb;" k="93" />
<hkern u1="&#x201d;" u2="&#xea;" k="93" />
<hkern u1="&#x201d;" u2="&#xe9;" k="93" />
<hkern u1="&#x201d;" u2="&#xe8;" k="93" />
<hkern u1="&#x201d;" u2="&#xe7;" k="93" />
<hkern u1="&#x201d;" u2="&#xe6;" k="93" />
<hkern u1="&#x201d;" u2="&#xe5;" k="93" />
<hkern u1="&#x201d;" u2="&#xe4;" k="93" />
<hkern u1="&#x201d;" u2="&#xe3;" k="93" />
<hkern u1="&#x201d;" u2="&#xe2;" k="93" />
<hkern u1="&#x201d;" u2="&#xe1;" k="93" />
<hkern u1="&#x201d;" u2="&#xe0;" k="93" />
<hkern u1="&#x201d;" u2="&#xdd;" k="-46" />
<hkern u1="&#x201d;" u2="&#xc6;" k="175" />
<hkern u1="&#x201d;" u2="&#xc5;" k="175" />
<hkern u1="&#x201d;" u2="&#xc4;" k="175" />
<hkern u1="&#x201d;" u2="&#xc3;" k="175" />
<hkern u1="&#x201d;" u2="&#xc2;" k="175" />
<hkern u1="&#x201d;" u2="&#xc1;" k="175" />
<hkern u1="&#x201d;" u2="&#xc0;" k="175" />
<hkern u1="&#x201d;" u2="&#xbb;" k="85" />
<hkern u1="&#x201d;" u2="&#xb7;" k="85" />
<hkern u1="&#x201d;" u2="&#xab;" k="85" />
<hkern u1="&#x201d;" u2="q" k="93" />
<hkern u1="&#x201d;" u2="o" k="93" />
<hkern u1="&#x201d;" u2="e" k="93" />
<hkern u1="&#x201d;" u2="d" k="93" />
<hkern u1="&#x201d;" u2="c" k="93" />
<hkern u1="&#x201d;" u2="a" k="93" />
<hkern u1="&#x201d;" u2="\" k="-55" />
<hkern u1="&#x201d;" u2="Y" k="-46" />
<hkern u1="&#x201d;" u2="W" k="-49" />
<hkern u1="&#x201d;" u2="V" k="-55" />
<hkern u1="&#x201d;" u2="A" k="175" />
<hkern u1="&#x201d;" u2="&#x2f;" k="175" />
<hkern u1="&#x201d;" u2="&#x2e;" k="94" />
<hkern u1="&#x201d;" u2="&#x2d;" k="85" />
<hkern u1="&#x201d;" u2="&#x2c;" k="94" />
<hkern u1="&#x201d;" u2="&#x26;" k="175" />
<hkern u1="&#x201e;" u2="&#x203a;" k="202" />
<hkern u1="&#x201e;" u2="&#x2039;" k="202" />
<hkern u1="&#x201e;" u2="&#x2022;" k="202" />
<hkern u1="&#x201e;" u2="&#x201d;" k="123" />
<hkern u1="&#x201e;" u2="&#x201c;" k="123" />
<hkern u1="&#x201e;" u2="&#x2019;" k="123" />
<hkern u1="&#x201e;" u2="&#x2018;" k="123" />
<hkern u1="&#x201e;" u2="&#x2014;" k="202" />
<hkern u1="&#x201e;" u2="&#x2013;" k="202" />
<hkern u1="&#x201e;" u2="&#x178;" k="202" />
<hkern u1="&#x201e;" u2="&#x152;" k="56" />
<hkern u1="&#x201e;" u2="&#x106;" k="56" />
<hkern u1="&#x201e;" u2="&#xff;" k="145" />
<hkern u1="&#x201e;" u2="&#xfd;" k="145" />
<hkern u1="&#x201e;" u2="&#xdd;" k="202" />
<hkern u1="&#x201e;" u2="&#xd8;" k="56" />
<hkern u1="&#x201e;" u2="&#xd6;" k="56" />
<hkern u1="&#x201e;" u2="&#xd5;" k="56" />
<hkern u1="&#x201e;" u2="&#xd4;" k="56" />
<hkern u1="&#x201e;" u2="&#xd3;" k="56" />
<hkern u1="&#x201e;" u2="&#xd2;" k="56" />
<hkern u1="&#x201e;" u2="&#xc7;" k="56" />
<hkern u1="&#x201e;" u2="&#xbb;" k="202" />
<hkern u1="&#x201e;" u2="&#xba;" k="123" />
<hkern u1="&#x201e;" u2="&#xb7;" k="202" />
<hkern u1="&#x201e;" u2="&#xb0;" k="123" />
<hkern u1="&#x201e;" u2="&#xae;" k="56" />
<hkern u1="&#x201e;" u2="&#xab;" k="202" />
<hkern u1="&#x201e;" u2="&#xaa;" k="123" />
<hkern u1="&#x201e;" u2="&#xa9;" k="56" />
<hkern u1="&#x201e;" u2="y" k="135" />
<hkern u1="&#x201e;" u2="w" k="74" />
<hkern u1="&#x201e;" u2="v" k="145" />
<hkern u1="&#x201e;" u2="\" k="217" />
<hkern u1="&#x201e;" u2="Y" k="202" />
<hkern u1="&#x201e;" u2="W" k="135" />
<hkern u1="&#x201e;" u2="V" k="217" />
<hkern u1="&#x201e;" u2="T" k="208" />
<hkern u1="&#x201e;" u2="Q" k="56" />
<hkern u1="&#x201e;" u2="O" k="56" />
<hkern u1="&#x201e;" u2="G" k="56" />
<hkern u1="&#x201e;" u2="C" k="56" />
<hkern u1="&#x201e;" u2="&#x40;" k="56" />
<hkern u1="&#x201e;" u2="&#x2d;" k="202" />
<hkern u1="&#x201e;" u2="&#x2a;" k="123" />
<hkern u1="&#x201e;" u2="&#x27;" k="123" />
<hkern u1="&#x201e;" u2="&#x22;" k="123" />
<hkern u1="&#x2022;" u2="&#x2206;" k="53" />
<hkern u1="&#x2022;" u2="&#x2026;" k="166" />
<hkern u1="&#x2022;" u2="&#x201e;" k="166" />
<hkern u1="&#x2022;" u2="&#x201d;" k="85" />
<hkern u1="&#x2022;" u2="&#x201c;" k="85" />
<hkern u1="&#x2022;" u2="&#x201a;" k="166" />
<hkern u1="&#x2022;" u2="&#x2019;" k="85" />
<hkern u1="&#x2022;" u2="&#x2018;" k="85" />
<hkern u1="&#x2022;" u2="&#x17d;" k="56" />
<hkern u1="&#x2022;" u2="&#x17b;" k="56" />
<hkern u1="&#x2022;" u2="&#x179;" k="56" />
<hkern u1="&#x2022;" u2="&#x178;" k="197" />
<hkern u1="&#x2022;" u2="&#x104;" k="53" />
<hkern u1="&#x2022;" u2="&#xdd;" k="197" />
<hkern u1="&#x2022;" u2="&#xc6;" k="53" />
<hkern u1="&#x2022;" u2="&#xc5;" k="53" />
<hkern u1="&#x2022;" u2="&#xc4;" k="53" />
<hkern u1="&#x2022;" u2="&#xc3;" k="53" />
<hkern u1="&#x2022;" u2="&#xc2;" k="53" />
<hkern u1="&#x2022;" u2="&#xc1;" k="53" />
<hkern u1="&#x2022;" u2="&#xc0;" k="53" />
<hkern u1="&#x2022;" u2="&#xba;" k="85" />
<hkern u1="&#x2022;" u2="&#xb0;" k="85" />
<hkern u1="&#x2022;" u2="&#xaa;" k="85" />
<hkern u1="&#x2022;" u2="\" k="125" />
<hkern u1="&#x2022;" u2="Z" k="56" />
<hkern u1="&#x2022;" u2="Y" k="197" />
<hkern u1="&#x2022;" u2="X" k="72" />
<hkern u1="&#x2022;" u2="V" k="125" />
<hkern u1="&#x2022;" u2="T" k="196" />
<hkern u1="&#x2022;" u2="A" k="53" />
<hkern u1="&#x2022;" u2="&#x2f;" k="53" />
<hkern u1="&#x2022;" u2="&#x2e;" k="166" />
<hkern u1="&#x2022;" u2="&#x2c;" k="166" />
<hkern u1="&#x2022;" u2="&#x2a;" k="85" />
<hkern u1="&#x2022;" u2="&#x27;" k="85" />
<hkern u1="&#x2022;" u2="&#x26;" k="53" />
<hkern u1="&#x2022;" u2="&#x22;" k="85" />
<hkern u1="&#x2026;" u2="&#x203a;" k="202" />
<hkern u1="&#x2026;" u2="&#x2039;" k="202" />
<hkern u1="&#x2026;" u2="&#x2022;" k="202" />
<hkern u1="&#x2026;" u2="&#x201d;" k="123" />
<hkern u1="&#x2026;" u2="&#x201c;" k="123" />
<hkern u1="&#x2026;" u2="&#x2019;" k="123" />
<hkern u1="&#x2026;" u2="&#x2018;" k="123" />
<hkern u1="&#x2026;" u2="&#x2014;" k="202" />
<hkern u1="&#x2026;" u2="&#x2013;" k="202" />
<hkern u1="&#x2026;" u2="&#x178;" k="202" />
<hkern u1="&#x2026;" u2="&#x152;" k="56" />
<hkern u1="&#x2026;" u2="&#x106;" k="56" />
<hkern u1="&#x2026;" u2="&#xff;" k="145" />
<hkern u1="&#x2026;" u2="&#xfd;" k="145" />
<hkern u1="&#x2026;" u2="&#xdd;" k="202" />
<hkern u1="&#x2026;" u2="&#xd8;" k="56" />
<hkern u1="&#x2026;" u2="&#xd6;" k="56" />
<hkern u1="&#x2026;" u2="&#xd5;" k="56" />
<hkern u1="&#x2026;" u2="&#xd4;" k="56" />
<hkern u1="&#x2026;" u2="&#xd3;" k="56" />
<hkern u1="&#x2026;" u2="&#xd2;" k="56" />
<hkern u1="&#x2026;" u2="&#xc7;" k="56" />
<hkern u1="&#x2026;" u2="&#xbb;" k="202" />
<hkern u1="&#x2026;" u2="&#xba;" k="123" />
<hkern u1="&#x2026;" u2="&#xb7;" k="202" />
<hkern u1="&#x2026;" u2="&#xb0;" k="123" />
<hkern u1="&#x2026;" u2="&#xae;" k="56" />
<hkern u1="&#x2026;" u2="&#xab;" k="202" />
<hkern u1="&#x2026;" u2="&#xaa;" k="123" />
<hkern u1="&#x2026;" u2="&#xa9;" k="56" />
<hkern u1="&#x2026;" u2="y" k="135" />
<hkern u1="&#x2026;" u2="w" k="74" />
<hkern u1="&#x2026;" u2="v" k="145" />
<hkern u1="&#x2026;" u2="\" k="217" />
<hkern u1="&#x2026;" u2="Y" k="202" />
<hkern u1="&#x2026;" u2="W" k="135" />
<hkern u1="&#x2026;" u2="V" k="217" />
<hkern u1="&#x2026;" u2="T" k="208" />
<hkern u1="&#x2026;" u2="Q" k="56" />
<hkern u1="&#x2026;" u2="O" k="56" />
<hkern u1="&#x2026;" u2="G" k="56" />
<hkern u1="&#x2026;" u2="C" k="56" />
<hkern u1="&#x2026;" u2="&#x40;" k="56" />
<hkern u1="&#x2026;" u2="&#x2d;" k="202" />
<hkern u1="&#x2026;" u2="&#x2a;" k="123" />
<hkern u1="&#x2026;" u2="&#x27;" k="123" />
<hkern u1="&#x2026;" u2="&#x22;" k="123" />
<hkern u1="&#x2039;" u2="&#x2206;" k="53" />
<hkern u1="&#x2039;" u2="&#x2026;" k="166" />
<hkern u1="&#x2039;" u2="&#x201e;" k="166" />
<hkern u1="&#x2039;" u2="&#x201d;" k="85" />
<hkern u1="&#x2039;" u2="&#x201c;" k="85" />
<hkern u1="&#x2039;" u2="&#x201a;" k="166" />
<hkern u1="&#x2039;" u2="&#x2019;" k="85" />
<hkern u1="&#x2039;" u2="&#x2018;" k="85" />
<hkern u1="&#x2039;" u2="&#x17d;" k="56" />
<hkern u1="&#x2039;" u2="&#x17b;" k="56" />
<hkern u1="&#x2039;" u2="&#x179;" k="56" />
<hkern u1="&#x2039;" u2="&#x178;" k="197" />
<hkern u1="&#x2039;" u2="&#x104;" k="53" />
<hkern u1="&#x2039;" u2="&#xdd;" k="197" />
<hkern u1="&#x2039;" u2="&#xc6;" k="53" />
<hkern u1="&#x2039;" u2="&#xc5;" k="53" />
<hkern u1="&#x2039;" u2="&#xc4;" k="53" />
<hkern u1="&#x2039;" u2="&#xc3;" k="53" />
<hkern u1="&#x2039;" u2="&#xc2;" k="53" />
<hkern u1="&#x2039;" u2="&#xc1;" k="53" />
<hkern u1="&#x2039;" u2="&#xc0;" k="53" />
<hkern u1="&#x2039;" u2="&#xba;" k="85" />
<hkern u1="&#x2039;" u2="&#xb0;" k="85" />
<hkern u1="&#x2039;" u2="&#xaa;" k="85" />
<hkern u1="&#x2039;" u2="\" k="125" />
<hkern u1="&#x2039;" u2="Z" k="56" />
<hkern u1="&#x2039;" u2="Y" k="197" />
<hkern u1="&#x2039;" u2="X" k="72" />
<hkern u1="&#x2039;" u2="V" k="125" />
<hkern u1="&#x2039;" u2="T" k="196" />
<hkern u1="&#x2039;" u2="A" k="53" />
<hkern u1="&#x2039;" u2="&#x2f;" k="53" />
<hkern u1="&#x2039;" u2="&#x2e;" k="166" />
<hkern u1="&#x2039;" u2="&#x2c;" k="166" />
<hkern u1="&#x2039;" u2="&#x2a;" k="85" />
<hkern u1="&#x2039;" u2="&#x27;" k="85" />
<hkern u1="&#x2039;" u2="&#x26;" k="53" />
<hkern u1="&#x2039;" u2="&#x22;" k="85" />
<hkern u1="&#x203a;" u2="&#x2206;" k="53" />
<hkern u1="&#x203a;" u2="&#x2026;" k="166" />
<hkern u1="&#x203a;" u2="&#x201e;" k="166" />
<hkern u1="&#x203a;" u2="&#x201d;" k="85" />
<hkern u1="&#x203a;" u2="&#x201c;" k="85" />
<hkern u1="&#x203a;" u2="&#x201a;" k="166" />
<hkern u1="&#x203a;" u2="&#x2019;" k="85" />
<hkern u1="&#x203a;" u2="&#x2018;" k="85" />
<hkern u1="&#x203a;" u2="&#x17d;" k="56" />
<hkern u1="&#x203a;" u2="&#x17b;" k="56" />
<hkern u1="&#x203a;" u2="&#x179;" k="56" />
<hkern u1="&#x203a;" u2="&#x178;" k="197" />
<hkern u1="&#x203a;" u2="&#x104;" k="53" />
<hkern u1="&#x203a;" u2="&#xdd;" k="197" />
<hkern u1="&#x203a;" u2="&#xc6;" k="53" />
<hkern u1="&#x203a;" u2="&#xc5;" k="53" />
<hkern u1="&#x203a;" u2="&#xc4;" k="53" />
<hkern u1="&#x203a;" u2="&#xc3;" k="53" />
<hkern u1="&#x203a;" u2="&#xc2;" k="53" />
<hkern u1="&#x203a;" u2="&#xc1;" k="53" />
<hkern u1="&#x203a;" u2="&#xc0;" k="53" />
<hkern u1="&#x203a;" u2="&#xba;" k="85" />
<hkern u1="&#x203a;" u2="&#xb0;" k="85" />
<hkern u1="&#x203a;" u2="&#xaa;" k="85" />
<hkern u1="&#x203a;" u2="\" k="125" />
<hkern u1="&#x203a;" u2="Z" k="56" />
<hkern u1="&#x203a;" u2="Y" k="197" />
<hkern u1="&#x203a;" u2="X" k="72" />
<hkern u1="&#x203a;" u2="V" k="125" />
<hkern u1="&#x203a;" u2="T" k="196" />
<hkern u1="&#x203a;" u2="A" k="53" />
<hkern u1="&#x203a;" u2="&#x2f;" k="53" />
<hkern u1="&#x203a;" u2="&#x2e;" k="166" />
<hkern u1="&#x203a;" u2="&#x2c;" k="166" />
<hkern u1="&#x203a;" u2="&#x2a;" k="85" />
<hkern u1="&#x203a;" u2="&#x27;" k="85" />
<hkern u1="&#x203a;" u2="&#x26;" k="53" />
<hkern u1="&#x203a;" u2="&#x22;" k="85" />
<hkern u1="&#x2122;" u2="&#x2206;" k="175" />
<hkern u1="&#x2122;" u2="&#x203a;" k="85" />
<hkern u1="&#x2122;" u2="&#x2039;" k="85" />
<hkern u1="&#x2122;" u2="&#x2026;" k="94" />
<hkern u1="&#x2122;" u2="&#x2022;" k="85" />
<hkern u1="&#x2122;" u2="&#x201e;" k="94" />
<hkern u1="&#x2122;" u2="&#x201a;" k="94" />
<hkern u1="&#x2122;" u2="&#x2014;" k="85" />
<hkern u1="&#x2122;" u2="&#x2013;" k="85" />
<hkern u1="&#x2122;" u2="&#x178;" k="-46" />
<hkern u1="&#x2122;" u2="&#x153;" k="93" />
<hkern u1="&#x2122;" u2="&#x119;" k="93" />
<hkern u1="&#x2122;" u2="&#x107;" k="93" />
<hkern u1="&#x2122;" u2="&#x105;" k="93" />
<hkern u1="&#x2122;" u2="&#x104;" k="175" />
<hkern u1="&#x2122;" u2="&#xf8;" k="93" />
<hkern u1="&#x2122;" u2="&#xf6;" k="93" />
<hkern u1="&#x2122;" u2="&#xf5;" k="93" />
<hkern u1="&#x2122;" u2="&#xf4;" k="93" />
<hkern u1="&#x2122;" u2="&#xf3;" k="93" />
<hkern u1="&#x2122;" u2="&#xf2;" k="93" />
<hkern u1="&#x2122;" u2="&#xf0;" k="93" />
<hkern u1="&#x2122;" u2="&#xeb;" k="93" />
<hkern u1="&#x2122;" u2="&#xea;" k="93" />
<hkern u1="&#x2122;" u2="&#xe9;" k="93" />
<hkern u1="&#x2122;" u2="&#xe8;" k="93" />
<hkern u1="&#x2122;" u2="&#xe7;" k="93" />
<hkern u1="&#x2122;" u2="&#xe6;" k="93" />
<hkern u1="&#x2122;" u2="&#xe5;" k="93" />
<hkern u1="&#x2122;" u2="&#xe4;" k="93" />
<hkern u1="&#x2122;" u2="&#xe3;" k="93" />
<hkern u1="&#x2122;" u2="&#xe2;" k="93" />
<hkern u1="&#x2122;" u2="&#xe1;" k="93" />
<hkern u1="&#x2122;" u2="&#xe0;" k="93" />
<hkern u1="&#x2122;" u2="&#xdd;" k="-46" />
<hkern u1="&#x2122;" u2="&#xc6;" k="175" />
<hkern u1="&#x2122;" u2="&#xc5;" k="175" />
<hkern u1="&#x2122;" u2="&#xc4;" k="175" />
<hkern u1="&#x2122;" u2="&#xc3;" k="175" />
<hkern u1="&#x2122;" u2="&#xc2;" k="175" />
<hkern u1="&#x2122;" u2="&#xc1;" k="175" />
<hkern u1="&#x2122;" u2="&#xc0;" k="175" />
<hkern u1="&#x2122;" u2="&#xbb;" k="85" />
<hkern u1="&#x2122;" u2="&#xb7;" k="85" />
<hkern u1="&#x2122;" u2="&#xab;" k="85" />
<hkern u1="&#x2122;" u2="q" k="93" />
<hkern u1="&#x2122;" u2="o" k="93" />
<hkern u1="&#x2122;" u2="e" k="93" />
<hkern u1="&#x2122;" u2="d" k="93" />
<hkern u1="&#x2122;" u2="c" k="93" />
<hkern u1="&#x2122;" u2="a" k="93" />
<hkern u1="&#x2122;" u2="\" k="-55" />
<hkern u1="&#x2122;" u2="Y" k="-46" />
<hkern u1="&#x2122;" u2="W" k="-49" />
<hkern u1="&#x2122;" u2="V" k="-55" />
<hkern u1="&#x2122;" u2="A" k="175" />
<hkern u1="&#x2122;" u2="&#x2f;" k="175" />
<hkern u1="&#x2122;" u2="&#x2e;" k="94" />
<hkern u1="&#x2122;" u2="&#x2d;" k="85" />
<hkern u1="&#x2122;" u2="&#x2c;" k="94" />
<hkern u1="&#x2122;" u2="&#x26;" k="175" />
<hkern u1="&#x2206;" u2="&#x203a;" k="53" />
<hkern u1="&#x2206;" u2="&#x2039;" k="53" />
<hkern u1="&#x2206;" u2="&#x2022;" k="53" />
<hkern u1="&#x2206;" u2="&#x201d;" k="186" />
<hkern u1="&#x2206;" u2="&#x201c;" k="186" />
<hkern u1="&#x2206;" u2="&#x2019;" k="186" />
<hkern u1="&#x2206;" u2="&#x2018;" k="186" />
<hkern u1="&#x2206;" u2="&#x2014;" k="53" />
<hkern u1="&#x2206;" u2="&#x2013;" k="53" />
<hkern u1="&#x2206;" u2="&#x178;" k="156" />
<hkern u1="&#x2206;" u2="&#x152;" k="48" />
<hkern u1="&#x2206;" u2="&#x106;" k="48" />
<hkern u1="&#x2206;" u2="&#xff;" k="48" />
<hkern u1="&#x2206;" u2="&#xfd;" k="48" />
<hkern u1="&#x2206;" u2="&#xdd;" k="156" />
<hkern u1="&#x2206;" u2="&#xdc;" k="41" />
<hkern u1="&#x2206;" u2="&#xdb;" k="41" />
<hkern u1="&#x2206;" u2="&#xda;" k="41" />
<hkern u1="&#x2206;" u2="&#xd9;" k="41" />
<hkern u1="&#x2206;" u2="&#xd8;" k="48" />
<hkern u1="&#x2206;" u2="&#xd6;" k="48" />
<hkern u1="&#x2206;" u2="&#xd5;" k="48" />
<hkern u1="&#x2206;" u2="&#xd4;" k="48" />
<hkern u1="&#x2206;" u2="&#xd3;" k="48" />
<hkern u1="&#x2206;" u2="&#xd2;" k="48" />
<hkern u1="&#x2206;" u2="&#xc7;" k="48" />
<hkern u1="&#x2206;" u2="&#xbb;" k="53" />
<hkern u1="&#x2206;" u2="&#xba;" k="186" />
<hkern u1="&#x2206;" u2="&#xb7;" k="53" />
<hkern u1="&#x2206;" u2="&#xb0;" k="186" />
<hkern u1="&#x2206;" u2="&#xae;" k="48" />
<hkern u1="&#x2206;" u2="&#xab;" k="53" />
<hkern u1="&#x2206;" u2="&#xaa;" k="186" />
<hkern u1="&#x2206;" u2="&#xa9;" k="48" />
<hkern u1="&#x2206;" u2="y" k="48" />
<hkern u1="&#x2206;" u2="w" k="33" />
<hkern u1="&#x2206;" u2="v" k="48" />
<hkern u1="&#x2206;" u2="t" k="58" />
<hkern u1="&#x2206;" u2="\" k="117" />
<hkern u1="&#x2206;" u2="Y" k="156" />
<hkern u1="&#x2206;" u2="W" k="85" />
<hkern u1="&#x2206;" u2="V" k="117" />
<hkern u1="&#x2206;" u2="U" k="41" />
<hkern u1="&#x2206;" u2="T" k="135" />
<hkern u1="&#x2206;" u2="Q" k="48" />
<hkern u1="&#x2206;" u2="O" k="48" />
<hkern u1="&#x2206;" u2="J" k="-63" />
<hkern u1="&#x2206;" u2="G" k="48" />
<hkern u1="&#x2206;" u2="C" k="48" />
<hkern u1="&#x2206;" u2="&#x40;" k="48" />
<hkern u1="&#x2206;" u2="&#x2d;" k="53" />
<hkern u1="&#x2206;" u2="&#x2a;" k="186" />
<hkern u1="&#x2206;" u2="&#x27;" k="186" />
<hkern u1="&#x2206;" u2="&#x22;" k="186" />
</font>
</defs></svg> 