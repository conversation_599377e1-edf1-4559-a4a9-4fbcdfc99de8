<footer class="css-4qtnb6" style="height: 200px !important;">
  <div style="width:100%;height:150px;background: #181A20;padding:10px 48px;">
    <div style="width:100%;height:60px;">
      <div style="min-width:100px;height:60px;line-height:60px;margin-right:15px;float: left">
        <span style="color:#fff;font-size:36px;font-weight:600;">{:get_config('webname')}</span>
      </div>
      <div style="min-width:100px;height:60px;line-height:60px;margin-right:15px;float:right" onclick="pop_box_show('privacy')">
        <a href="javascript:void(0)" class="footer-box-span" style="color:#848E9C;">{:L('隐私政策申明')}</a>
      </div>
      <div style="min-width:100px;height:60px;line-height:60px;margin-right:15px;float:right" onclick="pop_box_show('service')">
        <a href="javascript:void(0)" class="footer-box-span" style="color:#848E9C;">{:L('服务条款')}</a>
      </div>
      <div style="min-width:100px;height:60px;line-height:60px;margin-right:15px;float:right" onclick="pop_box_show('msb')">
        <a href="javascript:void(0)" class="footer-box-span" style="color:#848E9C;">{:L('MSB证书')}</a>
      </div>
      <div style="min-width:100px;height:60px;line-height:60px;margin-right:15px;float:right" onclick="pop_box_show('about')">
        <a href="javascript:void(0)" class="footer-box-span" style="color:#848E9C;">{:L('关于我们')}</a>
      </div>
    </div>
    <div style="width:100%;height:60px;line-height:90px;text-align:center;border-top:1px solid #848E9C;">
      <span style="color:#848E9C;">CopyRight © 2017 - 2022 {:get_config('webname')}. All Rights Reserved.</span>
    </div>
  </div>
</footer>

<div class="pop-box" id="pop-box" style="display:none" onclick="pop_box_hide()">
  <div class="pop-content">
    <div class="pop-content-desc" style="color:#2c3e50 !important;"></div>
  </div>
</div>

<!-- 本地 jQuery，避免外链 https 受阻 -->
<script src="/Public/Home/js/jquery-1.11.3.min.js"></script>
<script src="/Public/Home/static/js/layer/layer.js"></script>

<script>
  // footer 浮层链接的悬停效果（不拦截全站链接点击）
  (function($){
    $(function(){
      $('.footer-box-span').hover(
        function(){ $(this).css('color','#fff'); },
        function(){ $(this).css('color','#848E9C'); }
      ).on('click', function(e){ e.preventDefault(); }); // 只拦截本浮层的点击
    });
  })(jQuery);

  // 避免重复声明导致 SyntaxError：如页面多次包含footer
  window.text_obj = window.text_obj || {
    "privacy" : "<div data-v-73cf4925=\"\"><p>(\"The Company\") understands the importance of including customers' personal information, and will comply with the provisions of the \"Personal Information Protection Act\" (\"Act\"), and endeavor to handle in an appropriate manner in accordance with the provisions of this privacy policy (\"Privacy Policy\") And protect personal information.</p>\n<p>1. Definition</p>\n<p>In the privacy policy, personal information refers to the “personal information” defined in the first paragraph of Article 2 of the Act, that is, information related to living individuals. This information can use the name, date of birth or other information contained in the information (including easy association To other information, so as to identify a specific individual) in the description to identify a specific individual.</p>\n<p>2. Purpose of use</p>\n<p>The company uses customers’ personal information for the following purposes:</p>\n<p>Provide and improve the company's products or services;</p>\n<p>Notify the company's products, services or activities;</p>\n<p>Carry out marketing, survey or analysis to expand the scope of the company's products or services or improve their quality;</p>\n<p>Provide maintenance or support for the company's services;</p>\n<p>Notify the company of revisions to the terms of use, policies, etc. (\"Terms\") related to the services provided.</p>\n<p>Deal with violations of the terms of the company's services;</p>\n<p>Verify the account held by the user;</p>\n<p>Verify the transfer to the user's account; or communicate in emergency situations.</p>\n<p>Any other purpose related to the above purpose.</p>\n<p>3. Change the purpose of use</p>\n<p>The company can change the purpose of use of personal information so that the changed purpose of use can be reasonably regarded as related to the original purpose of use. After the purpose of use is changed, the company shall notify the user or publicly announce the changed purpose of use.</p>\n<p>4. Restrict use</p>\n<p>Without the consent of the relevant customer, the company shall not use personal information beyond the scope necessary for the realization of the purpose of use, unless permitted by the Act or other laws or regulations; however, this provision does not apply to the following situations:</p>\n<p>Use personal information in accordance with laws and regulations;</p>\n<p>The use of personal information is necessary to protect personal life, body or property, and it is difficult to obtain the consent of relevant customers;</p>\n<p>The use of personal information is necessary to improve public health or promote the physical and mental health of children, and it is difficult to obtain the consent of relevant customers;</p>\n<p>Or the use of personal information is necessary for the national government, local government, or individuals or entities entrusted to perform affairs prescribed by laws and regulations, and obtaining the consent of relevant customers may hinder the execution of related affairs.</p>\n<p>5. Appropriate collection</p>\n<p>The company may use appropriate means to collect personal information, but will not use deception or other improper means to collect personal information.</p>\n<p>6. Security Control</p>\n<p>The company fully and appropriately supervises its employees to ensure safe control of personal information to deal with the risk of loss, destruction, tampering or leakage.</p>\n<p>7. If the customer requests the company to correct, add or delete personal information on the grounds that the personal information is contrary to the facts in accordance with the provisions of the Act, the company should first confirm that the request was made by the person in charge, and then immediately make use of the purpose of use Carry out necessary investigations within the necessary scope, and then correct according to the investigation results, add or delete personal information, and notify the customer of the relevant situation (the company decides not to perform the correction, and the company shall notify the customer of the relevant situation when adding or deleting); but According to the provisions of the Act or other laws and regulations, the company is not obliged to perform corrections. When adding or deleting, the provisions do not apply.</p>\n<p>8. Forbidden</p>\n<p>If the customer requests the company to stop using or delete personal information on the grounds that the company’s processing of personal information exceeds the previously announced purpose of use or obtains personal information by deception or other improper means in accordance with the provisions of the Act, and the investigation proves that If the request is reasonable, the company should first confirm that the request was made by the person in charge, and then immediately stop using or delete personal information and notify the customer of the relevant situation; however, the company is not obliged to stop according to the Act or other laws and regulations. This rule does not apply when using or deleting personal information.</p>\n<p>9. Use information recording procedures and other technologies</p>\n<p>The services provided by the company may use information recording procedures or similar technologies. These technologies help the company understand the use of the company's services, etc. and continue to improve services. When the user wants to disable the information recording program, the user can change the settings of the web browser to disable the information recording program. Please note that after the information logging program is disabled, users will no longer be able to use some parts of the service.</p></div>",
    "service" : "<div data-v-73cf4925=\"\"><p>The {:get_config('webname')} Global user agreement is the relevant rights and obligations stipulated by the user and the {:get_config('webname')} Global platform for various services, and is contractual.</p>\n<p>By registering and using this website, the user means that he accepts and agrees to all the conditions and terms of the \"User Agreement\". Both {:get_config('webname')} Global and the user have carefully read all the terms in this \"User Agreement\" and the legal statements and operations issued by {:get_config('webname')} Global The content of the rules, this agreement and the aforementioned terms of service, legal statements and operating rules have been known, understood and accepted, and agreed to use them as the basis for determining the rights and obligations of both parties.</p>\n<p>The {:get_config('webname')} Global \"legal statement\" is an essential part of this agreement. When the user accepts this agreement, it shall be deemed to have accepted the entire content of the {:get_config('webname')} Global \"legal statement\". The content of this agreement includes the body of this agreement and the published or Various rules, statements, and instructions that may be released in the future. All rules, statements, and instructions are an integral part of the agreement and have the same legal effect as the body of the agreement.</p>\n<p>1. User service</p>\n<p>1.1 {:get_config('webname')} Global provides online trading platform services for users to conduct encrypted digital currency transactions through the platform. {:get_config('webname')} Global does not participate in the buying and selling of any digital currency itself as a buyer or seller.</p>\n<p>1.2 Users have the right to browse real-time digital currency market quotations and transaction information on {:get_config('webname')} Global, and have the right to submit digital currency transaction instructions and complete digital currency transactions through the {:get_config('webname')} Global platform.</p>\n<p>1.3 Users have the right to view their information under the platform member account in {:get_config('webname')} Global, and have the right to use the functions provided by {:get_config('webname')} Global to operate.</p>\n<p>1.4 Users have the right to participate in website activities organized by the platform in accordance with the activity rules published by {:get_config('webname')} Global.</p>\n<p>1.5 Users should abide by laws, regulations, regulatory documents and policy requirements, ensure the legitimacy of all funds and digital currency sources in the account, and must not engage in illegal or other damage to the platform or the third party in {:get_config('webname')} Global or use {:get_config('webname')} Global services. The activities of tripartite rights, such as sending or receiving any information that violates laws, regulations, public order and good customs, or infringes on the rights and interests of others, sending or receiving pyramid schemes or other harmful information or speech, using or forging {:get_config('webname')} Global electronics without the authorization of {:get_config('webname')} Global Email header information, etc.</p>\n<p>1.6 Users should abide by laws and regulations, and should properly use and keep their {:get_config('webname')} Global platform account and password, fund transaction password, mobile phone number bound to the registration time, and the security of the mobile phone verification code received by the mobile phone. The user is fully responsible for any operations and consequences performed using his platform account and password, capital password, and mobile phone verification code. When the user discovers that the {:get_config('webname')} Global platform account, password or fund password, verification code is used by a third party without its authorization, or there are other account security issues, the {:get_config('webname')} Global platform will be notified immediately and effectively, and the platform will be required to suspend the service {:get_config('webname')} Global of the platform account. The {:get_config('webname')} Global platform has the right to take action on the user’s request within a reasonable time, but it does not assume any responsibility for the losses that the user has suffered before the {:get_config('webname')} Global platform takes action. The user shall not give, borrow, rent, transfer or otherwise dispose of the {:get_config('webname')} Global platform account to others without the consent of the {:get_config('webname')} Global platform.</p>\n<p>1.7 The user shall abide by the user agreement and other terms of service and operating rules published and updated by the {:get_config('webname')} Global platform from time to time.</p>\n<p>2. Rights and obligations of users …</p></div>",
    "msb" : '<img src="/Public/Home/static/imgs/1.jpeg" style="width:45%"><img src="/Public/Home/static/imgs/2.jpeg" style="width:45%">',
    "about" : "<div data-v-73cf4925=\"\"><p>&nbsp; &nbsp; &nbsp; {:get_config('webname')} cryptocurrency exchange is headquartered in Singapore. …</p></div>"
  };

  function pop_box_show(type) {
    var pop_text = '', pop_title = '';
    if (type == 'privacy') { pop_text = text_obj.privacy; pop_title = 'Privacy'; }
    if (type == 'service') { pop_text = text_obj.service; pop_title = 'Service'; }
    if (type == 'msb')     { pop_text = text_obj.msb;     pop_title = 'Msb'; }
    if (type == 'about')   { pop_text = text_obj.about;   pop_title = 'About'; }
    $('.pop-content-desc').html(pop_text);
    layer.open({
      type: 1, area: ['80%','80vh'], shadeClose: true, title: pop_title,
      content: $('#pop-box')
    });
  }

  function pop_box_hide(){ $('#pop-box').hide(); }

  $('.pop-content').on('click',function(event){
    event.stopPropagation();
  });
</script><script>
jQuery(function($){
  $('a[data-url],a[data-href]').each(function(){
    var u = $(this).attr('data-url') || $(this).attr('data-href');
    if (u && (!this.getAttribute('href') || this.getAttribute('href').match(/^javascript:|^#$/))) {
      $(this).attr('href', u);
    }
  });
});
</script>
<script>
jQuery(function($){
  $(document).on('click', 'a[data-url],a[data-href]', function(e){
    var href = this.getAttribute('href') || '';
    var u = $(this).attr('data-url') || $(this).attr('data-href');
    if (u && (href === '' || href === '#' || href.indexOf('javascript:') === 0)) {
      e.preventDefault();
      window.location.href = u;
    }
  });
});
</script>
