<include file="Public:header" />
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">数据还原</span>
        </div>

		<div class="data-table table-striped">
			<table>
            <thead>
                <tr>
                    <th width="200">备份名称</th>
                    <th width="80">卷数</th>
                    
                    <th width="80">数据大小</th>
                    <th width="200">备份时间</th>
                    <th>状态</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                <volist name="list" id="data">
                    <tr>
                        <td>{$data.time|date='Ymd-His',###}</td>
                        <td>{$data.part}</td>
                       
                        <td>{$data.size}</td>
                        <td>{$data.key}</td>
                        <td>-</td>
                        <td class="action">
                            <a class=" btn-xs btn-info"  href="{:U('Tools/xiazai')}?file={$data.time|date='Ymd-His',###}-1.sql.gz">下载</a>


                            <a class="db-import  btn-primary btn-xs" href="{:U('Tools/import?time='.$data['time'])}">还原</a>
                          <a class="ajax-get confirm btn-danger btn-xs" href="{:U('Tools/del?time='.$data['time'])}">删除</a>
                        </td>
                    </tr>
                </volist>
            </tbody>
        </table>
			
		</div>
	</div>
</div>
<script type="text/javascript">
        $(".db-import").click(function(){
            var self = this, status = ".";
            $.get(self.href, success, "json");
            window.onbeforeunload = function(){ return "正在还原数据库，请不要关闭！" }
            return false;
        
            function success(data){
                if(data.status){
                    if(data.gz){
                        data.info += status;
                        if(status.length === 5){
                            status = ".";
                        } else {
                            status += ".";
                        }
                    }
                    $(self).parent().prev().text(data.info);
                    if(data.part){
                        $.get(self.href, 
                            {"part" : data.part, "start" : data.start}, 
                            success, 
                            "json"
                        );
                    }  else {
                        window.onbeforeunload = function(){ return null; }
                    }
                } else {
                    updateAlert(data.info,'alert-error');
                }
            }
        });
    </script>


<script type="text/javascript">
	//主导航高亮
	$('.index-box').addClass('current');
	$('.index-import').addClass('current');
</script>
<include file="Public:footer" />





