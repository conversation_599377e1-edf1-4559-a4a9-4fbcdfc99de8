<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="/Public/Home/fonts/bootstrap-icons/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
        <style>
            .css-ogtd7z {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                transition: all 1s ease 0s;
                -webkit-box-pack: center;
                justify-content: center;
                background-color: rgb(254, 241, 242);
            }
            .css-jrzkh7 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                background-color: rgb(24, 26, 32);
            }
            .css-1aac2e {
                box-sizing: border-box;
                margin: 0px auto;
                min-width: 0px;
                padding-left: 24px;
                padding-right: 24px;
                max-width: 1248px;
                background-color: rgb(254, 241, 242);
            }
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex-direction: column;
                flex: 1 1 0%;
            }
            .css-xry4yv {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                min-height: 600px;
                flex: 1 1 0%;
                flex-direction: column;
            }
            .css-xry4yv {
                flex-direction: row;
            }
            .css-foka8b {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                box-shadow: rgb(0 0 0 / 8%) 0px 2px 4px, rgb(0 0 0 / 8%) 0px 0px 4px;
                position: relative;
                z-index: 1;
                flex-direction: column;
                width: 200px;
                background: #ffffff;
            }
            .css-160vccy {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                flex: 1 1 0%;
                background-color: rgb(250, 250, 250);
            }
            .css-z87e9z {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                text-decoration: none;
                color: rgb(201, 148, 0);
                border-left: 4px solid #00b897;
                height: 48px;
                background-color: rgb(245, 245, 245);
                font-weight: 500;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: justify;
                justify-content: space-between;
            }
            .css-10j588g {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
                height: 100%;
                -webkit-box-align: center;
                align-items: center;
            }
            .css-iizq59 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;
                word-break: break-word;
                display: flex;
                flex: 1 1 0%;
                height: 100%;
                -webkit-box-align: center;
                align-items: center;
                color: rgb(33, 40, 51);
            }
            .css-14thuu2 {
                box-sizing: border-box;
                margin: 0px 8px;
                min-width: 0px;
                color: #00b897;
                font-size: 24px;
                fill: #00b897;
                width: 1em;
                flex-shrink: 0;
            }
            .css-6ijtmk {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                text-decoration: none;
                color: rgb(201, 148, 0);
                border-left: 4px solid transparent;
                height: 48px;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: justify;
                justify-content: space-between;
                background:#fff;
            }
            .css-hd27fe {
                box-sizing: border-box;
                margin: 0px 8px;
                min-width: 0px;
                color: rgb(132, 142, 156);
                font-size: 24px;
                fill: rgb(132, 142, 156);
                width: 1em;
                flex-shrink: 0;
            }
            .css-1n0484q {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                word-break: break-word;
                display: flex;
                flex: 1 1 0%;
                height: 100%;
                -webkit-box-align: center;
                align-items: center;
                color: rgb(33, 40, 51);
            }
            .css-146q23 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                flex: 1 1 0%;
                background-color: rgb(255, 255, 255);
            }
            .css-jlbk6n {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex-wrap: wrap;
                -webkit-box-pack: justify;
                justify-content: space-between;
                padding: 8px;
            }
            .css-1e6doj4 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                box-shadow: rgb(0 0 0 / 5%) 0px 0px 4px;
                flex: 1 1 0%;
                padding: 24px;
            }
            .css-6vt7sa {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                width: 40px;
                height: 40px;
                border-radius: 100%;
                border-width: 1px;
                border-style: solid;
                border-color: rgb(30, 35, 41);
                font-weight: 500;
                -webkit-box-pack: center;
                justify-content: center;
                -webkit-box-align: center;
                align-items: center;
                font-size: 20px;
                color: rgb(30, 35, 41);
            }
            .css-1sgz1lk {
                box-sizing: border-box;
                margin: 0px 0px 0px 16px;
                min-width: 0px;
            }
            .css-ize0sl {
                box-sizing: border-box;
                margin: 0px 0px 4px;
                min-width: 0px;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
            }
            .css-1uoge8i {
                box-sizing: border-box;
                margin: 0px 16px 0px 0px;
                min-width: 0px;
            }
            .css-180eiyx {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                font-size: 14px;
                color: rgb(30, 35, 41);
            }
            .css-1ap5wc6 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                color: rgb(112, 122, 138);
            }
            .css-1124n14 {
                box-sizing: border-box;
                margin: 0px 16px 0px 0px;
                min-width: 0px;
                color: rgb(30, 35, 41);
            }
            .css-lzd0h4 {
                box-sizing: border-box;
                margin: 0px 0px 0px 8px;
                min-width: 0px;
            }
            .css-bhso1m {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: inline-block;
                border-radius: 4px;
                padding-left: 8px;
                padding-right: 8px;
                font-size: 14px;
                background-color: #00b897;
color: #fff;
            }
            .css-1ry7rnu {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                color: rgb(112, 122, 138);
                font-size: 12px;
                line-height: 1.25;
            }
            .css-9cwl6c {
                box-sizing: border-box;
                margin: 0px 8px 0px 0px;
                min-width: 0px;
            }
            .css-vurnku {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
            }
            .css-kvkii2 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                width: 66.6667%;
                padding: 8px;
            }
            .css-1p01izn {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                box-shadow: rgb(20 21 26 / 4%) 0px 1px 2px, rgb(71 77 87 / 4%) 0px 3px 6px, rgb(20 21 26 / 10%) 0px 0px 1px;
                border-radius: 4px;
                background-color: rgb(255, 255, 255);
                padding: 0px 16px;
                width: 100%;
            }
            .css-1hythwr {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
                flex-direction: column;
            }
            .css-1hz1mz6 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                flex: 1 1 0%;
                padding-top: 16px;
                padding-bottom: 16px;
            }

            .css-181kvgz {
                box-sizing: border-box;
                margin: 0px 16px 0px 0px;
                min-width: 0px;
                display: inline-block;
                text-decoration: none;
                font-weight: 600;
                color: rgb(30, 35, 41);
                font-size: 16px;
                padding: 0px;
            }
            .css-181kvgz:hover {
                color: rgb(252, 213, 53);
            }
            a, a:active, a:visited {
                text-decoration: none;
            }
            .css-10nf7hq {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
            }
            .css-4cffwv {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
            }
            .css-noqr05 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                -webkit-box-pack: justify;
                justify-content: space-between;
                -webkit-box-align: center;
                align-items: center;
                width: auto;
            }
            .css-d732j0 {
                margin: 0px 4px;
                min-width: 0px;
                appearance: none;
                user-select: none;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-flex;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: center;
                justify-content: center;
                box-sizing: border-box;
                font-family: inherit;
                text-align: center;
                text-decoration: none;
                outline: none;
                font-weight: 500;
                line-height: 20px;
                word-break: keep-all;
                color: rgb(33, 40, 51);
                border-radius: 4px;
                padding: 4px 16px;
                border: none;
                background: #3db485;color: #fff;
                flex: 1 1 0%;
                min-height: 24px;
                font-size: 12px;
            }
            .css-1dfql01 {
                margin: 0px 4px;
                min-width: 0px;
                appearance: none;
                user-select: none;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-flex;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: center;
                justify-content: center;
                box-sizing: border-box;
                font-family: inherit;
                text-align: center;
                text-decoration: none;
                outline: none;
                font-weight: 500;
                line-height: 20px;
                word-break: keep-all;
                color: rgb(33, 40, 51);
                border-radius: 4px;
                padding: 4px 16px;
                border: none;
                background-color: transparent;
                box-shadow: rgb(234 236 239) 0px 0px 0px 1px inset;
                flex: 1 1 0%;
                min-height: 24px;
                font-size: 12px;
            }
            .css-1s6nhe2 {
                box-sizing: border-box;
                margin: 0px 0px 0px 16px;
                min-width: 0px;
                text-decoration: none;
                color: rgb(201, 148, 0);
                display: flex;
            }
            .css-155meta {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                color: rgb(0, 0, 0);
                font-size: 18px;
                fill: rgb(0, 0, 0);
                width: 1em;
                height: 1em;
            }
            .css-1s6nhe2:hover {
                text-decoration: underline;
                color: #00b897;
            }
            .css-vurnku {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
            }
            .css-gnqbje {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: block;
            }
            .css-ysetcg {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                padding-top: 48px;
                padding-bottom: 48px;
            }
            .css-ct0qa6 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
            }
            .css-1bliacb {
                box-sizing: border-box;
                margin: 0px;
                width: 25%;
                flex: 1 1 0%;
                margin-bottom: 24px;
            }
            .css-vurnku {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
            }
            .css-1f978ju {
                box-sizing: border-box;
                margin: 0px 0px 8px;
                min-width: 0px;
            }
            .css-ize0sl {
                box-sizing: border-box;
                margin: 0px 0px 4px;
                min-width: 0px;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
            }
            .css-1kbdyxh {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 14px;
                color: rgb(112, 122, 138);
                display: inline-block;
            }
            .css-n5mzgu {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                align-items: flex-end;
                flex-wrap: wrap;
                color: rgb(30, 35, 41);
                font-size: 14px;
                line-height: 24px;
            }
            .css-off8uh {
                box-sizing: border-box;
                margin: 0px 4px 0px 0px;
                min-width: 0px;
                display: flex;
                align-items: flex-end;
            }
            .css-1t9tl2o {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 32px;
                line-height: 36px;
            }
            .css-vurnku {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
            }
            .css-omwf4y {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                width: 100%;
                padding: 8px;
                flex-direction: column;
                flex: 1 1 0%;
            }
            .css-vt77s9 {
                box-sizing: border-box;
                /* margin: 16px 0px 0px; */
                min-width: 0px;
                box-shadow: rgb(20 21 26 / 4%) 0px 1px 2px, rgb(71 77 87 / 4%) 0px 3px 6px, rgb(20 21 26 / 10%) 0px 0px 1px;
                border-radius: 4px;
                background-color: rgb(255, 255, 255);
                padding: 0px 16px;
                width: 100%;
                flex: 1 1 0%;
            }
            .css-1hythwr {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
                flex-direction: column;
            }
            .css-hwv82q {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                border-width: 0px 0px 1px;
                border-style: solid;
                border-color: rgb(234, 236, 239);
                flex: 1 1 0%;
                padding-top: 16px;
                padding-bottom: 16px;
            }
            .css-5x6ly7 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
                -webkit-box-align: center;
                align-items: center;
            }

            .css-65w75 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 12px;
            }
            
            .css-1wuyyej {
                box-sizing: border-box;
                margin: 8px 0px 0px;
                min-width: 0px;
                text-decoration: none;
                display: block;
                border-width: 0px 0px 1px;
                border-style: solid;
                border-color: rgb(234, 236, 239);
                color: rgb(30, 35, 41);
                line-height: 1.5;
                padding-top: 16px;
                padding-bottom: 16px;
            }
            .css-1joqi3u {
                box-sizing: border-box;
                margin: 16px 0px 0px;
                min-width: 0px;
                text-align: right;
                color: rgb(112, 122, 138);
            }
            .css-1wuyyej:hover {
                color: #00b897;
            }
            a, .css-1wuyyej:active, .css-1wuyyej:visited {
                text-decoration: none;
            }
            .css-ei3nni {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                width: 100%;
                padding: 8px;
            }
            .css-1p01izn {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                box-shadow: rgb(20 21 26 / 4%) 0px 1px 2px, rgb(71 77 87 / 4%) 0px 3px 6px, rgb(20 21 26 / 10%) 0px 0px 1px;
                border-radius: 4px;
                background-color: rgb(255, 255, 255);
                padding: 0px 16px;
                width: 100%;
            }
            .css-1hythwr {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
                flex-direction: column;
            }
            .css-hwv82q {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                border-width: 0px 0px 1px;
                border-style: solid;
                border-color: rgb(234, 236, 239);
                flex: 1 1 0%;
                padding-top: 16px;
                padding-bottom: 16px;
            }
            .css-1dcd6pv {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
                padding-top: 16px;
                padding-bottom: 16px;
                flex-direction: column;
            }
            .css-1h690ep {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                flex: 1 1 0%;
            }
            .css-phax11 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                padding: 8px;
                flex-direction: column;
                width: 20%;
            }
            .css-9b6x94 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 12px;
                color: rgb(112, 122, 138);
                line-height: 1.5;
            }
            .css-1ngrbny {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                padding: 8px;
                flex-direction: row;
                width: 20%;
                -webkit-box-pack: end;
                justify-content: flex-end;
                -webkit-box-align: center;
                align-items: center;
            }
            .css-1pysja1 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                flex: 1 1 0%;
            }
            
            .css-kc3i4p {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
                border-width: 0px 0px 1px;
                border-style: solid;
                border-color: rgb(234, 236, 239);
                flex: 1 1 0%;
                padding-top: 4px;
                padding-bottom: 4px;
            }
            .css-1g02g2m {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 14px;
                color: rgb(30, 35, 41);
            }
            .css-cvky42 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 12px;
                color: rgb(30, 35, 41);
            }
            .css-4cffwv {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: flex;
            }
            
            .css-1pryf6p {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 12px;
                color: rgb(14, 203, 129);
            }
            .css-1g02g2m {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                font-size: 14px;
                color: rgb(30, 35, 41);
            }
            .css-9biujf {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                cursor: pointer;
                text-decoration: underline;
                font-size: 12px;
                color: #00b897;
                line-height: 1.5;
            }

        </style>
	</head>
	<body>
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            
	            <div class="css-ogtd7z">
	                <div class="css-jrzkh7">
	                    <div id="header_global_js_wxgy34nj" class="css-1aac2e"></div>
	                </div>
	           </div>
	           
	           <main class="css-1wr4jig">
	               <main class="css-xry4yv">
	                   <!--左边-->
	                   <div class="css-foka8b">
	                       <a data-bn-type="link" href="{:U('User/index')}" class="css-6ijtmk" style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-person-fill css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-iizq59">{:L('总览')}</div>
	                           </div>
	                       </a>
	                       
	                       <a data-bn-type="link" href="{:U('User/addresslist')}" class="css-6ijtmk"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-journal-text css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('地址管理')}</div>
	                           </div>
	                       </a>
	                       
	                       <a data-bn-type="link" href="{:U('User/authrz')}" class="css-6ijtmk"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-shield-check css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('实名认证')}</div>
	                           </div>
	                       </a>
	                       
	                       <a data-bn-type="link" href="{:U('User/respwd')}" class="css-6ijtmk"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-gear css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('修改密码')}</div>
	                           </div>
	                       </a>
	                       
	                       <a data-bn-type="link" href="{:U('User/tgcode')}" class="css-6ijtmk"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-person-plus css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('推荐返佣')}</div>
	                           </div>
	                       </a>
	                       
	                       <a data-bn-type="link" href="{:U('User/notice')}" class="css-z87e9z"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-bell css-14thuu2"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('我的通知')}</div>
	                           </div>
	                       </a>
	                        <a data-bn-type="link" href="{:U('User/online')}" class="css-6ijtmk"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-headset css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('联系客服')}</div>
	                           </div>
	                       </a>
	                       <a data-bn-type="link" href="{:U('User/mybill')}" class="css-6ijtmk"  style="text-decoration: none;">
	                           <div class="css-10j588g">
	                               <i class="bi bi-card-list css-hd27fe"></i>
	                               <div data-bn-type="text" class="css-1n0484q">{:L('我的账单')}</div>
	                           </div>
	                       </a>
	                       
	                       
	                   </div>
	                   
	                   <!--右边-->
	                   <div class="css-160vccy">
	                       <!--右上-->
	                       <div class="css-146q23">
	                           <div class="css-1e6doj4">
	                               <div class="css-6vt7sa">{$uheader}</div>
	                               <div class="css-1sgz1lk">
	                                   <div class="css-ize0sl">
	                                       <div class="css-1124n14">{$uinfo.name}</div>
	                                       <div class="css-1uoge8i">
	                                           <div class="css-180eiyx">
	                                               <div data-bn-type="text" class="css-1ap5wc6">{:L('用户')}ID: </div>
	                                               {$uinfo.uid}
	                                           </div> 
	                                           
	                                       </div>
	                                       <div class="css-lzd0h4">
	                                           <if condition="$uinfo.rzstatus eq 2">
	                                           <div class="css-bhso1m">{:L('已认证')}</div>
	                                           <else />
	                                           <div class="css-bhso1m">{:L('未认证')}</div>
	                                           </if>
	                                       </div>
	                                   </div>
	                                   <div class="css-1ry7rnu">
	                                       <div data-bn-type="text" class="css-9cwl6c">{:L('上次登录时间')} {$uinfo.logintime}</div>
	                                       <div data-bn-type="text" class="css-vurnku">IP: {$uinfo.loginip}</div>
	                                   </div>
	                                   
	                               </div>
	                           </div>
	                       </div>
	                       
	                       
	                       <!--右下-->
	                       <div class="css-jlbk6n">

	                           <!--登陆日志-->
	                           <div class="css-ei3nni">
	                               <div class="css-1p01izn">
	                                   <div class="css-1hythwr">
	                                       <div class="css-hwv82q">
	                                           <div class="css-5x6ly7">
	                                               <div class="css-5x6ly7">
	                                                   <a class="css-181kvgz">{:L('通知管理')}</a>
	                                               </div>
	                                               <div class="css-10nf7hq">
	                                                   <a class="css-1s6nhe2 f12" style="cursor:pointer;">
	                                                       <span onclick="allread();" style="color:#00b897;">{:L('标记已读')}</span>
	                                                   </a>
	                                                   <a onclick="allnoticedel();" class="css-1s6nhe2 f12" style=";cursor:pointer;color:#00b897;">
	                                                       <span>{:L('全部删除')}</span>
	                                                   </a>
	                                               </div>
	                                           </div>
	                                       </div>
	                                       <div class="css-1dcd6pv">
	                                           <div class="css-1h690ep">
	                                               <div class="css-phax11">
	                                                   <div data-bn-type="text" class="css-9b6x94">{:L('通知标题')}</div>
	                                               </div>
	                                               <div class="css-phax11" style="width:30%">
	                                                   <div data-bn-type="text" class="css-9b6x94">{:L('内容')}</div>
	                                               </div>
	                                               <div class="css-phax11">
	                                                   <div data-bn-type="text" class="css-9b6x94">{:L('时间')}</div>
	                                               </div>
	                                               <div class="css-phax11">
	                                                   <div data-bn-type="text" class="css-9b6x94">{:L('状态')}</div>
	                                               </div>
	                                               <div class="css-phax11">
	                                                   <div data-bn-type="text" class="css-9b6x94">{:L('操作')}</div>
	                                               </div>
	                                           </div>
	                                           
	                                           <foreach name="list" item="vo">
	                                           <div class="css-1pysja1">
	                                               <div class="css-kc3i4p">
	                                                   <div class="css-phax11">
	                                                       <div data-bn-type="text" class="css-1g02g2m">{$vo.title}</div>
	                                                   </div>
	                                                   <div class="css-phax11" style="width:30%">
	                                                       <div class="css-4cffwv">
	                                                           <div data-bn-type="text" class="css-cvky42">{$vo.content}</div>
	                                                       </div>
	                                                   </div>
	                                                   <div class="css-phax11">
	                                                       <div class="css-4cffwv">
	                                                           <div data-bn-type="text" class="css-cvky42">{$vo.addtime}</div>
	                                                       </div>
	                                                   </div>
	                                                   <div class="css-phax11">
	                                                       <div class="css-4cffwv">
	                                                           <if condition="$vo.status eq 1">
	                                                           <div data-bn-type="text" class="css-cvky42" style="color:#e54c67;">{:L('未读')}</div>
	                                                           <elseif condition="$vo.status eq 2" />
	                                                           <div data-bn-type="text" class="css-cvky42" style="color:#3db485;">{:L('已读')}</div>
	                                                           </if>
	                                                       </div>
	                                                   </div>
	                                                   <div class="css-phax11">
	                                                       <div data-bn-type="text" class="css-1g02g2m">
	                                                           
	                                                           <if condition="$vo.status eq 1">
	                                                           <span onclick="setread({$vo.id});" style="color:#3db485;cursor:pointer;font-size:12px;">{:L('标记已读')}</span>
	                                                           </if>
	                                                           
	                                                           <span onclick="delone({$vo.id});" style="color:#e54c67;cursor:pointer;font-size:12px;">{:L('删除')}</span>
	                                                       </div>
	                                                   </div>
	                                               </div>
	                                           </div>
	                                           </foreach>
	                                           
	                                       </div>
	                                   </div>
	                               </div>
	                           </div>
	                           
 
	                       </div>
	                   </div>
	                   
	               </main>
	           </main>

	            <include file="Public:footer"/>
	        </div>   
	    </div>
	    
	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>
    
    
    <script type="text/javascript">
        function allnoticedel(){
            var st = 1;
            $.post("{:U('User/allnoticedel')}",
            {'st' : st},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.reload();
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            });
        }
        function allread(){
            var st = 1;
            $.post("{:U('User/allread')}",
            {'st' : st},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.reload();
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            });
        }
        
        function delone(id){
            var nid = id;
            if(nid <= 0 || nid == null){
                layer.msg("{:L('缺少重要参数')}");return false;    
            }
            $.post("{:U('User/delonenotice')}",
            {'id':nid},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.reload();
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            });
        }
        function setread(id){
            var nid = id;
            if(nid <= 0 || nid == null){
                layer.msg("{:L('缺少重要参数')}");return false;    
            }
            $.post("{:U('User/readnoticeone')}",
            {'id':nid},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.reload();
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            }
            );

        }
    </script>
    
    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>

    
</html>