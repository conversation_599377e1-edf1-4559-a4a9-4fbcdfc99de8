<include file="Public:header" />
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main" style="margin-top:25px;">
		<div class="main-title-h" style="margin-bottom:0px;"> <span class="h1-title">矿池挖矿</span> </div>
		<div class="cf">
		
			<div class="fl"> 
				<a style="width: 120px;" class="btn btn-success navbar-btn btn-sm" href="{:U('Orepool/addorepool')}">新增矿池</a>
				<a style="width: 120px;" class="btn btn-info navbar-btn btn-sm" href="{:U('Orepool/orepoolconfig')}">奖励制度设置</a>
				<!--<button class="btn ajax-post btn-info navbar-btn btn-sm" url="{:U('Money/status',array('method'=>'resume'))}" target-form="ids">启 用</button>
				<button class="btn ajax-post btn-warning navbar-btn btn-sm" url="{:U('Money/status',array('method'=>'forbid'))}" target-form="ids">禁 用</button>
				<button class="btn ajax-post confirm btn-danger navbar-btn btn-sm" url="{:U('Money/status',array('method'=>'delete'))}" target-form="ids">删 除</button>
				<button class="btn ajax-post confirm btn-danger navbar-btn btn-sm" url="{:U('Money/status',array('method'=>'delete'))}" target-form="ids">手动释放利息</button>-->
			</div>
		</div>
		
		<div class="data-table table-striped">
			<table class="">
				<thead>
					<tr>
						<th class="">矿池名称</th>
						<th class="">参与币种</th>
						<th class="">总价值</th>
						<th class="">最低额度</th>
						<th class="">最高额度</th>
						<th class="">释放规则</th>
						<th class="">释放方式</th>
						<th class="">添加时间</th>
						<th class="">状态</th>
						<th class="">操作</th>
					</tr>
				</thead>
				
        		<tbody>
					<notempty name="list">
						<volist name="list" id="vo">
							<tr>
								<td>{$vo.oretitle}</td>
								<td>{$vo.coinname}</td>
								<td>{$vo.summoney}CNY</td>
								<td>{$vo.minmoney}CNY</td>
								<td>{$vo.maxmoney}CNY</td>
								<td>
									<?php if($vo['rtype']==1){?>
										<span>固定比例</span>
									<?php }elseif($vo['rtype']==2){?>
										<span>固定额度</span>
									<?php }elseif($vo['rtype']==3){?>
										<span>固定额度比例</span>
									<?php }?>
								</td>
								<td>
									<?php if($vo['rway']==1){?>
										<span>自动释放</span>
									<?php }elseif($vo['rway']==2){?>
										<span>手动释放</span>
									<?php }?>
								</td>
								<td>{$vo['addtime']}</td>
								<td>
									<?php if($vo['status']==1){?>
										<span>正常</span>
									<?php }elseif($vo['status']==2){?>
										<span>关闭</span>
									<?php }?>
								</td>
								<td>
									<a href="{:U('Orepool/editorepool?id='.$vo['id'])}" class="btn btn-primary btn-xs">编辑</a> 
									<a href="{:U('Orepool/delore?id='.$vo['id'])}" class="btn btn-danger btn-xs">删除</a> 
									<a href="{:U('Orepool/orerelease?id='.$vo['id'])}" class="btn btn-success btn-xs">释放收益 </a> 
									<a href="{:U('Orepool/orelog',array('oid' => $vo['id']))}" class="btn btn-warning btn-xs">参与记录</a>
								</td>
							</tr>
						</volist>
					<else />
						<td colspan="15" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
					</notempty>
				</tbody>
				
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	$(function(){
		//主导航高亮
		$('.Money-box').addClass('current');
		//边导航高亮
		$('.Money-index').addClass('current');
	});
</script> 
<include file="Public:footer" />