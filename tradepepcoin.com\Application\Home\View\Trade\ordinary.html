<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K-Line Chart</title>
    <link rel="stylesheet" href="/Public/Static/css/kline.css">
    <style>
        body {
            margin: 0;
            padding: 10px;
            background-color: #1a1a1a;
            font-family: Arial, sans-serif;
        }
        
        #chart_container {
            margin: 0 auto;
            width: 100%;
            height: 544px;
            position: relative;
        }
        
        .changInterval {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .interval-item {
            padding: 8px 16px;
            outline: none;
            transition: all .25s;
            border: 1px solid #444;
            cursor: pointer;
            background-color: #2c2d2e;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            min-width: 60px;
            text-align: center;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .interval-item:hover {
            background-color: #444;
            border-color: #666;
        }
        
        .interval-item.active {
            background-color: #f0b90b;
            border-color: #f0b90b;
            color: #000;
        }
        
        .interval-label {
            color: #ccc;
            font-size: 14px;
            margin-right: 10px;
            font-weight: 500;
        }
        
        .loding {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 999;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(26, 26, 26, 0.8);
        }
        
    </style>
</head>

<body>
    <div class="changInterval">
        <span class="interval-label">Time:</span>
        <button type="button" class="interval-item active" onclick="changInterval('m1',this)">1M</button>
        <button type="button" class="interval-item" onclick="changInterval('m5',this)">5M</button>
        <button type="button" class="interval-item" onclick="changInterval('m15',this)">15M</button>
        <button type="button" class="interval-item" onclick="changInterval('m30',this)">30M</button>
        <button type="button" class="interval-item" onclick="changInterval('m60',this)">1H</button>
        <button type="button" class="interval-item" onclick="changInterval('h4',this)">4H</button>
        <button type="button" class="interval-item" onclick="changInterval('d1',this)">1D</button>
    </div>
    
    <div id="chart_container">
        <div class="loding" data-type="0" style="display: flex;">
            <img src="/Public/Static/img/loading.gif" alt="Loading..." />  
        </div>
    </div>
    
    <script>
        // 全局变量设置
        window.SYMBOL = "{$market}";
        window.fenshi = "Time sharing";
        window.fen = "M";
        window.xiaoshi = "H";
        window.tian = "Day";
        window.zhou = "Week";
        
        window.kai = "Open";
        window.gao = "High";
        window.di = "Low";
        window.shou = "Close";
        window.labels = ["Time:", "Open:", "Close:", "High:", "Low:", "Volume:"];
        window.plots = [
            { key: "ma5", title: "MA5: ", type: "line" },
            { key: "ma10", title: "MA10: ", type: "line" },
            { key: "ma30", title: "MA30: ", type: "line" },
            { key: "ma60", title: "MA60: ", type: "line" }
        ];
        window.title = "MA20: ";
        window.title1 = "VOLUME: ";
        
        // 时间间隔切换函数
        function changInterval(interval, element) {
            // 移除所有active类
            document.querySelectorAll('.interval-item').forEach(item => {
                item.classList.remove('active');
            });
            // 添加active类到当前元素
            element.classList.add('active');
            
            // 如果K线图已初始化，切换时间间隔
            if (window.deedfeeds && typeof window.deedfeeds.intervalChanged === 'function') {
                window.deedfeeds.intervalChanged(interval);
            }
        }
        
        // 显示加载动画
        function loading() {
            const loadingEl = document.querySelector('.loding');
            if (loadingEl) {
                loadingEl.style.display = 'flex';
            }
        }
        
        // 隐藏加载动画
        function hideLoading() {
            const loadingEl = document.querySelector('.loding');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }
        }
        
        // 初始化K线图（仅使用 klinecharts + ws-deedfeeds）
        function initOnReady() {
            loading();
            window.init_offset = 10;
            if (window.deedfeeds && typeof window.deedfeeds.setHistoryData === 'function') {
                try {
                    window.deedfeeds.setHistoryData({
                        interval: 'm1',
                        setHistoryData: function (data) {
                            try {
                                if (typeof chart !== 'undefined') {
                                    chart.setPriceVolumePrecision(2, 2);
                                    chart.applyNewData(data);
                                }
                            } finally {
                                hideLoading();
                            }
                        },
                        subscribeData: function (bar) {
                            if (typeof chart !== 'undefined') {
                                chart.updateData(bar);
                            }
                        }
                    });
                } catch (error) {
                    console.error('K线图初始化失败:', error);
                    hideLoading();
                }
            } else {
                console.error('Klinecharts 数据源未就绪');
                hideLoading();
            }
        }
        
        // DOM加载完成后初始化
function changInterval(interval, element) {
    document.querySelectorAll(".interval-item").forEach(item => {
        item.classList.remove("active");
    });
    element.classList.add("active");
    if (window.deedfeeds && typeof window.deedfeeds.intervalChanged === "function") {
        window.deedfeeds.intervalChanged(interval);
    }
}

function loading() {
    const loadingEl = document.querySelector(".loding");
    if (loadingEl) {
        loadingEl.style.display = "flex";
    }
}

        window.addEventListener('DOMContentLoaded', initOnReady, false);
    </script>

    <!-- 加载必要的JavaScript文件 -->
    <script src="/Public/Home/js/jquery-1.8.3.min.js"></script>
    <script src="/Public/Static/js/pako.min.js"></script>
    <script src="/Public/Static/js/klinecharts.min.js"></script>
    <script src="/Public/Static/js/ws-deedfeeds.js"></script>
</body>
</html>
