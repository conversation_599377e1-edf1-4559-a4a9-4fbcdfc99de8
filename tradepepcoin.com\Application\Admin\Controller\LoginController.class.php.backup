<?php
namespace Admin\Controller;
class LoginController extends \Think\Controller {
  public function index(){
    if(IS_POST){
      $username = I('post.username','','trim');
      $password = I('post.password','','trim');
      $admin = M('Admin')->where(array('username'=>$username))->find();
      if(!$admin || $admin['password'] != md5($password)){
        $this->error('Login failed');
      }
      session('admin_id',$admin['id']);
      session('admin_username',$admin['username']);
      $this->success('Login ok', U('Index/index'));
    }else{
      $this->display();
    }
  }
}
