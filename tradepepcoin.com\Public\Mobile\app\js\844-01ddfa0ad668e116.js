(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[844],{1816:function(e,t,n){"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}n.d(t,{M:function(){return r}})},1704:function(e,t,n){"use strict";n.d(t,{VY:function(){return X},h4:function(){return Y},ck:function(){return H},fC:function(){return K},xz:function(){return q}});var r=n(6074),o=n(8245),a=n(2130),i=n(6215),c=n(4680),u=n(3597),l=n(4540),s=n(1816),d=n(2784),f=n(7896);const[p,v]=(0,l.b)("Collapsible"),[m,h]=p("Collapsible"),g=d.forwardRef(((e,t)=>{const{__scopeCollapsible:n,open:o,defaultOpen:i,disabled:c,onOpenChange:l,...s}=e,[p=!1,v]=(0,u.T)({prop:o,defaultProp:i,onChange:l});return d.createElement(m,{scope:n,disabled:c,contentId:(0,r.M)(),open:p,onOpenToggle:d.useCallback((()=>v((e=>!e))),[v])},d.createElement(a.W.div,(0,f.Z)({"data-state":w(p),"data-disabled":c?"":void 0},s,{ref:t})))})),y=d.forwardRef(((e,t)=>{const{__scopeCollapsible:n,...r}=e,o=h("CollapsibleTrigger",n);return d.createElement(a.W.button,(0,f.Z)({type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":w(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled},r,{ref:t,onClick:(0,s.M)(e.onClick,o.onOpenToggle)}))})),b=d.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,a=h("CollapsibleContent",e.__scopeCollapsible);return d.createElement(o.z,{present:n||a.open},(({present:e})=>d.createElement(E,(0,f.Z)({},r,{ref:t,present:e}))))})),E=d.forwardRef(((e,t)=>{const{__scopeCollapsible:n,present:r,children:o,...u}=e,l=h("CollapsibleContent",n),[s,p]=d.useState(r),v=d.useRef(null),m=(0,i.e)(t,v),g=d.useRef(0),y=g.current,b=d.useRef(0),E=b.current,C=l.open||s,R=d.useRef(C),S=d.useRef();return d.useEffect((()=>{const e=requestAnimationFrame((()=>R.current=!1));return()=>cancelAnimationFrame(e)}),[]),(0,c.b)((()=>{const e=v.current;if(e){S.current=S.current||{transitionDuration:e.style.transitionDuration,animationDuration:e.style.animationDuration,animationFillMode:e.style.animationFillMode},e.style.transitionDuration="0s",e.style.animationDuration="0s",e.style.animationFillMode="none";const t=e.getBoundingClientRect();g.current=t.height,b.current=t.width,R.current||(e.style.transitionDuration=S.current.transitionDuration,e.style.animationDuration=S.current.animationDuration,e.style.animationFillMode=S.current.animationFillMode),p(r)}}),[l.open,r]),d.createElement(a.W.div,(0,f.Z)({"data-state":w(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!C},u,{ref:m,style:{"--radix-collapsible-content-height":y?`${y}px`:void 0,"--radix-collapsible-content-width":E?`${E}px`:void 0,...e.style}}),C&&o)}));function w(e){return e?"open":"closed"}const C=g,R=y,S=b;var M=n(8210);const x=["Home","End","ArrowDown","ArrowUp"],[_,O,I]=(0,M.B)("Accordion"),[T,k]=(0,l.b)("Accordion",[I,v]),P=v(),D=d.forwardRef(((e,t)=>{const{type:n,...r}=e,o=r,a=r;return d.createElement(_.Provider,{scope:e.__scopeAccordion},"multiple"===n?d.createElement(Z,(0,f.Z)({},a,{ref:t})):d.createElement(W,(0,f.Z)({},o,{ref:t})))}));D.propTypes={type(e){const t=e.value||e.defaultValue;return e.type&&!["single","multiple"].includes(e.type)?new Error("Invalid prop `type` supplied to `Accordion`. Expected one of `single | multiple`."):"multiple"===e.type&&"string"==typeof t?new Error("Invalid prop `type` supplied to `Accordion`. Expected `single` when `defaultValue` or `value` is type `string`."):"single"===e.type&&Array.isArray(t)?new Error("Invalid prop `type` supplied to `Accordion`. Expected `multiple` when `defaultValue` or `value` is type `string[]`."):null}};const[L,N]=T("Accordion"),[A,F]=T("Accordion",{collapsible:!1}),W=d.forwardRef(((e,t)=>{const{value:n,defaultValue:r,onValueChange:o=(()=>{}),collapsible:a=!1,...i}=e,[c,l]=(0,u.T)({prop:n,defaultProp:r,onChange:o});return d.createElement(L,{scope:e.__scopeAccordion,value:c?[c]:[],onItemOpen:l,onItemClose:d.useCallback((()=>a&&l("")),[a,l])},d.createElement(A,{scope:e.__scopeAccordion,collapsible:a},d.createElement(U,(0,f.Z)({},i,{ref:t}))))})),Z=d.forwardRef(((e,t)=>{const{value:n,defaultValue:r,onValueChange:o=(()=>{}),...a}=e,[i=[],c]=(0,u.T)({prop:n,defaultProp:r,onChange:o}),l=d.useCallback((e=>c(((t=[])=>[...t,e]))),[c]),s=d.useCallback((e=>c(((t=[])=>t.filter((t=>t!==e))))),[c]);return d.createElement(L,{scope:e.__scopeAccordion,value:i,onItemOpen:l,onItemClose:s},d.createElement(A,{scope:e.__scopeAccordion,collapsible:!0},d.createElement(U,(0,f.Z)({},a,{ref:t}))))})),[j,V]=T("Accordion"),U=d.forwardRef(((e,t)=>{const{__scopeAccordion:n,disabled:r,...o}=e,c=d.useRef(null),u=(0,i.e)(c,t),l=O(n),p=(0,s.M)(e.onKeyDown,(e=>{var t;if(!x.includes(e.key))return;const n=e.target,r=l().filter((e=>{var t;return!(null!==(t=e.ref.current)&&void 0!==t&&t.disabled)})),o=r.findIndex((e=>e.ref.current===n)),a=r.length;if(-1===o)return;e.preventDefault();let i=o;switch(e.key){case"Home":i=0;break;case"End":i=a-1;break;case"ArrowDown":i=o+1;break;case"ArrowUp":i=o-1,i<0&&(i=a-1)}null===(t=r[i%a].ref.current)||void 0===t||t.focus()}));return d.createElement(j,{scope:n,disabled:r},d.createElement(_.Slot,{scope:n},d.createElement(a.W.div,(0,f.Z)({},o,{ref:u,onKeyDown:r?void 0:p}))))})),[B,$]=T("AccordionItem"),z=d.forwardRef(((e,t)=>{const{__scopeAccordion:n,value:o,...a}=e,i=V("AccordionItem",n),c=N("AccordionItem",n),u=P(n),l=(0,r.M)(),s=o&&c.value.includes(o)||!1,p=i.disabled||e.disabled;return d.createElement(B,{scope:n,open:s,disabled:p,triggerId:l},d.createElement(C,(0,f.Z)({"data-state":s?"open":"closed"},u,a,{ref:t,disabled:p,open:s,onOpenChange:e=>{e?c.onItemOpen(o):c.onItemClose(o)}})))})),K=D,H=z,Y=d.forwardRef(((e,t)=>{const{__scopeAccordion:n,...r}=e,o=$("AccordionHeader",n);return d.createElement(a.W.h3,(0,f.Z)({"data-state":(i=o.open,i?"open":"closed"),"data-disabled":o.disabled?"":void 0},r,{ref:t}));var i})),q=d.forwardRef(((e,t)=>{const{__scopeAccordion:n,...r}=e,o=$("AccordionTrigger",n),a=F("AccordionTrigger",n),i=P(n);return d.createElement(_.ItemSlot,{scope:n},d.createElement(R,(0,f.Z)({"aria-disabled":o.open&&!a.collapsible||void 0,id:o.triggerId},i,r,{ref:t})))})),X=d.forwardRef(((e,t)=>{const{__scopeAccordion:n,...r}=e,o=$("AccordionContent",n),a=P(n);return d.createElement(S,(0,f.Z)({role:"region","aria-labelledby":o.triggerId},a,r,{ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}}))}))},8930:function(e,t,n){"use strict";n.d(t,{fC:function(){return C},z$:function(){return R}});var r=n(2130),o=n(8245),a=n(9674),i=n(6842),c=n(9586),u=n(3597),l=n(1816),s=n(4540),d=n(6215),f=n(2784),p=n(7896);const[v,m]=(0,s.b)("Checkbox"),[h,g]=v("Checkbox"),y=f.forwardRef(((e,t)=>{const{__scopeCheckbox:n,"aria-labelledby":o,name:i,checked:c,defaultChecked:s,required:v,disabled:m,value:g="on",onCheckedChange:y,...C}=e,[R,S]=f.useState(null),M=(0,d.e)(t,(e=>S(e))),x=(0,a.t0)(R),_=o||x,O=f.useRef(!1),I=!R||Boolean(R.closest("form")),[T=!1,k]=(0,u.T)({prop:c,defaultProp:s,onChange:y});return f.createElement(h,{scope:n,state:T,disabled:m},f.createElement(r.W.button,(0,p.Z)({type:"button",role:"checkbox","aria-checked":E(T)?"mixed":T,"aria-labelledby":_,"aria-required":v,"data-state":w(T),"data-disabled":m?"":void 0,disabled:m,value:g},C,{ref:M,onKeyDown:(0,l.M)(e.onKeyDown,(e=>{"Enter"===e.key&&e.preventDefault()})),onClick:(0,l.M)(e.onClick,(e=>{k((e=>!!E(e)||!e)),I&&(O.current=e.isPropagationStopped(),O.current||e.stopPropagation())}))})),I&&f.createElement(b,{control:R,bubbles:!O.current,name:i,value:g,checked:T,required:v,disabled:m,style:{transform:"translateX(-100%)"}}))})),b=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,a=f.useRef(null),u=(0,c.D)(n),l=(0,i.t)(t);return f.useEffect((()=>{const e=a.current,t=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(t,"checked").set;if(u!==n&&o){const t=new Event("click",{bubbles:r});e.indeterminate=E(n),o.call(e,!E(n)&&n),e.dispatchEvent(t)}}),[u,n,r]),f.createElement("input",(0,p.Z)({type:"checkbox","aria-hidden":!0,defaultChecked:!E(n)&&n},o,{tabIndex:-1,ref:a,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}}))};function E(e){return"indeterminate"===e}function w(e){return E(e)?"indeterminate":e?"checked":"unchecked"}const C=y,R=f.forwardRef(((e,t)=>{const{__scopeCheckbox:n,forceMount:a,...i}=e,c=g("CheckboxIndicator",n);return f.createElement(o.z,{present:a||E(c.state)||!0===c.state},f.createElement(r.W.span,(0,p.Z)({"data-state":w(c.state),"data-disabled":c.disabled?"":void 0},i,{ref:t,style:{pointerEvents:"none",...e.style}})))}))},8210:function(e,t,n){"use strict";n.d(t,{B:function(){return c}});var r=n(9575),o=n(6215),a=n(4540),i=n(2784);function c(e){const t=e+"CollectionProvider",[n,c]=(0,a.b)(t),[u,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e+"CollectionSlot",d=i.forwardRef(((e,t)=>{const{scope:n,children:a}=e,c=l(s,n),u=(0,o.e)(t,c.collectionRef);return i.createElement(r.g7,{ref:u},a)})),f=e+"CollectionItemSlot",p="data-radix-collection-item",v=i.forwardRef(((e,t)=>{const{scope:n,children:a,...c}=e,u=i.useRef(null),s=(0,o.e)(t,u),d=l(f,n);return i.useEffect((()=>(d.itemMap.set(u,{ref:u,...c}),()=>{d.itemMap.delete(u)}))),i.createElement(r.g7,{[p]:"",ref:s},a)}));return[{Provider:e=>{const{scope:t,children:n}=e,r=i.useRef(null),o=i.useRef(new Map).current;return i.createElement(u,{scope:t,itemMap:o,collectionRef:r},n)},Slot:d,ItemSlot:v},function(t){const n=l(e+"CollectionConsumer",t);return i.useCallback((()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(n.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[n.collectionRef,n.itemMap])},c]}},6215:function(e,t,n){"use strict";n.d(t,{F:function(){return o},e:function(){return a}});var r=n(2784);function o(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function a(...e){return r.useCallback(o(...e),e)}},4540:function(e,t,n){"use strict";n.d(t,{k:function(){return o},b:function(){return a}});var r=n(2784);function o(e,t){const n=r.createContext(t);function o(e){const{children:t,...o}=e,a=r.useMemo((()=>o),Object.values(o));return r.createElement(n.Provider,{value:a},t)}return o.displayName=e+"Provider",[o,function(o){const a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw new Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[];const o=()=>{const t=n.map((e=>r.createContext(e)));return function(n){const o=(null==n?void 0:n[e])||t;return r.useMemo((()=>({[`__scope${e}`]:{...n,[e]:o}})),[n,o])}};return o.scopeName=e,[function(t,o){const a=r.createContext(o),i=n.length;function c(t){const{scope:n,children:o,...c}=t,u=(null==n?void 0:n[e][i])||a,l=r.useMemo((()=>c),Object.values(c));return r.createElement(u.Provider,{value:l},o)}return n=[...n,o],c.displayName=t+"Provider",[c,function(n,c){const u=(null==c?void 0:c[e][i])||a,l=r.useContext(u);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},i(o,...t)]}function i(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(e){const o=n.reduce(((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]})),{});return r.useMemo((()=>({[`__scope${t.scopeName}`]:o})),[o])}};return n.scopeName=t.scopeName,n}},2331:function(e,t,n){"use strict";n.d(t,{p8:function(){return E},jm:function(){return L},fC:function(){return A},xz:function(){return F},h_:function(){return W},aV:function(){return Z},VY:function(){return j},Dx:function(){return V},dk:function(){return U},x8:function(){return B}});var r=n(9575),o=n(9732),a=n(357),i=n(5070),c=n(2130),u=n(8245),l=n(6500),s=n(7334),d=n(1201),f=n(3597),p=n(6074),v=n(4540),m=n(6215),h=n(1816),g=n(2784),y=n(7896);const[b,E]=(0,v.b)("Dialog"),[w,C]=b("Dialog"),R=g.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=C("DialogTrigger",n),a=(0,m.e)(t,o.triggerRef);return g.createElement(c.W.button,(0,y.Z)({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":D(o.open)},r,{ref:a,onClick:(0,h.M)(e.onClick,o.onOpenToggle)}))})),S=g.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=C("DialogOverlay",e.__scopeDialog);return o.modal?g.createElement(u.z,{present:n||o.open},g.createElement(M,(0,y.Z)({},r,{ref:t}))):null})),M=g.forwardRef(((e,t)=>{const{__scopeDialog:n,...o}=e,i=C("DialogOverlay",n);return g.createElement(a.Z,{as:r.g7,allowPinchZoom:i.allowPinchZoom,shards:[i.contentRef]},g.createElement(c.W.div,(0,y.Z)({"data-state":D(i.open)},o,{ref:t,style:{pointerEvents:"auto",...o.style}})))})),x=g.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=C("DialogContent",e.__scopeDialog);return g.createElement(u.z,{present:n||o.open},o.modal?g.createElement(_,(0,y.Z)({},r,{ref:t})):g.createElement(O,(0,y.Z)({},r,{ref:t})))})),_=g.forwardRef(((e,t)=>{const n=C("DialogContent",e.__scopeDialog),r=g.useRef(null),a=(0,m.e)(t,n.contentRef,r);return g.useEffect((()=>{const e=r.current;if(e)return(0,o.R)(e)}),[]),g.createElement(I,(0,y.Z)({},e,{ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,h.M)(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:(0,h.M)(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:(0,h.M)(e.onFocusOutside,(e=>e.preventDefault()))}))})),O=g.forwardRef(((e,t)=>{const n=C("DialogContent",e.__scopeDialog),r=g.useRef(!1);return g.createElement(I,(0,y.Z)({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,a;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),r.current=!1},onInteractOutside:t=>{var o,a;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0);const i=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(i))&&t.preventDefault()}}))})),I=g.forwardRef(((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,u=C("DialogContent",n),l=g.useRef(null),f=(0,m.e)(t,l);return(0,i.EW)(),g.createElement(g.Fragment,null,g.createElement(s.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a},g.createElement(d.XB,(0,y.Z)({role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":D(u.open)},c,{ref:f,onDismiss:()=>u.onOpenChange(!1)}))),!1)})),T=g.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=C("DialogTitle",n);return g.createElement(c.W.h2,(0,y.Z)({id:o.titleId},r,{ref:t}))})),k=g.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=C("DialogDescription",n);return g.createElement(c.W.p,(0,y.Z)({id:o.descriptionId},r,{ref:t}))})),P=g.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=C("DialogClose",n);return g.createElement(c.W.button,(0,y.Z)({type:"button"},r,{ref:t,onClick:(0,h.M)(e.onClick,(()=>o.onOpenChange(!1)))}))}));function D(e){return e?"open":"closed"}const[L,N]=(0,v.k)("DialogTitleWarning",{contentName:"DialogContent",titleName:"DialogTitle",docsSlug:"dialog"}),A=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0,allowPinchZoom:c}=e,u=g.useRef(null),l=g.useRef(null),[s=!1,d]=(0,f.T)({prop:r,defaultProp:o,onChange:a});return g.createElement(w,{scope:t,triggerRef:u,contentRef:l,contentId:(0,p.M)(),titleId:(0,p.M)(),descriptionId:(0,p.M)(),open:s,onOpenChange:d,onOpenToggle:g.useCallback((()=>d((e=>!e))),[d]),modal:i,allowPinchZoom:c},n)},F=R,W=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=C("DialogPortal",t);return g.createElement(g.Fragment,null,g.Children.map(r,(e=>g.createElement(u.z,{present:n||a.open},g.createElement(l.c,{asChild:!0,container:o},e)))))},Z=S,j=x,V=T,U=k,B=P},1201:function(e,t,n){"use strict";n.d(t,{XB:function(){return p}});var r=n(6029),o=n(2784);var a=n(4680);let i,c=0;var u=n(6215),l=n(2130),s=n(1816),d=n(7896);const f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=o.forwardRef(((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:p,onPointerDownOutside:h,onFocusOutside:g,onInteractOutside:y,onDismiss:b,...E}=e,w=o.useContext(f),[C,R]=o.useState(null),[,S]=o.useState({}),M=(0,u.e)(t,(e=>R(e))),x=Array.from(w.layers),[_]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),O=x.indexOf(_),I=C?x.indexOf(C):-1,T=w.layersWithOutsidePointerEventsDisabled.size>0,k=I>=O,P=function(e){const t=(0,r.W)((e=>{const t=e.target,n=[...w.branches].some((e=>e.contains(t)));k&&!n&&(null==h||h(e),null==y||y(e),e.defaultPrevented||null==b||b())})),n=o.useRef(!1);return o.useEffect((()=>{const e=e=>{e.target&&!n.current&&m("dismissableLayer.pointerDownOutside",t,{originalEvent:e}),n.current=!1},r=window.setTimeout((()=>{document.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(r),document.removeEventListener("pointerdown",e)}}),[t]),{onPointerDownCapture:()=>n.current=!0}}(),D=function(e){const t=(0,r.W)((e=>{const t=e.target;[...w.branches].some((e=>e.contains(t)))||(null==g||g(e),null==y||y(e),e.defaultPrevented||null==b||b())})),n=o.useRef(!1);return o.useEffect((()=>{const e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",t,{originalEvent:e})};return document.addEventListener("focusin",e),()=>document.removeEventListener("focusin",e)}),[t]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}();return function(e){const t=(0,r.W)(e);o.useEffect((()=>{const e=e=>{"Escape"===e.key&&t(e)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[t])}((e=>{I===w.layers.size-1&&(null==p||p(e),e.defaultPrevented||null==b||b())})),function({disabled:e}){const t=o.useRef(!1);(0,a.b)((()=>{if(e){function e(){c--,0===c&&(document.body.style.pointerEvents=i)}function n(e){t.current="mouse"!==e.pointerType}return 0===c&&(i=document.body.style.pointerEvents),document.body.style.pointerEvents="none",c++,document.addEventListener("pointerup",n),()=>{t.current?document.addEventListener("click",e,{once:!0}):e(),document.removeEventListener("pointerup",n)}}}),[e])}({disabled:n}),o.useEffect((()=>{C&&(n&&w.layersWithOutsidePointerEventsDisabled.add(C),w.layers.add(C),v())}),[C,n,w]),o.useEffect((()=>()=>{C&&(w.layers.delete(C),w.layersWithOutsidePointerEventsDisabled.delete(C),v())}),[C,w]),o.useEffect((()=>{const e=()=>S({});return document.addEventListener("dismissableLayer.update",e),()=>document.removeEventListener("dismissableLayer.update",e)}),[]),o.createElement(l.W.div,(0,d.Z)({},E,{ref:M,style:{pointerEvents:T?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.M)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,s.M)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,s.M)(e.onPointerDownCapture,P.onPointerDownCapture)}))}));function v(){const e=new Event("dismissableLayer.update");document.dispatchEvent(e)}function m(e,t,n){const r=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});return t&&r.addEventListener(e,t,{once:!0}),!r.dispatchEvent(o)}},5070:function(e,t,n){"use strict";n.d(t,{EW:function(){return a}});var r=n(2784);let o=0;function a(){r.useEffect((()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:i()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),o--}}),[])}function i(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}},7334:function(e,t,n){"use strict";n.d(t,{M:function(){return l}});var r=n(6029),o=n(2130),a=n(6215),i=n(2784),c=n(7896);const u={bubbles:!1,cancelable:!0},l=i.forwardRef(((e,t)=>{const{loop:n=!1,trapped:l=!1,onMountAutoFocus:f,onUnmountAutoFocus:m,...h}=e,[g,y]=i.useState(null),b=(0,r.W)(f),E=(0,r.W)(m),w=i.useRef(null),C=(0,a.e)(t,(e=>y(e))),R=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect((()=>{if(l){function e(e){if(R.paused||!g)return;const t=e.target;g.contains(t)?w.current=t:p(w.current,{select:!0})}function t(e){!R.paused&&g&&(g.contains(e.relatedTarget)||p(w.current,{select:!0}))}return document.addEventListener("focusin",e),document.addEventListener("focusout",t),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t)}}}),[l,g,R.paused]),i.useEffect((()=>{if(g){v.add(R);const e=document.activeElement;if(!g.contains(e)){const t=new Event("focusScope.autoFocusOnMount",u);g.addEventListener("focusScope.autoFocusOnMount",b),g.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(p(r,{select:t}),document.activeElement!==n)return}(s(g).filter((e=>"A"!==e.tagName)),{select:!0}),document.activeElement===e&&p(g))}return()=>{g.removeEventListener("focusScope.autoFocusOnMount",b),setTimeout((()=>{const t=new Event("focusScope.autoFocusOnUnmount",u);g.addEventListener("focusScope.autoFocusOnUnmount",E),g.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),g.removeEventListener("focusScope.autoFocusOnUnmount",E),v.remove(R)}),0)}}}),[g,b,E,R]);const S=i.useCallback((e=>{if(!n&&!l)return;if(R.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){const t=e.currentTarget,[o,a]=function(e){const t=s(e);return[d(t,e),d(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&p(a,{select:!0})):(e.preventDefault(),n&&p(o,{select:!0})):r===t&&e.preventDefault()}}),[n,l,R.paused]);return i.createElement(o.W.div,(0,c.Z)({tabIndex:-1},h,{ref:C,onKeyDown:S}))}));function s(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function d(e,t){for(const n of e)if(!f(n,{upTo:t}))return n}function f(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function p(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}const v=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=m(e,t),e.unshift(t)},remove(t){var n;e=m(e,t),null===(n=e[0])||void 0===n||n.resume()}}}();function m(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}},6074:function(e,t,n){"use strict";var r;n.d(t,{M:function(){return u}});var o=n(4680),a=n(2784);const i=(r||(r=n.t(a,2)))["useId".toString()]||(()=>{});let c=0;function u(e){const[t,n]=a.useState(i());return(0,o.b)((()=>{e||n((e=>null!=e?e:String(c++)))}),[e]),e||(t?`radix-${t}`:"")}},9674:function(e,t,n){"use strict";n.d(t,{t0:function(){return f},fC:function(){return p}});var r=n(6074),o=n(2130),a=n(6215),i=n(4540),c=n(2784),u=n(7896);const[l,s]=(0,i.k)("Label",{id:void 0,controlRef:{current:null}}),d=c.forwardRef(((e,t)=>{const{htmlFor:n,id:i,...s}=e,d=c.useRef(null),f=c.useRef(null),p=(0,a.e)(t,f),v=(0,r.M)(i);return c.useEffect((()=>{if(n){const e=document.getElementById(n);if(f.current&&e){const t=()=>e.getAttribute("aria-labelledby"),n=[v,t()].filter(Boolean).join(" ");return e.setAttribute("aria-labelledby",n),d.current=e,()=>{var n;const r=null===(n=t())||void 0===n?void 0:n.replace(v,"");""===r?e.removeAttribute("aria-labelledby"):r&&e.setAttribute("aria-labelledby",r)}}}}),[v,n]),c.createElement(l,{id:v,controlRef:d},c.createElement(o.W.span,(0,u.Z)({role:"label",id:v},s,{ref:p,onMouseDown:t=>{var n;null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault()},onClick:t=>{var n;if(null===(n=e.onClick)||void 0===n||n.call(e,t),!d.current||t.defaultPrevented)return;const r=d.current.contains(t.target),o=!0===t.isTrusted;!r&&o&&(d.current.click(),d.current.focus())}})))})),f=e=>{const t=s("LabelConsumer"),{controlRef:n}=t;return c.useEffect((()=>{e&&(n.current=e)}),[e,n]),t.id},p=d},3251:function(e,t,n){"use strict";n.d(t,{VY:function(){return le},z$:function(){return ue},ck:function(){return ae},rU:function(){return ce},aV:function(){return oe},fC:function(){return re},xz:function(){return ie},l_:function(){return se}});var r=n(9401),o=n(6029),a=n(4680),i=n(9586),c=n(1201),u=n(8210),l=n(6074),s=n(8245),d=n(2784);function f(e,t){const[n,r]=d.useState("ltr"),[o,a]=d.useState(),i=d.useRef(0);return d.useEffect((()=>{if(void 0===t&&null!=e&&e.parentElement){const t=getComputedStyle(e.parentElement);a(t)}}),[e,t]),d.useEffect((()=>(void 0===t&&function e(){i.current=requestAnimationFrame((()=>{const t=null==o?void 0:o.direction;t&&r(t),e()}))}(),()=>cancelAnimationFrame(i.current))),[o,t,r]),t||n}var p=n(6215),v=n(3597),m=n(2130),h=n(1816),g=n(4540),y=n(8316),b=n(7896);const[E,w,C]=(0,u.B)("NavigationMenu"),[R,S,M]=(0,u.B)("NavigationMenu"),[x,_]=(0,g.b)("NavigationMenu",[C,M]),[O,I]=x("NavigationMenu"),[T,k]=x("NavigationMenu"),P=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",...c}=e,[u,l]=d.useState(null),s=(0,p.e)(t,(e=>l(e)));return d.createElement(D,{scope:n,isRootMenu:!0,value:r,onValueChange:o,defaultValue:a,dir:f(u,e.dir),orientation:i,rootNavigationMenu:u},d.createElement(m.W.nav,(0,b.Z)({"aria-label":"Main","data-orientation":i},c,{ref:s})))})),D=e=>{const{scope:t,isRootMenu:n,rootNavigationMenu:r,value:o,onValueChange:a,defaultValue:c,dir:u,orientation:s,children:f}=e,[p,m]=d.useState(null),[h,g]=d.useState(new Map),[y,b]=d.useState(null),w=d.useRef(0),[C="",R]=(0,v.T)({prop:o,onChange:a,defaultProp:c});return d.useEffect((()=>()=>window.clearTimeout(w.current)),[w]),d.createElement(O,{scope:t,isRootMenu:n,rootNavigationMenu:r,value:C,previousValue:(0,i.D)(C),baseId:(0,l.M)(),dir:u,orientation:s,viewport:p,onViewportChange:m,indicatorTrack:y,onIndicatorTrackChange:b,onItemOver:d.useCallback((e=>{n&&window.clearTimeout(w.current),R(e)}),[R,n]),onItemLeave:d.useCallback((()=>{n&&(window.clearTimeout(w.current),w.current=window.setTimeout((()=>R("")),150))}),[R,n]),onItemSelect:d.useCallback((e=>{R((t=>n&&t===e?"":e))}),[R,n]),onItemDismiss:d.useCallback((()=>R("")),[R]),onViewportContentChange:d.useCallback(((e,t)=>{g((n=>(n.set(e,t),new Map(n))))}),[]),onViewportContentRemove:d.useCallback((e=>{g((t=>t.has(e)?(t.delete(e),new Map(t)):t))}),[])},d.createElement(E.Provider,{scope:t},d.createElement(T,{scope:t,items:h},f)))},L=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,...r}=e,o=I("NavigationMenuList",n),a=d.createElement(m.W.ul,(0,b.Z)({"data-orientation":o.orientation},r,{ref:t}));return d.createElement(m.W.div,{style:{position:"relative"},ref:o.onIndicatorTrackChange},d.createElement(E.Slot,{scope:n},o.isRootMenu?d.createElement(H,{asChild:!0},a):a))})),[N,A]=x("NavigationMenuItem"),F=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,value:r,...o}=e,a=(0,l.M)(),i=r||a||"LEGACY_REACT_AUTO_VALUE",c=d.useRef(null),u=d.useRef(null),s=d.useRef(null),f=d.useRef((()=>{})),p=d.useCallback(((e="start")=>{if(c.current){f.current();const t=X(c.current);t.length&&G("start"===e?t:t.reverse())}}),[]),v=d.useCallback((()=>{if(c.current){const e=X(c.current);e.length&&(f.current=function(e){return e.forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")})),()=>{e.forEach((e=>{const t=e.dataset.tabindex;e.setAttribute("tabindex",t)}))}}(e))}}),[]);return d.createElement(N,{scope:n,value:i,triggerRef:u,contentRef:c,focusProxyRef:s,onEntryKeyDown:p,onFocusProxyEnter:p,onRootContentClose:v,onContentFocusOutside:v},d.createElement(m.W.li,(0,b.Z)({},o,{ref:t})))})),W=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,disabled:o,...a}=e,i=I("NavigationMenuTrigger",e.__scopeNavigationMenu),c=A("NavigationMenuTrigger",e.__scopeNavigationMenu),u=d.useRef(null),l=(0,p.e)(u,c.triggerRef,t),s=ee(i.baseId,c.value),f=te(i.baseId,c.value),v=d.useRef(!1),g=c.value===i.value;return d.createElement(d.Fragment,null,d.createElement(E.ItemSlot,{scope:n,value:c.value},d.createElement(q,{asChild:!0},d.createElement(m.W.button,(0,b.Z)({id:s,disabled:o,"data-disabled":o?"":void 0,"data-state":Q(g),"aria-expanded":g,"aria-controls":f},a,{ref:l,onPointerEnter:(0,h.M)(e.onPointerEnter,(()=>{v.current=!1})),onPointerMove:(0,h.M)(e.onPointerMove,ne((()=>{o||v.current||i.onItemOver(c.value)}))),onPointerLeave:(0,h.M)(e.onPointerLeave,ne((()=>{o||i.onItemLeave()}))),onClick:(0,h.M)(e.onClick,(()=>{i.onItemSelect(c.value),v.current=g})),onKeyDown:(0,h.M)(e.onKeyDown,(e=>{const t={horizontal:"ArrowDown",vertical:"rtl"===i.dir?"ArrowLeft":"ArrowRight"}[i.orientation];g&&e.key===t&&(c.onEntryKeyDown(),e.preventDefault())}))})))),g&&d.createElement(d.Fragment,null,d.createElement(r.f,{"aria-hidden":!0,tabIndex:0,ref:c.focusProxyRef,onFocus:e=>{const t=c.contentRef.current,n=e.relatedTarget,r=n===u.current,o=null==t?void 0:t.contains(n);!r&&o||c.onFocusProxyEnter(r?"start":"end")}}),i.viewport&&d.createElement("span",{"aria-owns":f})))})),Z=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,active:r,...o}=e;return d.createElement(q,{asChild:!0},d.createElement(m.W.a,(0,b.Z)({"data-active":r?"":void 0,"aria-current":r?"page":void 0},o,{ref:t})))})),j=d.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=I("NavigationMenuIndicator",e.__scopeNavigationMenu),a=Boolean(o.value);return o.indicatorTrack?y.createPortal(d.createElement(s.z,{present:n||a},d.createElement(V,(0,b.Z)({},r,{ref:t}))),o.indicatorTrack):null})),V=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,...r}=e,o=I("NavigationMenuIndicator",n),a=w(n),[i,c]=d.useState(null),[u,l]=d.useState(null),s="horizontal"===o.orientation,f=Boolean(o.value);d.useEffect((()=>{var e;const t=null===(e=a().find((e=>e.value===o.value)))||void 0===e?void 0:e.ref.current;t&&c(t)}),[a,o.value]);const p=()=>{i&&l({size:s?i.offsetWidth:i.offsetHeight,offset:s?i.offsetLeft:i.offsetTop})};return J(i,p),J(o.indicatorTrack,p),u?d.createElement(m.W.div,(0,b.Z)({"aria-hidden":!0,"data-state":f?"visible":"hidden","data-orientation":o.orientation},r,{ref:t,style:{position:"absolute",...s?{left:0,width:u.size+"px",transform:`translateX(${u.offset}px)`}:{top:0,height:u.size+"px",transform:`translateY(${u.offset}px)`},...r.style}})):null})),U=d.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=I("NavigationMenuContent",e.__scopeNavigationMenu),a=A("NavigationMenuContent",e.__scopeNavigationMenu),i=(0,p.e)(a.contentRef,t),c=a.value===o.value,u={value:a.value,triggerRef:a.triggerRef,focusProxyRef:a.focusProxyRef,onContentFocusOutside:a.onContentFocusOutside,onRootContentClose:a.onRootContentClose,...r};return o.viewport?d.createElement(B,(0,b.Z)({forceMount:n},u,{ref:i})):d.createElement(s.z,{present:n||c},d.createElement($,(0,b.Z)({"data-state":Q(c)},u,{ref:i,onPointerEnter:(0,h.M)(e.onPointerEnter,(()=>{o.onItemOver(a.value)})),onPointerLeave:(0,h.M)(e.onPointerLeave,ne(o.onItemLeave)),style:{pointerEvents:!c&&o.isRootMenu?"none":void 0,...u.style}})))})),B=d.forwardRef(((e,t)=>{const n=I("NavigationMenuContent",e.__scopeNavigationMenu),{onViewportContentChange:r,onViewportContentRemove:o}=n;return(0,a.b)((()=>{r(e.value,{ref:t,...e})}),[e,t,r]),(0,a.b)((()=>()=>o(e.value)),[e.value,o]),null})),$=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,value:r,triggerRef:o,focusProxyRef:a,onRootContentClose:i,onContentFocusOutside:u,...l}=e,s=I("NavigationMenuContent",n),f=d.useRef(null),v=(0,p.e)(f,t),m=ee(s.baseId,r),g=te(s.baseId,r),y=w(n),E=d.useRef(null),{onItemDismiss:C}=s;d.useEffect((()=>{const e=f.current;if(s.isRootMenu&&e){const t=()=>{var t;C(),i(),e.contains(document.activeElement)&&(null===(t=o.current)||void 0===t||t.focus())};return e.addEventListener("navigationMenu.contentDismiss",t),()=>e.removeEventListener("navigationMenu.contentDismiss",t)}}),[s.isRootMenu,e.value,o,C,i]);const R=d.useMemo((()=>{const e=y().map((e=>e.value));"rtl"===s.dir&&e.reverse();const t=e.indexOf(s.value),n=e.indexOf(s.previousValue),o=r===s.value,a=n===e.indexOf(r);if(!o&&!a)return E.current;const i=(()=>{if(t!==n){if(o&&-1!==n)return t>n?"from-end":"from-start";if(a&&-1!==t)return t>n?"to-start":"to-end"}return null})();return E.current=i,i}),[s.previousValue,s.value,s.dir,y,r]);return d.createElement(H,{asChild:!0},d.createElement(c.XB,(0,b.Z)({id:g,"aria-labelledby":m,"data-motion":R,"data-orientation":s.orientation},l,{ref:v,onDismiss:()=>{var e;const t=new Event("navigationMenu.contentDismiss",{bubbles:!0,cancelable:!0});null===(e=f.current)||void 0===e||e.dispatchEvent(t)},onFocusOutside:(0,h.M)(e.onFocusOutside,(e=>{var t;u();const n=e.target;null!==(t=s.rootNavigationMenu)&&void 0!==t&&t.contains(n)&&e.preventDefault()})),onPointerDownOutside:(0,h.M)(e.onPointerDownOutside,(e=>{var t;const n=e.target,r=y().some((e=>{var t;return null===(t=e.ref.current)||void 0===t?void 0:t.contains(n)})),o=s.isRootMenu&&(null===(t=s.viewport)||void 0===t?void 0:t.contains(n));(r||o||!s.isRootMenu)&&e.preventDefault()})),onKeyDown:(0,h.M)(e.onKeyDown,(e=>{const t=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!t){const t=X(e.currentTarget),r=document.activeElement,o=t.findIndex((e=>e===r));var n;G(e.shiftKey?t.slice(0,o).reverse():t.slice(o+1,t.length))?e.preventDefault():null===(n=a.current)||void 0===n||n.focus()}}))})))})),z=d.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=I("NavigationMenuViewport",e.__scopeNavigationMenu),a=Boolean(o.value);return d.createElement(s.z,{present:n||a},d.createElement(K,(0,b.Z)({},r,{ref:t})))})),K=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,children:r,...o}=e,a=I("NavigationMenuViewport",n),i=(0,p.e)(t,a.onViewportChange),c=k("NavigationMenuContent",e.__scopeNavigationMenu),[u,l]=d.useState(null),[f,v]=d.useState(null),g=u?(null==u?void 0:u.width)+"px":void 0,y=u?(null==u?void 0:u.height)+"px":void 0,E=Boolean(a.value),w=E?a.value:a.previousValue;return J(f,(()=>{f&&l({width:f.offsetWidth,height:f.offsetHeight})})),d.createElement(m.W.div,(0,b.Z)({"data-state":Q(E),"data-orientation":a.orientation},o,{ref:i,style:{pointerEvents:!E&&a.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":g,"--radix-navigation-menu-viewport-height":y,...o.style},onPointerEnter:(0,h.M)(e.onPointerEnter,(()=>{a.onItemOver(w)})),onPointerLeave:(0,h.M)(e.onPointerLeave,ne(a.onItemLeave))}),Array.from(c.items).map((([e,{ref:t,forceMount:n,...r}])=>{const o=w===e;return d.createElement(s.z,{key:e,present:n||o},d.createElement($,(0,b.Z)({},r,{ref:(0,p.F)(t,(e=>{o&&e&&v(e)}))})))})))})),H=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,...r}=e,o=I("FocusGroup",n);return d.createElement(R.Provider,{scope:n},d.createElement(R.Slot,{scope:n},d.createElement(m.W.div,(0,b.Z)({dir:o.dir},r,{ref:t}))))})),Y=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],q=d.forwardRef(((e,t)=>{const{__scopeNavigationMenu:n,...r}=e,o=S(n),a=I("FocusGroupItem",n);return d.createElement(R.ItemSlot,{scope:n},d.createElement(m.W.button,(0,b.Z)({},r,{ref:t,onKeyDown:(0,h.M)(e.onKeyDown,(e=>{if(["Home","End",...Y].includes(e.key)){let t=o().map((e=>e.ref.current));if(["rtl"===a.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(e.key)&&t.reverse(),Y.includes(e.key)){const n=t.indexOf(e.currentTarget);t=t.slice(n+1)}setTimeout((()=>G(t))),e.preventDefault()}}))})))}));function X(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function G(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}function J(e,t){const n=(0,o.W)(t);(0,a.b)((()=>{let t=0;if(e){const r=new ResizeObserver((()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)}));return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}}),[e,n])}function Q(e){return e?"open":"closed"}function ee(e,t){return`${e}-trigger-${t}`}function te(e,t){return`${e}-content-${t}`}function ne(e){return t=>"mouse"===t.pointerType?e(t):void 0}const re=P,oe=L,ae=F,ie=W,ce=Z,ue=j,le=U,se=z},6500:function(e,t,n){"use strict";n.d(t,{h_:function(){return u},c:function(){return l}});var r=n(2130),o=n(4680),a=n(8316),i=n(2784),c=n(7896);const u=i.forwardRef(((e,t)=>{var n,u;const{containerRef:l,style:s,...d}=e,f=null!==(n=null==l?void 0:l.current)&&void 0!==n?n:null===globalThis||void 0===globalThis||null===(u=globalThis.document)||void 0===u?void 0:u.body,[,p]=i.useState({});return(0,o.b)((()=>{p({})}),[]),f?a.createPortal(i.createElement(r.W.div,(0,c.Z)({"data-radix-portal":""},d,{ref:t,style:f===document.body?{position:"absolute",top:0,left:0,zIndex:2147483647,...s}:void 0})),f):null})),l=i.forwardRef(((e,t)=>{var n;const{container:o=(null===globalThis||void 0===globalThis||null===(n=globalThis.document)||void 0===n?void 0:n.body),...u}=e;return o?a.createPortal(i.createElement(r.W.div,(0,c.Z)({},u,{ref:t})),o):null}))},8245:function(e,t,n){"use strict";n.d(t,{z:function(){return i}});var r=n(4680),o=n(6215),a=n(2784);const i=e=>{const{present:t,children:n}=e,i=function(e){const[t,n]=a.useState(),o=a.useRef({}),i=a.useRef(e),u=a.useRef("none"),l=e?"mounted":"unmounted",[s,d]=function(e,t){return a.useReducer(((e,n)=>{const r=t[e][n];return null!=r?r:e}),e)}(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect((()=>{const e=c(o.current);u.current="mounted"===s?e:"none"}),[s]),(0,r.b)((()=>{const t=o.current,n=i.current;if(n!==e){const r=u.current,o=c(t);if(e)d("MOUNT");else if("none"===o||"none"===(null==t?void 0:t.display))d("UNMOUNT");else{const e=r!==o;d(n&&e?"ANIMATION_OUT":"UNMOUNT")}i.current=e}}),[e,d]),(0,r.b)((()=>{if(t){const e=e=>{const n=c(o.current).includes(e.animationName);e.target===t&&n&&d("ANIMATION_END")},n=e=>{e.target===t&&(u.current=c(o.current))};return t.addEventListener("animationstart",n),t.addEventListener("animationcancel",e),t.addEventListener("animationend",e),()=>{t.removeEventListener("animationstart",n),t.removeEventListener("animationcancel",e),t.removeEventListener("animationend",e)}}d("ANIMATION_END")}),[t,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:a.useCallback((e=>{e&&(o.current=getComputedStyle(e)),n(e)}),[])}}(t),u="function"==typeof n?n({present:i.isPresent}):a.Children.only(n),l=(0,o.e)(i.ref,u.ref);return"function"==typeof n||i.isPresent?a.cloneElement(u,{ref:l}):null};function c(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},2130:function(e,t,n){"use strict";n.d(t,{W:function(){return i}});var r=n(9575),o=n(2784),a=n(7896);const i=["a","button","div","h2","h3","img","li","nav","ol","p","span","svg","ul"].reduce(((e,t)=>({...e,[t]:o.forwardRef(((e,n)=>{const{asChild:i,...c}=e,u=i?r.g7:t;return o.useEffect((()=>{window[Symbol.for("radix-ui")]=!0}),[]),o.createElement(u,(0,a.Z)({},c,{ref:n}))}))})),{})},8432:function(e,t,n){"use strict";n.d(t,{VY:function(){return ce},ZA:function(){return le},JO:function(){return ie},ck:function(){return de},wU:function(){return pe},eT:function(){return fe},__:function(){return se},fC:function(){return re},$G:function(){return me},u_:function(){return ve},Z0:function(){return he},xz:function(){return oe},B4:function(){return ae},l_:function(){return ue}});var r=n(357),o=n(9732),a=n(9401),i=n(9586),c=n(4680),u=n(3597),l=n(6029),s=n(2130),d=n(6500),f=n(9674),p=n(6074),v=n(7334),m=n(1201),h=n(4540),g=n(6215),y=n(8210),b=n(1816);function E(e,[t,n]){return Math.min(n,Math.max(t,e))}var w=n(8316),C=n(2784),R=n(7896);const S=[" ","Enter","ArrowUp","ArrowDown"],M=[" ","Enter"],[x,_,O]=(0,y.B)("Select"),[I,T]=(0,h.b)("Select",[O]),[k,P]=I("Select"),D=C.forwardRef(((e,t)=>{const{__scopeSelect:n,disabled:r=!1,"aria-labelledby":o,...a}=e,i=P("SelectTrigger",n),c=(0,g.e)(t,i.onTriggerChange),u=_(n),l=(0,f.t0)(i.trigger),d=o||l,[p,v,m]=te((e=>{const t=u().filter((e=>!e.disabled)),n=t.find((e=>e.value===i.value)),r=ne(t,e,n);void 0!==r&&i.onValueChange(r.value)})),h=()=>{r||(i.onOpenChange(!0),m())};return C.createElement(s.W.button,(0,R.Z)({type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-autocomplete":"none","aria-labelledby":d,dir:i.dir,disabled:r,"data-disabled":r?"":void 0},a,{ref:c,onPointerDown:(0,b.M)(a.onPointerDown,(e=>{e.target.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(h(),i.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())})),onKeyDown:(0,b.M)(a.onKeyDown,(e=>{const t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),t&&" "===e.key||S.includes(e.key)&&(h(),e.preventDefault())}))}))})),L=C.forwardRef(((e,t)=>{const{__scopeSelect:n,className:r,style:o,...a}=e,i=P("SelectValue",n),{onValueNodeHasChildrenChange:c}=i,u=void 0!==e.children,l=(0,g.e)(t,i.onValueNodeChange);return C.useEffect((()=>{c(u)}),[c,u]),C.createElement(s.W.span,(0,R.Z)({},a,{ref:l,style:{pointerEvents:"none"}}))})),N=C.forwardRef(((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return C.createElement(s.W.span,(0,R.Z)({"aria-hidden":!0},o,{ref:t}),r||"\u25bc")})),A=C.forwardRef(((e,t)=>{const n=P("SelectContent",e.__scopeSelect),[r,o]=C.useState();return(0,c.b)((()=>{o(new DocumentFragment)}),[]),n.open?C.createElement(Z,(0,R.Z)({},e,{ref:t})):r?w.createPortal(C.createElement(F,{scope:e.__scopeSelect},C.createElement(x.Slot,{scope:e.__scopeSelect},C.createElement("div",null,e.children))),r):null})),[F,W]=I("SelectContent"),Z=C.forwardRef(((e,t)=>{const{__scopeSelect:n,onCloseAutoFocus:a,children:i,...u}=e,l=P("SelectContent",n),[s,f]=C.useState(null),[p,h]=C.useState(null),[y,w]=C.useState(null),S=(0,g.e)(t,(e=>h(e))),[M,x]=C.useState(null),[O,I]=C.useState(null),T=_(n),[k,D]=C.useState(!1),L=C.useRef(!0),N=C.useRef(!1);C.useEffect((()=>{if(p)return(0,o.R)(p)}),[p]);const A=C.useCallback((e=>{const[t,...n]=T().map((e=>e.ref.current)),[r]=n.slice(-1),o=document.activeElement;for(const a of e){if(a===o)return;if(null==a||a.scrollIntoView({block:"nearest"}),a===t&&y&&(y.scrollTop=0),a===r&&y&&(y.scrollTop=y.scrollHeight),null==a||a.focus(),document.activeElement!==o)return}}),[T,y]),W=C.useCallback((()=>{if(l.trigger&&l.valueNode&&s&&p&&y&&M&&O){const e=l.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=O.getBoundingClientRect();if("rtl"!==l.dir){const o=r.left-t.left,a=n.left-o,i=e.left-a,c=e.width+i,u=Math.max(c,t.width),l=E(a,[10,window.innerWidth-10-u]);s.style.minWidth=c+"px",s.style.left=l+"px"}else{const o=t.right-r.right,a=window.innerWidth-n.right-o,i=window.innerWidth-e.right-a,c=e.width+i,u=Math.max(c,t.width),l=E(a,[10,window.innerWidth-10-u]);s.style.minWidth=c+"px",s.style.right=l+"px"}const o=window.innerHeight-20,a=5*M.offsetHeight,i=y.scrollHeight,c=window.getComputedStyle(p),u=parseInt(c.borderTopWidth,10),d=parseInt(c.paddingTop,10),f=parseInt(c.borderBottomWidth,10),v=u+d+i+parseInt(c.paddingBottom,10)+f,m=e.top+e.height/2-10,h=o-m,g=M.offsetHeight/2,b=u+(M.offsetTop+g),w=v-b;if(b<=m){s.style.bottom="0px";const e=p.clientHeight-y.offsetTop-y.offsetHeight,t=b+Math.max(h,g+e+f);s.style.height=t+"px"}else{s.style.top="0px";const e=Math.max(m,u+y.offsetTop+g)+w;s.style.height=e+"px",y.scrollTop=b-m+y.offsetTop}s.style.margin="10px 0",s.style.minHeight=a+"px",s.style.maxHeight=o+"px",D(!0),requestAnimationFrame((()=>N.current=!0))}}),[l.trigger,l.valueNode,s,p,y,M,O,l.dir]);(0,c.b)((()=>W()),[W]);const Z=C.useCallback((()=>A([M,p])),[A,M,p]);C.useEffect((()=>{k&&Z()}),[k,Z]);const j=C.useCallback((e=>{e&&!0===L.current&&(W(),Z(),L.current=!1)}),[W,Z]),{onOpenChange:V,triggerPointerDownPosRef:U}=l;C.useEffect((()=>{if(p){let e={x:0,y:0};const t=t=>{var n,r,o,a;e={x:Math.abs(Math.round(t.pageX)-(null!==(n=null===(r=U.current)||void 0===r?void 0:r.x)&&void 0!==n?n:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(a=U.current)||void 0===a?void 0:a.y)&&void 0!==o?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():p.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[p,V,U]),C.useEffect((()=>{const e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[V]);const[B,$]=te((e=>{const t=T().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=ne(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),z=C.useCallback((()=>null==p?void 0:p.focus()),[p]);return C.createElement(d.h_,null,C.createElement(r.Z,null,C.createElement("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:0}},C.createElement(v.M,{asChild:!0,trapped:l.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,b.M)(a,(e=>{var t;null===(t=l.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}))},C.createElement(m.XB,(0,R.Z)({role:"listbox",id:l.contentId,"data-state":l.open?"open":"closed",dir:l.dir,onContextMenu:e=>e.preventDefault()},u,{ref:S,style:{display:"flex",flexDirection:"column",boxSizing:"border-box",maxHeight:"100%",outline:"none",...u.style},disableOutsidePointerEvents:!0,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>l.onOpenChange(!1),onKeyDown:(0,b.M)(u.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=T().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>A(t))),e.preventDefault()}}))}),C.createElement(F,{scope:n,contentWrapper:s,content:p,viewport:y,onViewportChange:w,selectedItem:M,onSelectedItemChange:x,selectedItemText:O,onSelectedItemTextChange:I,onScrollButtonChange:j,onItemLeave:z,isPositioned:k,shouldExpandOnScrollRef:N,searchRef:B},i))))))})),j=C.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=W("SelectViewport",n),a=(0,g.e)(t,o.onViewportChange),i=C.useRef(0);return C.createElement(C.Fragment,null,C.createElement("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"}}),C.createElement(x.Slot,{scope:n},C.createElement(s.W.div,(0,R.Z)({"data-radix-select-viewport":"",role:"presentation"},r,{ref:a,style:{position:"relative",flex:1,overflow:"auto",...r.style},onScroll:(0,b.M)(r.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=o;if(r.current&&n){const e=Math.abs(i.current-t.scrollTop);if(e>0){const r=window.innerHeight-20,o=parseFloat(n.style.minHeight),a=parseFloat(n.style.height),i=Math.max(o,a);if(i<r){const o=i+e,a=Math.min(r,o),c=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=c>0?c:0,n.style.justifyContent="flex-end")}}}i.current=t.scrollTop}))}))))})),[V,U]=I("SelectGroup"),B=C.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=(0,p.M)();return C.createElement(V,{scope:n,id:o},C.createElement(s.W.div,(0,R.Z)({role:"group","aria-labelledby":o},r,{ref:t})))})),$=C.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=U("SelectLabel",n);return C.createElement(s.W.div,(0,R.Z)({id:o.id},r,{ref:t}))})),[z,K]=I("SelectItem"),H=C.forwardRef(((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:a,...i}=e,c=P("SelectItem",n),u=W("SelectItem",n),l=c.value===r,[d,f]=C.useState(null!=a?a:""),[v,m]=C.useState(!1),h=(0,g.e)(t,l?u.onSelectedItemChange:void 0),y=(0,p.M)(),E=()=>{o||(c.onValueChange(r),c.onOpenChange(!1))};return C.createElement(z,{scope:n,value:r,textId:y,isSelected:l,onItemTextChange:C.useCallback((e=>{f((t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()}))}),[])},C.createElement(x.ItemSlot,{scope:n,value:r,disabled:o,textValue:d},C.createElement(s.W.div,(0,R.Z)({role:"option","aria-labelledby":y,"aria-selected":l&&v,"data-state":l?"active":"inactive","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1},i,{ref:h,onFocus:(0,b.M)(i.onFocus,(()=>m(!0))),onBlur:(0,b.M)(i.onBlur,(()=>m(!1))),onPointerUp:(0,b.M)(i.onPointerUp,E),onPointerMove:(0,b.M)(i.onPointerMove,(e=>{o?u.onItemLeave():e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:(0,b.M)(i.onPointerLeave,(e=>{e.currentTarget===document.activeElement&&u.onItemLeave()})),onKeyDown:(0,b.M)(i.onKeyDown,(e=>{""!==u.searchRef.current&&" "===e.key||(M.includes(e.key)&&E()," "===e.key&&e.preventDefault())}))}))))})),Y=C.forwardRef(((e,t)=>{var n;const{__scopeSelect:r,className:o,style:a,...i}=e,c=P("SelectItemText",r),u=W("SelectItemText",r),l=K("SelectItemText",r),d=C.useRef(null),f=(0,g.e)(t,d,l.onItemTextChange,l.isSelected?u.onSelectedItemTextChange:void 0);return C.createElement(C.Fragment,null,C.createElement(s.W.span,(0,R.Z)({id:l.textId},i,{ref:f})),l.isSelected&&c.valueNode&&!c.valueNodeHasChildren?w.createPortal(i.children,c.valueNode):null,c.bubbleSelect?w.createPortal(C.createElement("option",{value:l.value},null===(n=d.current)||void 0===n?void 0:n.textContent),c.bubbleSelect):null)})),q=C.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return K("SelectItemIndicator",n).isSelected?C.createElement(s.W.span,(0,R.Z)({"aria-hidden":!0},r,{ref:t})):null})),X=C.forwardRef(((e,t)=>{const n=W("SelectScrollUpButton",e.__scopeSelect),[r,o]=C.useState(!1),a=(0,g.e)(t,n.onScrollButtonChange);return C.useEffect((()=>{if(n.viewport&&n.isPositioned){const e=n.viewport;function t(){const t=e.scrollTop>0;o(t)}return t(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}}),[n.viewport,n.isPositioned]),r?C.createElement(J,(0,R.Z)({},e,{ref:a,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}})):null})),G=C.forwardRef(((e,t)=>{const n=W("SelectScrollDownButton",e.__scopeSelect),[r,o]=C.useState(!1),a=(0,g.e)(t,n.onScrollButtonChange);return C.useEffect((()=>{if(n.viewport&&n.isPositioned){const e=n.viewport;function t(){const t=e.scrollHeight-e.clientHeight,n=Math.ceil(e.scrollTop)<t;o(n)}return t(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}}),[n.viewport,n.isPositioned]),r?C.createElement(J,(0,R.Z)({},e,{ref:a,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}})):null})),J=C.forwardRef(((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,a=W("SelectScrollButton",n),i=C.useRef(null),u=_(n),l=C.useCallback((()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)}),[]);return C.useEffect((()=>()=>l()),[l]),(0,c.b)((()=>{var e;const t=u().find((e=>e.ref.current===document.activeElement));null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})}),[u]),C.createElement(s.W.div,(0,R.Z)({"aria-hidden":!0},o,{ref:t,style:{flexShrink:0,...o.style},onPointerMove:(0,b.M)(o.onPointerMove,(()=>{a.onItemLeave(),null===i.current&&(i.current=window.setInterval(r,50))})),onPointerLeave:(0,b.M)(o.onPointerLeave,(()=>{l()}))}))})),Q=C.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return C.createElement(s.W.div,(0,R.Z)({"aria-hidden":!0},r,{ref:t}))})),ee=C.forwardRef(((e,t)=>{const{value:n,...r}=e,o=C.useRef(null),c=(0,g.e)(t,o),u=(0,i.D)(n);return C.useEffect((()=>{const e=o.current,t=window.HTMLSelectElement.prototype,r=Object.getOwnPropertyDescriptor(t,"value").set;if(u!==n&&r){const t=new Event("change",{bubbles:!0});r.call(e,n),e.dispatchEvent(t)}}),[u,n]),C.createElement(a.T,{asChild:!0},C.createElement("select",(0,R.Z)({},r,{ref:c,defaultValue:n})))}));function te(e){const t=(0,l.W)(e),n=C.useRef(""),r=C.useRef(0),o=C.useCallback((e=>{const o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout((()=>e("")),1e3))}(o)}),[t]),a=C.useCallback((()=>{n.current="",window.clearTimeout(r.current)}),[]);return C.useEffect((()=>()=>window.clearTimeout(r.current)),[]),[n,o,a]}function ne(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let a=(i=e,c=Math.max(o,0),i.map(((e,t)=>i[(c+t)%i.length])));var i,c;1===r.length&&(a=a.filter((e=>e!==n)));const u=a.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return u!==n?u:void 0}const re=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:a,value:i,defaultValue:c,onValueChange:l,dir:s,name:d,autoComplete:f}=e,[v,m]=C.useState(null),[h,g]=C.useState(null),[y,b]=C.useState(!1),[E=!1,w]=(0,u.T)({prop:r,defaultProp:o,onChange:a}),[R="",S]=(0,u.T)({prop:i,defaultProp:c,onChange:l}),M=!v||Boolean(v.closest("form")),[_,O]=C.useState(null),I=C.useRef(null);return C.createElement(k,{scope:t,trigger:v,onTriggerChange:m,valueNode:h,onValueNodeChange:g,valueNodeHasChildren:y,onValueNodeHasChildrenChange:b,contentId:(0,p.M)(),value:R,onValueChange:S,open:E,onOpenChange:w,dir:s,bubbleSelect:_,triggerPointerDownPosRef:I},C.createElement(x.Provider,{scope:t},n),M?C.createElement(ee,{ref:O,"aria-hidden":!0,tabIndex:-1,name:d,autoComplete:f,value:R,onChange:e=>S(e.target.value)}):null)},oe=D,ae=L,ie=N,ce=A,ue=j,le=B,se=$,de=H,fe=Y,pe=q,ve=X,me=G,he=Q},9575:function(e,t,n){"use strict";n.d(t,{g7:function(){return i},A4:function(){return u}});var r=n(6215),o=n(2784),a=n(7896);const i=o.forwardRef(((e,t)=>{const{children:n,...r}=e;return o.Children.toArray(n).some(l)?o.createElement(o.Fragment,null,o.Children.map(n,(e=>l(e)?o.createElement(c,(0,a.Z)({},r,{ref:t}),e.props.children):e))):o.createElement(c,(0,a.Z)({},r,{ref:t}),n)}));i.displayName="Slot";const c=o.forwardRef(((e,t)=>{const{children:n,...a}=e;return o.isValidElement(n)?o.cloneElement(n,{...s(a,n.props),ref:(0,r.F)(t,n.ref)}):o.Children.count(n)>1?o.Children.only(null):null}));c.displayName="SlotClone";const u=({children:e})=>o.createElement(o.Fragment,null,e);function l(e){return o.isValidElement(e)&&e.type===u}function s(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?n[r]=(...e)=>{null==a||a(...e),null==o||o(...e)}:"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}},6029:function(e,t,n){"use strict";n.d(t,{W:function(){return o}});var r=n(2784);function o(e){const t=r.useRef(e);return r.useEffect((()=>{t.current=e})),r.useMemo((()=>(...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)}),[])}},3597:function(e,t,n){"use strict";n.d(t,{T:function(){return a}});var r=n(6029),o=n(2784);function a({prop:e,defaultProp:t,onChange:n=(()=>{})}){const[a,i]=function({defaultProp:e,onChange:t}){const n=o.useState(e),[a]=n,i=o.useRef(a),c=(0,r.W)(t);return o.useEffect((()=>{i.current!==a&&(c(a),i.current=a)}),[a,i,c]),n}({defaultProp:t,onChange:n}),c=void 0!==e,u=c?e:a,l=(0,r.W)(n);return[u,o.useCallback((t=>{if(c){const n=t,r="function"==typeof t?n(e):t;r!==e&&l(r)}else i(t)}),[c,e,i,l])]}},4680:function(e,t,n){"use strict";n.d(t,{b:function(){return o}});var r=n(2784);const o=Boolean(null===globalThis||void 0===globalThis?void 0:globalThis.document)?r.useLayoutEffect:()=>{}},9586:function(e,t,n){"use strict";n.d(t,{D:function(){return o}});var r=n(2784);function o(e){const t=r.useRef({value:e,previous:e});return r.useMemo((()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous)),[e])}},6842:function(e,t,n){"use strict";n.d(t,{t:function(){return o}});var r=n(2784);function o(e){const[t,n]=r.useState(void 0);return r.useEffect((()=>{if(e){const t=new ResizeObserver((t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let o,a;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,a=t.blockSize}else{const t=e.getBoundingClientRect();o=t.width,a=t.height}n({width:o,height:a})}));return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)}),[e]),t}},9401:function(e,t,n){"use strict";n.d(t,{T:function(){return i},f:function(){return c}});var r=n(2130),o=n(2784),a=n(7896);const i=o.forwardRef(((e,t)=>o.createElement(r.W.span,(0,a.Z)({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})))),c=i},9732:function(e,t,n){"use strict";n.d(t,{R:function(){return c}});var r=new WeakMap,o=new WeakMap,a={},i=0,c=function(e,t,n){void 0===t&&(t=function(e){return"undefined"===typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e)),void 0===n&&(n="data-aria-hidden");var c=Array.isArray(e)?e:[e];a[n]||(a[n]=new WeakMap);var u=a[n],l=[],s=new Set,d=function(e){e&&!s.has(e)&&(s.add(e),d(e.parentNode))};c.forEach(d);var f=function(e){!e||c.indexOf(e)>=0||Array.prototype.forEach.call(e.children,(function(e){if(s.has(e))f(e);else{var t=e.getAttribute("aria-hidden"),a=null!==t&&"false"!==t,i=(r.get(e)||0)+1,c=(u.get(e)||0)+1;r.set(e,i),u.set(e,c),l.push(e),1===i&&a&&o.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute("aria-hidden","true")}}))};return f(t),s.clear(),i++,function(){l.forEach((function(e){var t=r.get(e)-1,a=u.get(e)-1;r.set(e,t),u.set(e,a),t||(o.has(e)||e.removeAttribute("aria-hidden"),o.delete(e)),a||e.removeAttribute(n)})),--i||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}}},3463:function(e,t,n){"use strict";var r=n(8570),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};function u(e){return r.isMemo(e)?i:c[e.$$typeof]||o}c[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},c[r.Memo]=i;var l=Object.defineProperty,s=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,v=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(v){var o=p(n);o&&o!==v&&e(t,o,r)}var i=s(n);d&&(i=i.concat(d(n)));for(var c=u(t),m=u(n),h=0;h<i.length;++h){var g=i[h];if(!a[g]&&(!r||!r[g])&&(!m||!m[g])&&(!c||!c[g])){var y=f(n,g);try{l(t,g,y)}catch(b){}}}}return t}},1088:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(u){c=!0,o=u}finally{try{i||null==n.return||n.return()}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t.default=void 0;var a,i=(a=n(2784))&&a.__esModule?a:{default:a},c=n(4e3),u=n(2203),l=n(8599);var s={};function d(e,t,n,r){if(e&&c.isLocalURL(t)){e.prefetch(t,n,r).catch((function(e){0}));var o=r&&"undefined"!==typeof r.locale?r.locale:e&&e.locale;s[t+"%"+n+(o?"%"+o:"")]=!0}}var f=function(e){var t,n=!1!==e.prefetch,r=u.useRouter(),a=i.default.useMemo((function(){var t=o(c.resolveHref(r,e.href,!0),2),n=t[0],a=t[1];return{href:n,as:e.as?c.resolveHref(r,e.as):a||n}}),[r,e.href,e.as]),f=a.href,p=a.as,v=e.children,m=e.replace,h=e.shallow,g=e.scroll,y=e.locale;"string"===typeof v&&(v=i.default.createElement("a",null,v));var b=(t=i.default.Children.only(v))&&"object"===typeof t&&t.ref,E=o(l.useIntersection({rootMargin:"200px"}),2),w=E[0],C=E[1],R=i.default.useCallback((function(e){w(e),b&&("function"===typeof b?b(e):"object"===typeof b&&(b.current=e))}),[b,w]);i.default.useEffect((function(){var e=C&&n&&c.isLocalURL(f),t="undefined"!==typeof y?y:r&&r.locale,o=s[f+"%"+p+(t?"%"+t:"")];e&&!o&&d(r,f,p,{locale:t})}),[p,f,C,y,n,r]);var S={ref:R,onClick:function(e){t.props&&"function"===typeof t.props.onClick&&t.props.onClick(e),e.defaultPrevented||function(e,t,n,r,o,a,i,u){("A"!==e.currentTarget.nodeName.toUpperCase()||!function(e){var t=e.currentTarget.target;return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)&&c.isLocalURL(n))&&(e.preventDefault(),t[o?"replace":"push"](n,r,{shallow:a,locale:u,scroll:i}))}(e,r,f,p,m,h,g,y)},onMouseEnter:function(e){t.props&&"function"===typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),c.isLocalURL(f)&&d(r,f,p,{priority:!0})}};if(e.passHref||"a"===t.type&&!("href"in t.props)){var M="undefined"!==typeof y?y:r&&r.locale,x=r&&r.isLocaleDomain&&c.getDomainLocale(p,M,r&&r.locales,r&&r.domainLocales);S.href=x||c.addBasePath(c.addLocale(p,M,r&&r.defaultLocale))}return i.default.cloneElement(t,S)};t.default=f},8599:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(u){c=!0,o=u}finally{try{i||null==n.return||n.return()}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}Object.defineProperty(t,"__esModule",{value:!0}),t.useIntersection=function(e){var t=e.rootRef,n=e.rootMargin,r=e.disabled||!c,s=a.useRef(),d=o(a.useState(!1),2),f=d[0],p=d[1],v=o(a.useState(t?t.current:null),2),m=v[0],h=v[1],g=a.useCallback((function(e){s.current&&(s.current(),s.current=void 0),r||f||e&&e.tagName&&(s.current=function(e,t,n){var r=function(e){var t,n={root:e.root||null,margin:e.rootMargin||""},r=l.find((function(e){return e.root===n.root&&e.margin===n.margin}));r?t=u.get(r):(t=u.get(n),l.push(n));if(t)return t;var o=new Map,a=new IntersectionObserver((function(e){e.forEach((function(e){var t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)}))}),e);return u.set(n,t={id:n,observer:a,elements:o}),t}(n),o=r.id,a=r.observer,i=r.elements;return i.set(e,t),a.observe(e),function(){if(i.delete(e),a.unobserve(e),0===i.size){a.disconnect(),u.delete(o);var t=l.findIndex((function(e){return e.root===o.root&&e.margin===o.margin}));t>-1&&l.splice(t,1)}}}(e,(function(e){return e&&p(e)}),{root:m,rootMargin:n}))}),[r,m,n,f]);return a.useEffect((function(){if(!c&&!f){var e=i.requestIdleCallback((function(){return p(!0)}));return function(){return i.cancelIdleCallback(e)}}}),[f]),a.useEffect((function(){t&&h(t.current)}),[t]),[g,f]};var a=n(2784),i=n(1424),c="undefined"!==typeof IntersectionObserver;var u=new Map,l=[]},9097:function(e,t,n){e.exports=n(1088)},5632:function(e,t,n){e.exports=n(2203)},8262:function(e,t,n){"use strict";var r=n(3586);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,i){if(i!==r){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},3980:function(e,t,n){e.exports=n(8262)()},3586:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3176:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(2784),o=n(3980),a=n.n(o);function i(){return i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var u=function(e){var t,n;function o(){var t;return(t=e.call(this)||this).handleExpired=t.handleExpired.bind(c(t)),t.handleErrored=t.handleErrored.bind(c(t)),t.handleChange=t.handleChange.bind(c(t)),t.handleRecaptchaRef=t.handleRecaptchaRef.bind(c(t)),t}n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var a=o.prototype;return a.getValue=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this.props.grecaptcha.getResponse(this._widgetId):null},a.getWidgetId=function(){return this.props.grecaptcha&&void 0!==this._widgetId?this._widgetId:null},a.execute=function(){var e=this.props.grecaptcha;if(e&&void 0!==this._widgetId)return e.execute(this._widgetId);this._executeRequested=!0},a.executeAsync=function(){var e=this;return new Promise((function(t,n){e.executionResolve=t,e.executionReject=n,e.execute()}))},a.reset=function(){this.props.grecaptcha&&void 0!==this._widgetId&&this.props.grecaptcha.reset(this._widgetId)},a.handleExpired=function(){this.props.onExpired?this.props.onExpired():this.handleChange(null)},a.handleErrored=function(){this.props.onErrored&&this.props.onErrored(),this.executionReject&&(this.executionReject(),delete this.executionResolve,delete this.executionReject)},a.handleChange=function(e){this.props.onChange&&this.props.onChange(e),this.executionResolve&&(this.executionResolve(e),delete this.executionReject,delete this.executionResolve)},a.explicitRender=function(){if(this.props.grecaptcha&&this.props.grecaptcha.render&&void 0===this._widgetId){var e=document.createElement("div");this._widgetId=this.props.grecaptcha.render(e,{sitekey:this.props.sitekey,callback:this.handleChange,theme:this.props.theme,type:this.props.type,tabindex:this.props.tabindex,"expired-callback":this.handleExpired,"error-callback":this.handleErrored,size:this.props.size,stoken:this.props.stoken,hl:this.props.hl,badge:this.props.badge}),this.captcha.appendChild(e)}this._executeRequested&&this.props.grecaptcha&&void 0!==this._widgetId&&(this._executeRequested=!1,this.execute())},a.componentDidMount=function(){this.explicitRender()},a.componentDidUpdate=function(){this.explicitRender()},a.componentWillUnmount=function(){void 0!==this._widgetId&&(this.delayOfCaptchaIframeRemoving(),this.reset())},a.delayOfCaptchaIframeRemoving=function(){var e=document.createElement("div");for(document.body.appendChild(e),e.style.display="none";this.captcha.firstChild;)e.appendChild(this.captcha.firstChild);setTimeout((function(){document.body.removeChild(e)}),5e3)},a.handleRecaptchaRef=function(e){this.captcha=e},a.render=function(){var e=this.props,t=(e.sitekey,e.onChange,e.theme,e.type,e.tabindex,e.onExpired,e.onErrored,e.size,e.stoken,e.grecaptcha,e.badge,e.hl,function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,["sitekey","onChange","theme","type","tabindex","onExpired","onErrored","size","stoken","grecaptcha","badge","hl"]));return r.createElement("div",i({},t,{ref:this.handleRecaptchaRef}))},o}(r.Component);u.displayName="ReCAPTCHA",u.propTypes={sitekey:a().string.isRequired,onChange:a().func,grecaptcha:a().object,theme:a().oneOf(["dark","light"]),type:a().oneOf(["image","audio"]),tabindex:a().number,onExpired:a().func,onErrored:a().func,size:a().oneOf(["compact","normal","invisible"]),stoken:a().string,hl:a().string,badge:a().oneOf(["bottomright","bottomleft","inline"])},u.defaultProps={onChange:function(){},theme:"light",type:"image",tabindex:0,size:"normal",badge:"bottomright"};var l=n(3463),s=n.n(l);function d(){return d=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d.apply(this,arguments)}var f={},p=0;var v="onloadcallback";var m,h,g=(m=function(){return"https://"+(("undefined"!==typeof window&&window.recaptchaOptions||{}).useRecaptchaNet?"recaptcha.net":"www.google.com")+"/recaptcha/api.js?onload="+v+"&render=explicit"},h=(h={callbackName:v,globalName:"grecaptcha"})||{},function(e){var t=e.displayName||e.name||"Component",n=function(t){var n,o;function a(e,n){var r;return(r=t.call(this,e,n)||this).state={},r.__scriptURL="",r}o=t,(n=a).prototype=Object.create(o.prototype),n.prototype.constructor=n,n.__proto__=o;var i=a.prototype;return i.asyncScriptLoaderGetScriptLoaderID=function(){return this.__scriptLoaderID||(this.__scriptLoaderID="async-script-loader-"+p++),this.__scriptLoaderID},i.setupScriptURL=function(){return this.__scriptURL="function"===typeof m?m():m,this.__scriptURL},i.asyncScriptLoaderHandleLoad=function(e){var t=this;this.setState(e,(function(){return t.props.asyncScriptOnLoad&&t.props.asyncScriptOnLoad(t.state)}))},i.asyncScriptLoaderTriggerOnScriptLoaded=function(){var e=f[this.__scriptURL];if(!e||!e.loaded)throw new Error("Script is not loaded.");for(var t in e.observers)e.observers[t](e);delete window[h.callbackName]},i.componentDidMount=function(){var e=this,t=this.setupScriptURL(),n=this.asyncScriptLoaderGetScriptLoaderID(),r=h,o=r.globalName,a=r.callbackName,i=r.scriptId;if(o&&"undefined"!==typeof window[o]&&(f[t]={loaded:!0,observers:{}}),f[t]){var c=f[t];return c&&(c.loaded||c.errored)?void this.asyncScriptLoaderHandleLoad(c):void(c.observers[n]=function(t){return e.asyncScriptLoaderHandleLoad(t)})}var u={};u[n]=function(t){return e.asyncScriptLoaderHandleLoad(t)},f[t]={loaded:!1,observers:u};var l=document.createElement("script");for(var s in l.src=t,l.async=!0,h.attributes)l.setAttribute(s,h.attributes[s]);i&&(l.id=i);var d=function(e){if(f[t]){var n=f[t].observers;for(var r in n)e(n[r])&&delete n[r]}};a&&"undefined"!==typeof window&&(window[a]=function(){return e.asyncScriptLoaderTriggerOnScriptLoaded()}),l.onload=function(){var e=f[t];e&&(e.loaded=!0,d((function(t){return!a&&(t(e),!0)})))},l.onerror=function(){var e=f[t];e&&(e.errored=!0,d((function(t){return t(e),!0})))},document.body.appendChild(l)},i.componentWillUnmount=function(){var e=this.__scriptURL;if(!0===h.removeOnUnmount)for(var t=document.getElementsByTagName("script"),n=0;n<t.length;n+=1)t[n].src.indexOf(e)>-1&&t[n].parentNode&&t[n].parentNode.removeChild(t[n]);var r=f[e];r&&(delete r.observers[this.asyncScriptLoaderGetScriptLoaderID()],!0===h.removeOnUnmount&&delete f[e])},i.render=function(){var t=h.globalName,n=this.props,o=(n.asyncScriptOnLoad,n.forwardedRef),a=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(n,["asyncScriptOnLoad","forwardedRef"]);return t&&"undefined"!==typeof window&&(a[t]="undefined"!==typeof window[t]?window[t]:void 0),a.ref=o,(0,r.createElement)(e,a)},a}(r.Component),o=(0,r.forwardRef)((function(e,t){return(0,r.createElement)(n,d({},e,{forwardedRef:t}))}));return o.displayName="AsyncScriptLoader("+t+")",o.propTypes={asyncScriptOnLoad:a().func},s()(o,e)})(u),y=g},6866:function(e,t){"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,u=n?Symbol.for("react.provider"):60109,l=n?Symbol.for("react.context"):60110,s=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,v=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,h=n?Symbol.for("react.lazy"):60116,g=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,E=n?Symbol.for("react.scope"):60119;function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case s:case d:case a:case c:case i:case p:return e;default:switch(e=e&&e.$$typeof){case l:case f:case h:case m:case u:return e;default:return t}}case o:return t}}}function C(e){return w(e)===d}t.AsyncMode=s,t.ConcurrentMode=d,t.ContextConsumer=l,t.ContextProvider=u,t.Element=r,t.ForwardRef=f,t.Fragment=a,t.Lazy=h,t.Memo=m,t.Portal=o,t.Profiler=c,t.StrictMode=i,t.Suspense=p,t.isAsyncMode=function(e){return C(e)||w(e)===s},t.isConcurrentMode=C,t.isContextConsumer=function(e){return w(e)===l},t.isContextProvider=function(e){return w(e)===u},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===f},t.isFragment=function(e){return w(e)===a},t.isLazy=function(e){return w(e)===h},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===o},t.isProfiler=function(e){return w(e)===c},t.isStrictMode=function(e){return w(e)===i},t.isSuspense=function(e){return w(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===d||e===c||e===i||e===p||e===v||"object"===typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===m||e.$$typeof===u||e.$$typeof===l||e.$$typeof===f||e.$$typeof===y||e.$$typeof===b||e.$$typeof===E||e.$$typeof===g)},t.typeOf=w},8570:function(e,t,n){"use strict";e.exports=n(6866)},357:function(e,t,n){"use strict";n.d(t,{Z:function(){return j}});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};var o=n(2784),a="right-scroll-bar-position",i="width-before-scroll-bar";var c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},c.apply(this,arguments)};function u(e){return e}function l(e,t){void 0===t&&(t=u);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}}}var s=function(e){void 0===e&&(e={});var t=l(null);return t.options=c({async:!0,ssr:!1},e),t}();function d(e,t){return function(e,t){var n=(0,o.useState)((function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(e){var t=n.value;t!==e&&(n.value=e,n.callback(e,t))}}}}))[0];return n.callback=t,n.facade}(t,(function(t){return e.forEach((function(e){return function(e,t){return"function"===typeof e?e(t):e&&(e.current=t),e}(e,t)}))}))}var f=function(){},p=o.forwardRef((function(e,t){var n=o.useRef(null),a=o.useState({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:f}),i=a[0],c=a[1],u=e.forwardProps,l=e.children,p=e.className,v=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noIsolation,b=e.inert,E=e.allowPinchZoom,w=e.as,C=void 0===w?"div":w,R=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),S=g,M=d([n,t]),x=r({},R,i);return o.createElement(o.Fragment,null,m&&o.createElement(S,{sideCar:s,removeScrollBar:v,shards:h,noIsolation:y,inert:b,setCallbacks:c,allowPinchZoom:!!E,lockRef:n}),u?o.cloneElement(o.Children.only(l),r({},x,{ref:M})):o.createElement(C,r({},x,{className:p,ref:M}),l))}));p.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},p.classNames={fullWidth:i,zeroRight:a};var v,m=function(e){var t=e.sideCar,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return o.createElement(r,c({},n))};m.isSideCarExport=!0;function h(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=v||n.nc;return t&&e.setAttribute("nonce",t),e}var g=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=h())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){!--e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=function(){var e=g();return function(t){o.useEffect((function(){return e.add(t),function(){e.remove()}}),[])}}();return function(t){var n=t.styles;return e(n),null}},b={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},w=function(e){if(void 0===e&&(e="margin"),"undefined"===typeof window)return b;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=y(),R=function(e,t,n,r){var o=e.left,c=e.top,u=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .with-scroll-bars-hidden {\n   overflow: hidden "+r+";\n   padding-right: "+l+"px "+r+";\n  }\n  body {\n    overflow: hidden "+r+";\n    "+[t&&"position: relative "+r+";","margin"===n&&"\n    padding-left: "+o+"px;\n    padding-top: "+c+"px;\n    padding-right: "+u+"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: "+l+"px "+r+";\n    ","padding"===n&&"padding-right: "+l+"px "+r+";"].filter(Boolean).join("")+"\n  }\n  \n  ."+a+" {\n    right: "+l+"px "+r+";\n  }\n  \n  ."+i+" {\n    margin-right: "+l+"px "+r+";\n  }\n  \n  ."+a+" ."+a+" {\n    right: 0 "+r+";\n  }\n  \n  ."+i+" ."+i+" {\n    margin-right: 0 "+r+";\n  }\n  \n  body {\n    --removed-body-scroll-bar-size: "+l+"px;\n  }\n"},S=function(e){var t=o.useState(w(e.gapMode)),n=t[0],r=t[1];o.useEffect((function(){r(w(e.gapMode))}),[e.gapMode]);var a=e.noRelative,i=e.noImportant,c=e.gapMode,u=void 0===c?"margin":c;return o.createElement(C,{styles:R(n,!a,u,i?"":"!important")})},M=function(e,t){var n=t;do{if("undefined"!==typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),x(e,n)){var r=_(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},x=function(e,t){return"v"===e?function(e){var t=window.getComputedStyle(e);return"hidden"!==t.overflowY&&!(t.overflowY===t.overflowX&&"visible"===t.overflowY)}(t):function(e){var t=window.getComputedStyle(e);return"range"===e.type||"hidden"!==t.overflowX&&!(t.overflowY===t.overflowX&&"visible"===t.overflowX)}(t)},_=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},O=!1;if("undefined"!==typeof window)try{var I=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",I,I),window.removeEventListener("test",I,I)}catch(V){O=!1}var T=!!O&&{passive:!1},k=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},P=function(e){return[e.deltaX,e.deltaY]},D=function(e){return e&&"current"in e?e.current:e},L=function(e){return"\n  .block-interactivity-"+e+" {pointer-events: none;}\n  .allow-interactivity-"+e+" {pointer-events: all;}\n"},N=0,A=[];var F,W=(F=function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),a=o.useState(N++)[0],i=o.useState((function(){return y()}))[0],c=o.useRef(e);o.useEffect((function(){c.current=e}),[e]),o.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-"+a);var t=[e.lockRef.current].concat((e.shards||[]).map(D)).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-"+a)})),function(){document.body.classList.remove("block-interactivity-"+a),t.forEach((function(e){return e.classList.remove("allow-interactivity-"+a)}))}}}),[e.inert,e.lockRef.current,e.shards]);var u=o.useCallback((function(e,t){if("touches"in e&&2===e.touches.length)return!c.current.allowPinchZoom;var o,a=k(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v",f=M(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=M(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var p=r.current||o;return function(e,t,n,r,o){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=a*r,c=n.target,u=t.contains(c),l=!1,s=i>0,d=0,f=0;do{var p=_(e,c),v=p[0],m=p[1]-p[2]-a*v;(v||m)&&x(e,c)&&(d+=m,f+=v),c=c.parentNode}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return(s&&(o&&0===d||!o&&i>d)||!s&&(o&&0===f||!o&&-i>f))&&(l=!0),l}(p,t,e,"h"===p?u:l,!0)}),[]),l=o.useCallback((function(e){var n=e;if(A.length&&A[A.length-1]===i){var r="deltaY"in n?P(n):k(n),o=t.current.filter((function(e){return e.name===n.type&&e.target===n.target&&(t=e.delta,o=r,t[0]===o[0]&&t[1]===o[1]);var t,o}))[0];if(o&&o.should)n.preventDefault();else if(!o){var a=(c.current.shards||[]).map(D).filter(Boolean).filter((function(e){return e.contains(n.target)}));(a.length>0?u(n,a[0]):!c.current.noIsolation)&&n.preventDefault()}}}),[]),s=o.useCallback((function(e,n,r,o){var a={name:e,delta:n,target:r,should:o};t.current.push(a),setTimeout((function(){t.current=t.current.filter((function(e){return e!==a}))}),1)}),[]),d=o.useCallback((function(e){n.current=k(e),r.current=void 0}),[]),f=o.useCallback((function(t){s(t.type,P(t),t.target,u(t,e.lockRef.current))}),[]),p=o.useCallback((function(t){s(t.type,k(t),t.target,u(t,e.lockRef.current))}),[]);o.useEffect((function(){return A.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,T),document.addEventListener("touchmove",l,T),document.addEventListener("touchstart",d,T),function(){A=A.filter((function(e){return e!==i})),document.removeEventListener("wheel",l,T),document.removeEventListener("touchmove",l,T),document.removeEventListener("touchstart",d,T)}}),[]);var v=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(i,{styles:L(a)}):null,v?o.createElement(S,{gapMode:"margin"}):null)},s.useMedium(F),m),Z=o.forwardRef((function(e,t){return o.createElement(p,r({},e,{ref:t,sideCar:W}))}));Z.classNames=p.classNames;var j=Z},7896:function(e,t,n){"use strict";function r(){return r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{Z:function(){return r}})}}]);