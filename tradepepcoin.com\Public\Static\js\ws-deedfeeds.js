const wssUrl = "wss://api.huobi.pro/ws";
const pako = window.pako;

const unitMap = { m: "min", h: "hour", d: "day", w: "week", M: "mon" };
const chart = klinecharts.init("chart_container");
let chart_type = 0;

// 统一规范K线数据：生成 time(秒)+timestamp(毫秒)，并数值化
function normalizeBar(d) {
  if (!d) return null;
  // 优先已有毫秒timestamp；否则用time/id(秒)转毫秒
  let ts = d.timestamp != null ? Number(d.timestamp) : 0;
  if (!ts) {
    if (d.time != null) ts = Number(d.time) * 1000;
    else if (d.id != null) ts = Number(d.id) * 1000;
  }
  // 若仍是秒级(极小可能)，兜底乘1000
  if (ts > 0 && ts < 1000000000000) ts = ts * 1000;

  const open = Number(d.open);
  const high = Number(d.high);
  const low = Number(d.low);
  const close = Number(d.close);
  const volume = Number(d.volume != null ? d.volume : d.vol);

  if (
    !isFinite(ts) ||
    !isFinite(open) ||
    !isFinite(high) ||
    !isFinite(low) ||
    !isFinite(close) ||
    !isFinite(volume)
  ) {
    return null;
  }
  return {
    time: ts,
    timestamp: ts,               // klinecharts 使用毫秒
    open,
    high,
    low,
    close,
    volume,
  };
}

function loading(type) {
  console.info(type);
  if (type == 0) {
    $(".loding").css("display", "flex");
    $(".loding").data("type", "1");
  } else {
    $(".loding").css("display", "none");
    $(".loding").data("type", "0");
  }
}
function changInterval(interval, th) {
  loading(0);
  window.deedfeeds.intervalChanged({
    interval: interval,
    setHistoryData: function (data) {
      try {
        if (typeof chart !== 'undefined') {
          chart.setPriceVolumePrecision(2, 2);
          chart.applyNewData(data);
        }
      } finally {
        loading(1);
      }
    },
    subscribeData: function (bar) {
      if (typeof chart !== 'undefined') {
        chart.updateData(bar);
      }
    },
  });
  if (th) {
    $(th).addClass("active");
    $(th).siblings().removeClass("active");
  }
}

function initOnReady1(kLineDataList) {
  if (!kLineDataList || !Array.isArray(kLineDataList) || kLineDataList.length === 0) {
    console.error("Invalid or empty kLineDataList");
    return;
  }
  try {
    chart.createTechnicalIndicator("MA", false, { id: "candle_pane" });
    chart.createTechnicalIndicator("VOL");
    kLineDataList.reverse();

    // 先映射后再规范
    var chartDataList = kLineDataList
      .map(function (data) {
        if (data && typeof data === "object") {
          return {
            timestamp: data.timestamp || 0,
            time: Math.floor((data.timestamp || 0) / 1000),
            open: data.open || 0,
            high: data.high || 0,
            low: data.low || 0,
            close: data.close || 0,
            volume: data.volume || 0,
          };
        }
        return null;
      })
      .filter(function (item) {
        return item !== null;
      })
      .map(normalizeBar)
      .filter(function (d) {
        return (
          d &&
          isFinite(d.open) &&
          isFinite(d.high) &&
          isFinite(d.low) &&
          isFinite(d.close) &&
          isFinite(d.volume) &&
          isFinite(d.timestamp) &&
          d.timestamp > 0
        );
      });

    if (chartDataList.length > 0) {
      chart.applyNewData(chartDataList);
      chart.setOffsetRightSpace(2);
      chart_type = 1;
    } else {
      console.error("No valid chart data after processing");
    }
  } catch (error) {
    console.error("Error in initOnReady1:", error);
  }
}

// 工具
function formatPeriod(str) {
  const reg = /([A-z]+)([0-9]+)/;
  const matchedArr = str.match(reg);
  if (matchedArr) {
    return matchedArr[2] + unitMap[matchedArr[1]];
  } else {
    console.error("格式化时间粒度出错！");
  }
}

const SYMBOL = window.SYMBOL;
const dataNum = 900;

const historyK = (interval) => {
  const reg1 = /([A-z]+)([0-9]+)/;
  let matchedArr = interval.match(reg1);

  const reg = /[0-9]+/;
  let shijian = interval.match(reg)[0];
  if (matchedArr[1] == "h") {
    shijian = matchedArr[2] * 60;
  }
  if (matchedArr[1] == "d") {
    shijian = matchedArr[2] * 60 * 24;
  }
  if (matchedArr[1] == "w") {
    shijian = matchedArr[2] * 60 * 24 * 3.5;
  }

  let form1 = Math.trunc(Date.now() / 1000) - 60 * dataNum * shijian;

  return {
    req: `market.${SYMBOL}.kline.${formatPeriod(interval)}`,
    id: SYMBOL,
    from: form1,
    to: Math.trunc(Date.now() / 1000),
  };
};

const subK = (interval) => ({
  sub: `market.${SYMBOL}.kline.${formatPeriod(interval)}`,
  id: SYMBOL,
});
const unsubK = (interval) => ({
  unsub: `market.${SYMBOL}.kline.${formatPeriod(interval)}`,
  id: SYMBOL,
});

class Deedfeeds {
  getMarketSpecialtyJsonAitclast(interval) {
    const reg1 = /([A-z]+)([0-9]+)/;
    let matchedArr = interval.match(reg1);
    let shijian = matchedArr[2];
    if (matchedArr[1] == "h") {
      shijian = matchedArr[2] * 60;
    }
    if (matchedArr[1] == "d") {
      shijian = matchedArr[2] * 60 * 24;
    }
    if (matchedArr[1] == "w") {
      shijian = matchedArr[2] * 60 * 24 * 3.5;
    }
    let _this = this;
    $.ajax({
      type: "GET",
      contentType: "application/json;charset=UTF-8",
      url: "/Home/Chart/getMarketSpecialtyJsonAitclast.html?step=" + shijian * 60,
      success: function (result) {
        var historyData = [];
        var item = (result && result[0]) || null;
        if (item) {
          const bar = normalizeBar({
            time: item.time,
            id: item.id,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
            volume: item.volume,
          });
          if (bar) {
            if (_this.subscribeDataParam && typeof _this.subscribeDataParam.subscribeData === 'function') {
              _this.subscribeDataParam.subscribeData(bar);
            }
            if (typeof chart !== 'undefined') {
              chart.updateData(bar);
            }
            localStorage.setItem("closeshijian" + SYMBOL, bar.close);
          }
        }
      },
      error: function (e) {
        console.log(e.status);
        console.log(e.responseText);
      },
    });
  }

  ajaxaitc(interval) {
    let _this = this;
    const reg1 = /([A-z]+)([0-9]+)/;
    let matchedArr = interval.match(reg1);

    let shijian = matchedArr[2];
    if (matchedArr[1] == "h") {
      shijian = matchedArr[2] * 60;
    }
    if (matchedArr[1] == "d") {
      shijian = matchedArr[2] * 60 * 24;
    }
    if (matchedArr[1] == "w") {
      shijian = matchedArr[2] * 60 * 24 * 3.5;
    }

    $.ajax({
      type: "GET",
      contentType: "application/json;charset=UTF-8",
      url:
        "/Home/Chart/getMarketSpecialtyJsonAitc.html?market=" +
        SYMBOL +
        "&step=" +
        shijian * 60,
      success: function (result) {
        var historyData = [];

        if (result && Array.isArray(result) && result.length > 0) {
          result.forEach((item) => {
            const bar = normalizeBar({
              time: item.time,
              id: item.id,
              open: item.open,
              high: item.high,
              low: item.low,
              close: item.close,
              volume: item.volume,
            });
            if (bar) historyData.unshift(bar);
          });
        }

        if (historyData.length === 0) {
          console.warn("No data received, generating mock data");
          var currentTime = Date.now();
          for (var i = 0; i < 50; i++) {
            var basePrice = 50000 + Math.random() * 2000 - 1000;
            const bar = normalizeBar({
              timestamp: currentTime - i * shijian * 60 * 1000,
              open: basePrice,
              high: basePrice + Math.random() * 100,
              low: basePrice - Math.random() * 100,
              close: basePrice + Math.random() * 200 - 100,
              volume: Math.random() * 1000 + 100,
            });
            if (bar) historyData.push(bar);
          }
        }

        var historyDataClean = historyData.filter(function (d) {
          return (
            d &&
            isFinite(d.open) &&
            isFinite(d.high) &&
            isFinite(d.low) &&
            isFinite(d.close) &&
            isFinite(d.volume) &&
            isFinite(d.timestamp)
          );
        });

        if (historyDataClean.length > 0) {
          _this.subscribeDataParam.setHistoryData(historyDataClean);
          if (matchedArr[0] == "m1") {
            localStorage.setItem("closeshijian" + SYMBOL, historyDataClean[0].close);
          } else {
            localStorage.removeItem("closeshijian" + SYMBOL);
          }
          if (chart_type == 0) {
            loading(1);
            chart.setPriceVolumePrecision(6, 6);
            initOnReady1(historyDataClean);
          } else {
            let historyData1 = [];
            historyDataClean.forEach((item) => {
              const bar = normalizeBar(item);
              if (bar) historyData1.unshift(bar);
            });
            loading(1);
            chart.applyNewData(historyData1.reverse());
          }
        } else {
          console.error("Failed to generate chart data");
          loading(1);
        }
      },
      error: function (e) {
        console.log(e.status);
        console.log(e.responseText);
      },
    });
  }

  constructor() {
    this.ws = new WebSocket(wssUrl);
    this.currentInterval = null;
    this.subscribeDataParam = {
      interval: null,
      setHistoryData: null,
      subscribeData: null,
    };
  }

  handleData(msg) {
    if (!this.subscribeDataParam.interval) {
      console.error("订阅数据参数错误！");
      return;
    }
    let data = JSON.parse(msg);

    if (data.ping) {
      this.ws.send(JSON.stringify({ pong: data.ping }));
    } else if (data.status === "ok") {
      if (data.data) {
        const historyData = [];
        data.data &&
          data.data.forEach((item) => {
            const bar = normalizeBar({
              time: item.time,
              id: item.id,
              open: item.open,
              high: item.high,
              low: item.low,
              close: item.close,
              volume: item.vol,
            });
            if (bar) historyData.unshift(bar);
          });
        console.log(historyData);
        var historyDataClean = historyData.filter(function (d) {
          return (
            d &&
            isFinite(d.open) &&
            isFinite(d.high) &&
            isFinite(d.low) &&
            isFinite(d.close) &&
            isFinite(d.volume) &&
            isFinite(d.timestamp)
          );
        });
        console.log("Sample data:", historyData[0]);
        loading(1);
        if (chart_type == 0) {
          console.log(
            "Filtered data length:",
            historyDataClean.length
          );
          console.log("Filtered sample:", historyDataClean[0]);
          chart.setPriceVolumePrecision(2, 2);
          initOnReady1(historyDataClean);
        } else {
          let historyData1 = [];
          data.data &&
            data.data.forEach((item) => {
              const bar = normalizeBar({
                time: item.time,
                id: item.id,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.close,
                volume: item.vol,
              });
              if (bar) historyData1.unshift(bar);
            });
          historyData1 = historyData1.filter(function (d) {
            return (
              d &&
              isFinite(d.open) &&
              isFinite(d.high) &&
              isFinite(d.low) &&
              isFinite(d.close) &&
              isFinite(d.volume) &&
              isFinite(d.timestamp)
            );
          });
          historyData1.reverse();
          chart.applyNewData(historyData1);
        }

        this.subscribeDataParam.setHistoryData(historyDataClean);
      } else if (data.unsubbed) {
        this.ws.send(JSON.stringify(historyK(this.subscribeDataParam.interval)));
        this.ws.send(JSON.stringify(subK(this.subscribeDataParam.interval)));
      }
    } else {
      if (data.tick) {
        const perData = data.tick;
        if (
          !(
            perData &&
            isFinite(perData.open) &&
            isFinite(perData.high) &&
            isFinite(perData.low) &&
            isFinite(perData.close) &&
            isFinite(perData.vol) &&
            (isFinite(perData.id) || isFinite(perData.time))
          )
        ) {
          return;
        }
        const perData1 = normalizeBar({
          time: perData.time,
          id: perData.id,
          open: perData.open,
          high: perData.high,
          low: perData.low,
          close: perData.close,
          volume: perData.vol,
        });
        if (perData1) {
          if (typeof chart !== 'undefined') {
            chart.updateData(perData1);
          }
          localStorage.setItem("closeshijian" + SYMBOL, perData.close);
          if (this.subscribeDataParam && typeof this.subscribeDataParam.subscribeData === 'function') {
            this.subscribeDataParam.subscribeData(perData1);
          }
        }
      }
    }
  }

  setHistoryData({ interval, setHistoryData, subscribeData }) {
    this.currentInterval = interval;
    this.ws.onopen = () => {
      if (SYMBOL == "usdzusdt") {
        this.ajaxaitc(interval);
        let _this = this;
        window.a = setInterval(function () {
          _this.getMarketSpecialtyJsonAitclast(interval);
        }, 2000);
      } else {
        this.ws.send(JSON.stringify(historyK(interval)));
        this.ws.send(JSON.stringify(subK(interval)));
      }
    };

    this.subscribeDataParam = {
      interval,
      setHistoryData,
      subscribeData,
    };

    this.ws.onmessage = (event) => {
      let blob = event.data;
      const fileReader = new FileReader();

      fileReader.onload = (e) => {
        let ploydata = new Uint8Array(e.target.result);
        let msg = pako.inflate(ploydata, { to: "string" });

        this.handleData(msg);
      };
      fileReader.readAsArrayBuffer(blob, "utf-8");
    };
  }

  intervalChanged({ interval, setHistoryData, subscribeData }) {
    this.subscribeDataParam = {
      interval,
      setHistoryData,
      subscribeData,
    };
    console.log("重新请求");
    if (SYMBOL == "usdzusdt") {
      clearInterval(window.a);
      clearInterval(window.b);

      let _this = this;
      window.b = setInterval(function () {
        _this.getMarketSpecialtyJsonAitclast(interval);
      }, 2000);

      this.ajaxaitc(interval);
    } else {
      this.ws.send(JSON.stringify(unsubK(this.currentInterval)));
    }

    this.currentInterval = interval;
  }
}

window.deedfeeds = new Deedfeeds();