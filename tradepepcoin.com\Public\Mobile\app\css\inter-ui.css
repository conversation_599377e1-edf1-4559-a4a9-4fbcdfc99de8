/*

LEGACY -- this file is deprecated.

The font changed name from "Inter UI" to "Inter" in February 2019.
This file is still here to support older uses of the name "Inter UI".

Please use "inter.css" instead for new applications.

*/
@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 100;
  src: url("../font/Inter-Thin.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 100;
  src: url("../font/Inter-ThinItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 200;
  src: url("../font/Inter-ExtraLight.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 200;
  src: url("../font/Inter-ExtraLightItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 300;
  src: url("../font/Inter-Light.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 300;
  src: url("../font/Inter-LightItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 400;
  src: url("../font/Inter-Regular.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 400;
  src: url("../font/Inter-Italic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 500;
  src: url("../font/Inter-Medium.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 500;
  src: url("../font/Inter-MediumItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 600;
  src: url("../font/Inter-SemiBold.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 600;
  src: url("../font/Inter-SemiBoldItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 700;
  src: url("../font/Inter-Bold.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 700;
  src: url("../font/Inter-BoldItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 800;
  src: url("../font/Inter-ExtraBold.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 800;
  src: url("../font/Inter-ExtraBoldItalic.woff2") format("woff2");
}

@font-face {
  font-family: 'Inter UI';
  font-style:  normal;
  font-weight: 900;
  src: url("../font/Inter-Black.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI';
  font-style:  italic;
  font-weight: 900;
  src: url("../font/Inter-BlackItalic.woff2") format("woff2");
}

/* --------------------------------------------------------------------------
Single variable font.

Note that you may want to do something like this to make sure you're serving
constant fonts to older browsers:
html {
  font-family: 'Inter UI', sans-serif;
}
@supports (font-variation-settings: normal) {
  html {
    font-family: 'Inter UI var', sans-serif;
  }
}

BUGS:
- Safari 12.0 will default to italic instead of regular when font-weight
  is provided in a @font-face declaration.
  Workaround: Use "Inter UI var alt" for Safari, or explicitly set
  `font-variation-settings:"slnt" DEGREE`.
*/
@font-face {
  font-family: 'Inter UI var';
  font-weight: 100 900;
  font-style: oblique 0deg 10deg;
  src: url("../font/Inter.var.woff2") format("woff2");
}

/* --------------------------------------------------------------------------

"Inter UI var alt" is recommended for Safari and Edge, for reliable italics.

@supports (font-variation-settings: normal) {
  html {
    font-family: 'Inter UI var alt', sans-serif;
  }
}

*/
@font-face {
  font-family: 'Inter UI var alt';
  font-weight: 100 900;
  font-style: normal;
  font-named-instance: 'Regular';
  src: url("../font/Inter-roman.var.woff2") format("woff2");
}
@font-face {
  font-family: 'Inter UI var alt';
  font-weight: 100 900;
  font-style: italic;
  font-named-instance: 'Italic';
  src: url("../font/Inter-italic.var.woff2") format("woff2");
}
