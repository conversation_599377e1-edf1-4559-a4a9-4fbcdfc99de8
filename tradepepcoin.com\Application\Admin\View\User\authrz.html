<include file="Public:header"/>
<script type="text/javascript" src="__PUBLIC__/layer/laydate/laydate.js"></script>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('User/index')}">用户管理</a> &gt;&gt;</span>
			<span class="h1-title">认证审核</span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('User/upanthrz')}" method="post" class="form-horizontal">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>

								<tr class="controls">
									<td class="item-label">用户名 :</td>
									<td>
										<input type="text" style="color:#000;"   readonly="readonly"  class="form-control input-10x" name="username" value="{$info.username}">
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">手机号码 :</td>
									<td>
										<input type="text" style="color:#000;"  readonly="readonly" class="form-control input-10x" name="phone" value="{$info.phone}">
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">地区 :</td>
									<td>
										<input type="text" style="color:#000;"  readonly="readonly" class="form-control input-10x" name="phone" value="{$info.area}">
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">姓名 :</td>
									<td>
										<input type="text" style="color:#000;"  readonly="readonly" class="form-control input-10x" name="phone" value="{$info.real_name}">
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">认证类型 :</td>
									<td>
										<input type="text" style="color:#000;"  readonly="readonly" class="form-control input-10x" name="phone" value="{$info.rztypestr}">
									</td>
									<td class="item-note"></td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">身份证正面 :</td>
									<td>
										<img src="/Public/Static/payimgs/{$info.cardzm}" style="height:100px;" />
									</td>
									<td class="item-note"></td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">身份证反面 :</td>
									<td>
										<img src="/Public/Static/payimgs/{$info.cardfm}" style="height:100px;" />
									</td>
									<td class="item-note"></td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">申请时间 :</td>
									<td>
										<input type="text" style="color:#000;" readonly="readonly" class="form-control input-10x" name="rztime" value="<?php echo date('Y-m-d H:i:s',$info['rztime']);?>">
									</td>
									<td class="item-note"></td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">认证赠送 :</td>
									<td>
									    <select name="kjid" class="form-control input-10x">
									        <foreach name="klist" item="vo">
										    <option  value="{$vo.id}" <eq name="vo.txstate" value="$vo.id}">selected</eq>>{$vo.title}</option>
										    </foreach>
									    </select>
									</td>
									<td class="item-note" style="color:red;"></td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">操作类型 :</td>
									<td>
									    <select name="rzstatus" class="form-control input-10x">
										<option value="2"  <eq name="data.rzstatus" value="2">selected</eq>>通过认证</option>
										<option value="3"  <eq name="data.rzstatus" value="3">selected</eq>>驳回认证</option>
									</select></td>
									<td class="item-note" style="color:red;"></td>
								</tr>

								<input type="hidden" name="uid"  value="{$info.id}" />
								
								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="btn submit-btn ajax-post" id="submit" type="submit" target-form="form-horizontal">确定</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
											</notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//提交表单
	$('#submit').click(function () {
		$('#form').submit();
	});
</script>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('User/index')}");
	</script>
</block>