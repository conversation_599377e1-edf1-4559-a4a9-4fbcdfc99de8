<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        .logo {
            margin-bottom: 30px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .language-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        .language-option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
            display: block;
        }
        .language-option:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
            transform: translateY(-2px);
        }
        .language-option.active {
            border-color: #667eea;
            background-color: #667eea;
            color: white;
        }
        .language-flag {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        .language-name {
            font-weight: bold;
            font-size: 14px;
        }
        .back-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .current-lang {
            background-color: #e8f5e8;
            color: #2d5a2d;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="/Public/Home/images/logo.png" alt="Logo" onerror="this.style.display='none'">
        </div>
        
        <h1>Select Language</h1>
        
        <div class="current-lang">
            Current Language: <strong id="currentLang">English</strong>
        </div>
        
        <div class="language-grid">
            <a href="?Lang=en-us" class="language-option" data-lang="en-us">
                <span class="language-flag">🇺🇸</span>
                <span class="language-name">English</span>
            </a>
            
            <a href="?Lang=zh-cn" class="language-option" data-lang="zh-cn">
                <span class="language-flag">🇨🇳</span>
                <span class="language-name">中文</span>
            </a>
            
            <a href="?Lang=fr-fr" class="language-option" data-lang="fr-fr">
                <span class="language-flag">🇫🇷</span>
                <span class="language-name">Français</span>
            </a>
            
            <a href="?Lang=de-de" class="language-option" data-lang="de-de">
                <span class="language-flag">🇩🇪</span>
                <span class="language-name">Deutsch</span>
            </a>
            
            <a href="?Lang=it-it" class="language-option" data-lang="it-it">
                <span class="language-flag">🇮🇹</span>
                <span class="language-name">Italiano</span>
            </a>
            
            <a href="?Lang=ja-jp" class="language-option" data-lang="ja-jp">
                <span class="language-flag">🇯🇵</span>
                <span class="language-name">日本語</span>
            </a>
            
            <a href="?Lang=ko-kr" class="language-option" data-lang="ko-kr">
                <span class="language-flag">🇰🇷</span>
                <span class="language-name">한국어</span>
            </a>
            
            <a href="?Lang=tr-tr" class="language-option" data-lang="tr-tr">
                <span class="language-flag">🇹🇷</span>
                <span class="language-name">Türkçe</span>
            </a>
        </div>
        
        <a href="/" class="back-link">← Back to Home</a>
    </div>

    <script>
        // 获取当前语言
        function getCurrentLanguage() {
            const urlParams = new URLSearchParams(window.location.search);
            const lang = urlParams.get('Lang') || 'en-us';
            return lang;
        }
        
        // 设置当前语言显示
        function setCurrentLanguageDisplay() {
            const currentLang = getCurrentLanguage();
            const langNames = {
                'en-us': 'English',
                'zh-cn': '中文',
                'fr-fr': 'Français',
                'de-de': 'Deutsch',
                'it-it': 'Italiano',
                'ja-jp': '日本語',
                'ko-kr': '한국어',
                'tr-tr': 'Türkçe'
            };
            
            document.getElementById('currentLang').textContent = langNames[currentLang] || 'English';
            
            // 高亮当前选中的语言
            document.querySelectorAll('.language-option').forEach(option => {
                if (option.getAttribute('data-lang') === currentLang) {
                    option.classList.add('active');
                }
            });
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            setCurrentLanguageDisplay();
            
            // 添加语言切换事件
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    const lang = this.getAttribute('data-lang');
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('Lang', lang);
                    window.location.href = currentUrl.toString();
                });
            });
        });
    </script>
</body>
</html> 