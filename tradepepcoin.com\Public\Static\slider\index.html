﻿<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>jQuery滑块补充缺口验证码插件 www.bootstrapMB.com</title>
    <link rel="stylesheet" href="https://lib.sinaapp.com/js/bootstrap/4.2.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/5.15.2/css/all.min.css">
  <link href="./disk/style.css"  rel="stylesheet">
  <style>
    .slidercaptcha {
      margin: 0 auto;
      width: 314px;
      height: 286px;
      border-radius: 4px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.125);
      margin-top: 40px;
    }
    .slidercaptcha .card-body {
      padding: 1rem;
    }
    .slidercaptcha canvas:first-child {
      border-radius: 4px;
      border: 1px solid #e6e8eb;
    }
    .slidercaptcha.card .card-header {
      background-image: none;
      background-color: rgba(0, 0, 0, 0.03);
    }
    .refreshIcon {
      top: -42px;
    }
  </style>
</head>

<body>
  <div class="container-fluid">
    <div class="form-row">
      <div class="col-12">
        <div class="slidercaptcha card">
          <div class="card-header"><span>请完成安全验证</span></div>
          <div class="card-body">
            <div id="captcha"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
    <script src="jquery.js"></script>
  <script src="./disk/longbow.js"></script>
  <script>
    $('#captcha').sliderCaptcha(
      { 
        repeatIcon: 'fa fa-redo',
        setSrc: function () {
          return ''; },
        onSuccess: function () { alert('验证通过!'); }
      });
        
    </script>
</body>

</html>