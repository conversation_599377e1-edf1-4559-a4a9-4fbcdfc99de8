// 语言切换下拉菜单功能
function toggleLanguageDropdown() {
    const dropdown = document.getElementById('langDropdown');
    const button = document.querySelector('.lang-btn');
    
    if (dropdown.style.display === 'block') {
        dropdown.style.display = 'none';
        button.style.borderColor = '#d8d8d8';
    } else {
        dropdown.style.display = 'block';
        button.style.borderColor = '#f0b90b';
    }
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('langDropdown');
    const button = document.querySelector('.lang-btn');
    const languageDropdown = document.querySelector('.language-dropdown');
    
    if (languageDropdown && !languageDropdown.contains(event.target)) {
        dropdown.style.display = 'none';
        button.style.borderColor = '#d8d8d8';
    }
});
