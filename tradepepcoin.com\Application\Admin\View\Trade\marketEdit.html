<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('Trade/market')}">市场列表</a> >></span>
            <span class="h1-title"><empty name="data">新增市场<else/>编辑市场</empty></span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('Trade/marketEdit')}" method="post" class="form-horizontal" enctype="multipart/form-data">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>
								<empty name="data['id']">
									<tr class="controls">
										<td class="item-label">买方币种 :</td>
										<td><select name="buyname" class="form-control input-10x">
											<volist name="C['coin']" id="v">
												<option value="{$v['name']}">{$v['title']}</option>
											</volist>
										</select>
										</td>
										<td class="item-note"></td>
									</tr>
									<tr class="controls">
										<td class="item-label">卖方币种 :</td>
										<td><select name="sellname" class="form-control input-10x">
											<volist name="C['coin']" id="v">
												<option value="{$v['name']}">{$v['title']}</option>
											</volist>
										</select>
										</td>
										<td class="item-note"></td>
									</tr>
									<else/>
									<tr class="controls">
										<td class="item-label">市场名称 :</td>
										<td>{$data.name}</td>
										<td class="item-note"></td>
									</tr>
								</empty>
								<tr class="controls">
									<td class="item-label">所属交易区</td>
									<td>
										<select name="jiaoyiqu">
											<volist name="getCoreConfig" id="v">
												<option value="{$key}"
												<eq name="data.jiaoyiqu" value="$key">
													selected
												</eq>
												>{$v}</option>
											</volist>
										</select>
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">小数位数:</td>
									<td>
										<input type="text" class="form-control input-10x" name="round" value="{$data.round}">
									</td>
									<td class="item-note">填1--6整数，比如填3就表示3位小数</td>
								</tr>
								<tr class="controls">
									<td class="item-label">买入手续费:</td>
									<td>
										<input type="text" class="form-control input-10x" name="fee_buy" value="{$data.fee_buy}">
									</td>
									<td class="item-note">%有挂单的时候不能再修改</td>
								</tr>
								<tr class="controls">
									<td class="item-label">卖出手续费:</td>
									<td>
										<input type="text" class="form-control input-10x" name="fee_sell" value="{$data.fee_sell}">
									</td>
									<td class="item-note">%有挂单的时候不能再修改</td>
								</tr>
								<tr class="controls">
									<td class="item-label">买入最小交易价:</td>
									<td>
                                        <empty name="data['buy_min']">
                                                <input type="text" class="form-control input-10x" name="buy_min" value="0.0001">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="buy_min" value="{$data.buy_min}">
                                        </empty>
									</td>
									<td class="item-note">买入最小交易价 默认0.000001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">买入最大交易价:</td>
									<td>
                                        <empty name="data['buy_max']">
                                                <input type="text" class="form-control input-10x" name="buy_max" value="10000000">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="buy_max" value="{$data.buy_max}">
                                        </empty>
									</td>
									<td class="item-note">买入最大交易价 默认10000000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">卖出最小交易价:</td>
									<td>
                                        <empty name="data['sell_min']">
                                                <input type="text" class="form-control input-10x" name="sell_min" value="0.0001">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="sell_min" value="{$data.sell_min}">
                                        </empty>
									</td>
									<td class="item-note">卖出最小交易价 默认0.000001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">卖出最大交易价:</td>
									<td>
                                        <empty name="data['sell_max']">
                                                <input type="text" class="form-control input-10x" name="sell_max" value="10000000">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="sell_max" value="{$data.sell_max}">
                                        </empty>
									</td>
									<td class="item-note">卖出最大交易价 默认10000000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔最小交易额:</td>
									<td>
                                        <empty name="data['trade_min']">
                                                <input type="text" class="form-control input-10x" name="trade_min" value="0.0001">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_min" value="{$data.trade_min}">
                                        </empty>
									</td>
									<td class="item-note">单笔最小交易额 默认10000000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔最大交易额:</td>
									<td>
                                        <empty name="data['trade_max']">
                                                <input type="text" class="form-control input-10x" name="trade_max" value="10000000">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_max" value="{$data.trade_max}">
                                        </empty>
									</td>
									<td class="item-note">单笔最大交易额 默认10000000</td>
								</tr>


								<tr class="controls">
									<td class="item-label">单笔买入最小交易数量:</td>
									<td>
                                        <empty name="data['trade_buy_num_min']">
                                                <input type="text" class="form-control input-10x" name="trade_buy_num_min" value="0.0001">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_buy_num_min" value="{$data.trade_buy_num_min}">
                                        </empty>
									</td>
									<td class="item-note">单笔买入最小交易量 默认0.0001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔买入最大交易数量:</td>
									<td>
                                        <empty name="data['trade_buy_num_max']">
                                                <input type="text" class="form-control input-10x" name="trade_buy_num_max" value="10000000">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_buy_num_max" value="{$data.trade_buy_num_max}">
                                        </empty>
									</td>
									<td class="item-note">单笔买入最大交易量 默认1000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔卖出最小交易数量:</td>
									<td>
                                        <empty name="data['trade_sell_num_min']">
                                                <input type="text" class="form-control input-10x" name="trade_sell_num_min" value="0.0001">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_sell_num_min" value="{$data.trade_sell_num_min}">
                                        </empty>
									</td>
									<td class="item-note">单笔卖出最小交易量 默认0.0001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔卖出最大交易数量:</td>
									<td>
                                       <empty name="data['trade_sell_num_max']">
                                                <input type="text" class="form-control input-10x" name="trade_sell_num_max" value="10000000">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_sell_num_max" value="{$data.trade_sell_num_max}">
                                        </empty>
									</td>
									<td class="item-note">单笔卖出最大交易量 默认1000</td>
								</tr>

								<tr class="controls" >
									<td class="item-label">一代赠送比例:</td>
									<td>
										<input type="text" class="form-control input-10x" name="invit_1" value="{$data.invit_1}">
									</td>
									<td class="item-note">%根据单笔交易手续费计算</td>
								</tr>
								<tr class="controls" >
									<td class="item-label">二代赠送比例:</td>
									<td>
										<input type="text" class="form-control input-10x" name="invit_2" value="{$data.invit_2}">
									</td>
									<td class="item-note">%根据单笔交易手续费计算</td>
								</tr>
								<tr class="controls" >
									<td class="item-label">三代赠送比例:</td>
									<td>
										<input type="text" class="form-control input-10x" name="invit_3" value="{$data.invit_3}">
									</td>
									<td class="item-note">%根据单笔交易手续费计算</td>
								</tr>
								<tr class="controls" >
									<td class="item-label">买家上家赠送 :</td>
									<td><select name="invit_buy" class="form-control input-10x">
										<option value="1"
										<eq name="data.invit_buy" value="1">selected</eq>
										>开启赠送</option>
										<option value="0"
										<eq name="data.invit_buy" value="0">selected</eq>
										>禁止赠送</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls" >
									<td class="item-label">卖家上家赠送 :</td>
									<td><select name="invit_sell" class="form-control input-10x">
										<option value="1"
										<eq name="data.invit_sell" value="1">selected</eq>
										>开启赠送</option>
										<option value="0"
										<eq name="data.invit_sell" value="0">selected</eq>
										>禁止赠送</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">涨幅限制:</td>
									<td>
										<input type="text" class="form-control input-10x" name="zhang" value="{$data.zhang}">
									</td>
									<td class="item-note">%根据昨日最后一笔交易计算</td>
								</tr>
								<tr class="controls">
									<td class="item-label">跌幅限制:</td>
									<td>
										<input type="text" class="form-control input-10x" name="die" value="{$data.die}">
									</td>
									<td class="item-note">%根据昨日最后一笔交易计算</td>
								</tr>
								<tr class="controls">
									<td class="item-label">开盘交易时间 :</td>
									<td>
										<select name="start_time" class="form-control input-10x" style="width:48%;float:left;">
											<volist name="time_arr" id="vo" key="k">
												<option value="{$k-1}"
													<eq name="data.start_time" value="$k-1">selected</eq>
												>{$vo}时</option>
											</volist>
										</select>
										<select name="start_minute" class="form-control input-10x" style="width:48%;float:right;">
											<volist name="time_minute" id="vo" key="k">
												<option value="{$k-1}"
													<eq name="data.start_minute" value="$k-1">selected</eq>
												>{$vo}分</option>
											</volist>
										</select>
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">闭盘交易时间 :</td>
									<td>
										<select name="stop_time" class="form-control input-10x" style="width:48%;float:left;">
											<volist name="time_arr" id="vo" key="k">
												<option value="{$k-1}"
													<eq name="data.stop_time" value="$k-1">selected</eq>
												>{$vo}时</option>
											</volist>
										</select>
										<select name="stop_minute" class="form-control input-10x" style="width:48%;float:right;">
											<volist name="time_minute" id="vo" key="k">
												<option value="{$k-1}"
													<eq name="data.stop_minute" value="$k-1">selected</eq>
												>{$vo}分</option>
											</volist>
										</select>
									</td>
									<td class="item-note"></td>
								</tr>

								<tr class="controls">
									<td class="item-label">周六 :</td>
									<td><select name="agree6" class="form-control input-10x">
										<option value="1"
										<eq name="data.agree6" value="1">selected</eq>
										>可交易</option>
										<option value="0"
										<eq name="data.agree6" value="0">selected</eq>
										>不可交易</option>
									</select></td>
									<td class="item-note"></td>
								</tr>

								<tr class="controls">
									<td class="item-label">周日 :</td>
									<td><select name="agree7" class="form-control input-10x">
										<option value="1"
										<eq name="data.agree7" value="1">selected</eq>
										>可交易</option>
										<option value="0"
										<eq name="data.agree7" value="0">selected</eq>
										>不可交易</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">开启交易 :</td>
									<td><select name="trade" class="form-control input-10x">
										<option value="1"
										<eq name="data.trade" value="1">selected</eq>
										>开启交易</option>
										<option value="0"
										<eq name="data.trade" value="0">selected</eq>
										>禁止交易</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">状态 :</td>
									<td><select name="status" class="form-control input-10x">
										<option value="1"
										<eq name="data.status" value="1">selected</eq>
										>可用</option>
										<option value="0"
										<eq name="data.status" value="0">selected</eq>
										>禁用</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">开启刷单 :</td>
									<td><select name="shuadan" class="form-control input-10x">
										<option value="1"
										<eq name="data.shuadan" value="1">selected</eq>
										>开启</option>
										<option value="0"
										<eq name="data.shuadan" value="0">selected</eq>
										>关闭</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">调用外部价格 :</td>
									<td><select name="sdtype" class="form-control input-10x">
										<option value="1"
										<eq name="data.sdtype" value="1">selected</eq>
										>开启</option>
										<option value="0"
										<eq name="data.sdtype" value="0">selected</eq>
										>关闭</option>
									</select></td>
									<td class="item-note">使用前需修改文件代码，调用比特儿海外交易所gate.io的价格</td>
								</tr>
								<tr class="controls">
									<td class="item-label">刷单上限价格:</td>
									<td>
										<input type="text" class="form-control input-10x" name="sdhigh" value="{$data.sdhigh}">
									</td>
									<td class="item-note">当前价:{$data.new_price}(设置涨跌幅后该设置无效)</td>
								</tr>
								<tr class="controls">
									<td class="item-label">刷单下限价格:</td>
									<td>
										<input type="text" class="form-control input-10x" name="sdlow" value="{$data.sdlow}">
									</td>
									<td class="item-note">当前价:{$data.new_price}(设置涨跌幅后该设置无效)</td>
								</tr>
								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="btn submit-btn ajax-post" id="submit" type="submit" target-form="form-horizontal">提交
											</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
											</notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
				<script type="text/javascript">
				//提交表单
				$('#submit').click(function () {
					$('#form').submit();
				});
				</script>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Trade/market')}");
	</script>
</block>