// 强制保障导航/重要链接可跳转（一次性兜底）
(function(){
  function closestAnchor(node){
    if (!node) return null;
    if (node.closest) return node.closest('a');
    while (node && node.nodeType === 1 && node.tagName !== 'A') node = node.parentNode;
    return node && node.tagName === 'A' ? node : null;
  }
  function isValidHref(h){
    if (!h) return false;
    if (h === '#' || /^javascript:/i.test(h)) return false;
    return true;
  }
  function shouldForce(a){
    // 头部导航、按钮、显式标注 data-force-nav
    if (a.getAttribute('data-force-nav') === '1') return true;
    return (
      a.matches && (
        a.matches('.modern-header a, .navbar a, .nav a, .btn-header, .dropdown-menu a')
      )
    );
  }
  function go(href){ try { window.location.assign(href); } catch(e) { window.location.href = href; } }

  // 捕获阶段兜底：尽量早于其他冒泡监听器执行
  document.addEventListener('click', function(ev){
    var a = closestAnchor(ev.target);
    if (!a) return;
    var href = a.getAttribute('href');
    if (!isValidHref(href)) return;
    if (!shouldForce(a)) return;
    ev.preventDefault();
    ev.stopImmediatePropagation();
    ev.stopPropagation();
    go(href);
  }, true);
})();

