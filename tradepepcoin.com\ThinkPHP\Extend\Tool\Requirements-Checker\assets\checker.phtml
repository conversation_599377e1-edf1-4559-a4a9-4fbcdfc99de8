<?php
/**
 * ThinkPHP框架探针 改自 Nette Framework Requirements Checker template.
 *
 * @param array    $requirements
 * @param bool     $errors
 * @param bool     $warnings
 */

header('Content-Type: text/html; charset=utf-8');
header('Cache-Control: s-maxage=0, max-age=0, must-revalidate');
header('Expires: Mon, 23 Jan 1978 10:00:00 GMT');
?>
<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Nette Framework">

	<title>ThinkPHP环境探针</title>

	<style type="text/css">
	html {
		font: 13px/1.5 Verdana, sans-serif;
		border-top: 5.3em solid #F4EBDB;
	}

	body {
		border-top: 1px solid #E4DED5;
		margin: 0;
		background: white;
		color: #333;
	}

	#wrapper {
		max-width: 780px;
		margin: -88px auto 0;
	}

	h1 {
		font: 2.3em/1.5 sans-serif;
		background: url(<?php echo $_SERVER['PHP_SELF'] .
		 '?=' . php_logo_guid(); ?>) right center no-repeat;
		color: #7A7772;
		text-shadow: 1px 1px 0 white;
		height:70px;
		line-height: 70px
	}

	h2 {
		font-size: 2em;
		font-weight: normal;
		color: #3484D2;
		margin: .7em 0;
	}

	p {
		margin: 1.2em 0;
	}

	.result {
		margin: 1.5em 0;
		padding: 0 1em;
		border: 2px solid white;
	}

	.passed h2 {
		color: #1A7E1E;
	}

	.failed h2 {
		color: white;
	}

	table {
		padding: 0;
		margin: 0;
		border-collapse: collapse;
		width: 100%;
	}

	table td, table th {
		text-align: left;
		padding: 10px;
		vertical-align: top;
		border-style: solid;
		border-width: 1px 0 0;
		border-color: inherit;
		background: white none no-repeat 12px 8px;
		background-color: inherit;
	}

	table th {
		font-size: 105%;
		font-weight: bold;
		padding-left: 50px;
	}

	.passed, .info {
		background-color: #E4F9E3;
		border-color: #C6E5C4;
	}

	.passed th {
		background-image: url('assets/passed.gif');
	}

	.info th {
		background-image: url('assets/info.gif');
	}

	.warning {
		background-color: #FEFAD4;
		border-color: #EEEE99;
	}

	.warning th {
		background-image: url('assets/warning.gif');
	}

	.failed {
		background-color: #F4D2D2;
		border-color: #D2B994;
	}

	div.failed {
		background-color: #CD1818;
		border-color: #CD1818;
	}

	.failed th {
		background-image: url('assets/failed.gif');
	}

	.description td {
		border-top: none !important;
		padding: 0 10px 10px 50px;
		color: #555;
	}

	.passed.description {
		display: none;
	}
	</style>

	<script type="text/javascript">
	var displayResult = function(id, passed) {
		var tr = document.getElementById('res' + id);
		tr.className = passed ? 'passed' : 'warning';
		tr.getElementsByTagName('td').item(0).innerHTML = passed ? '支持' : '不支持';
		if (tr = document.getElementById('desc' + id)) {
			if (passed) {
				tr.parentNode.removeChild(tr);
			} else {
				tr.className = 'warning description';
			}
		}
	}
	</script>
</head>

<body>
<div id="wrapper">
	<h1>ThinkPHP框架环境探针</h1>

	<p>本脚本检测是否你的PHP配置满足运行<a href="http://thinkphp.cn/">ThinkPHP框架</a>.它检测了PHP版本,是否合适的PHP扩展被加载了,还有是否PHP配置设置正确.</p>

	<?php if ($errors): ?>
	<div class="failed result">
		<h2>对不起，你的服务器配置不满足ThinkPHP框架的要求.</h2>
	</div>
	<?php else: ?>
	<div class="passed result">
		<h2>祝贺你!服务器配置满足ThinkPHP框架的要求.</h2>
		<?php if ($warnings):?><p><strong>请看下面列出的警告项.</strong></p><?php endif ?>
	</div>
	<?php endif ?>


	<h2>详情</h2>

	<table>
	<?php foreach ($requirements as $id => $requirement):?>
	<?php $class = isset($requirement->passed) ? ($requirement->passed ? 'passed' : ($requirement->required ? 'failed' : 'warning')) : 'info' ?>
	<tr id="res<?php echo $id ?>" class="<?php echo $class ?>">
		<th><?php echo htmlSpecialChars($requirement->title) ?></th>

		<?php if (empty($requirement->passed) && isset($requirement->errorMessage)): ?>
			<td><?php echo htmlSpecialChars($requirement->errorMessage) ?></td>
		<?php elseif (isset($requirement->message)): ?>
			<td><?php echo htmlSpecialChars($requirement->message) ?></td>
		<?php elseif (isset($requirement->passed)): ?>
			<td><?php echo $requirement->passed ? '支持' : '不支持' ?></td>
		<?php else: ?>
			<td>未测试</td>
		<?php endif ?>
	</tr>

	<?php if (isset($requirement->description)): ?>
	<tr id="desc<?php echo $id ?>" class="<?php echo $class ?> description">
		<td colspan="2"><?php echo $requirement->description ?></td>
	</tr>
	<?php endif ?>

	<?php if (isset($requirement->script)): ?>
		<?php echo $requirement->script ?>
	<?php endif ?>

	<?php endforeach ?>
	</table>

	<?php if ($errors): ?><p>请检查错误信息然后再<a href="?">试一次</a>.</p><?php endif ?>

	<p><small>探针版本<?php echo CHECKER_VERSION ?></small></p>
</div>
</body>
</html>