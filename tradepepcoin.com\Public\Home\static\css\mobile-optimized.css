/* Mobile-First Responsive Design for Trading Platform */

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
    .btn, .nav-link, .dropdown-item {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
    }
    
    .table tbody td {
        padding: 16px 8px;
    }
    
    .action-buttons .btn-sm {
        min-height: 40px;
        padding: 8px 12px;
    }
}

/* Mobile Navigation Improvements */
@media (max-width: 991px) {
    .navbar-toggler {
        border: none;
        padding: 8px;
        border-radius: 8px;
        background: rgba(240, 185, 11, 0.1);
    }
    
    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(240, 185, 11, 0.25);
    }
    
    .navbar-collapse {
        background: #fff;
        padding: 1.5rem;
        border-radius: 12px;
        margin-top: 1rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid var(--border-color);
    }
    
    .navbar-nav .nav-link {
        padding: 12px 16px !important;
        margin: 4px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .navbar-nav .nav-link:hover {
        background: rgba(240, 185, 11, 0.1);
        transform: translateX(8px);
    }
    
    .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: none;
        background: rgba(248, 249, 250, 0.8);
        margin: 8px 0;
        border-radius: 8px;
    }
    
    .dropdown-item {
        padding: 12px 20px;
        border-radius: 6px;
        margin: 2px 8px;
    }
}

/* Mobile Hero Section */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.2rem;
        margin-bottom: 1rem;
        line-height: 1.3;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        min-width: 140px;
        padding: 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }
    
    .stat-value {
        font-size: 1.8rem;
    }
    
    .register-form {
        margin-top: 2rem;
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .register-form h3 {
        font-size: 1.3rem;
        margin-bottom: 1.25rem;
    }
}

/* Mobile Market Table */
@media (max-width: 768px) {
    .market-table {
        border-radius: 12px;
        overflow: hidden;
    }
    
    .table-responsive {
        border-radius: 12px;
    }
    
    .table {
        font-size: 0.85rem;
    }
    
    .table thead th {
        padding: 12px 8px;
        font-size: 0.8rem;
        white-space: nowrap;
    }
    
    .table tbody td {
        padding: 12px 8px;
        vertical-align: middle;
    }
    
    .coin-info {
        gap: 8px;
    }
    
    .coin-logo {
        width: 28px;
        height: 28px;
        flex-shrink: 0;
    }
    
    .coin-name {
        font-size: 0.9rem;
        line-height: 1.2;
    }
    
    .coin-symbol {
        font-size: 0.75rem;
        margin-top: 1px;
    }
    
    .change-badge {
        padding: 3px 6px;
        font-size: 0.7rem;
        min-width: 50px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
        align-items: stretch;
    }
    
    .action-buttons .btn-sm {
        font-size: 0.75rem;
        padding: 6px 8px;
        text-align: center;
    }
}

/* Mobile Features Section */
@media (max-width: 768px) {
    .features-section {
        padding: 60px 0;
    }
    
    .feature-card {
        padding: 2rem 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 16px;
    }
    
    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
        margin-bottom: 1.25rem;
    }
    
    .feature-title {
        font-size: 1.3rem;
        margin-bottom: 0.75rem;
    }
    
    .feature-description {
        font-size: 0.95rem;
        line-height: 1.5;
    }
}

/* Mobile Notice Section */
@media (max-width: 768px) {
    .notice-section {
        padding: 15px 0;
    }
    
    .notice-container {
        gap: 12px;
        align-items: flex-start;
    }
    
    .notice-label {
        font-size: 0.8rem;
        padding: 6px 12px;
        flex-shrink: 0;
    }
    
    .notice-marquee {
        font-size: 0.9rem;
        line-height: 1.4;
    }
}

/* Small Mobile Devices */
@media (max-width: 576px) {
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .hero-section {
        padding: 40px 0 30px;
    }
    
    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 0.75rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.25rem;
    }
    
    .stat-item {
        min-width: 120px;
        padding: 12px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .register-form {
        padding: 1.25rem;
        border-radius: 12px;
    }
    
    .section-title {
        font-size: 1.8rem;
        margin-bottom: 2rem;
    }
    
    .market-section {
        padding: 40px 0;
    }
    
    .features-section {
        padding: 40px 0;
    }
    
    .feature-card {
        padding: 1.5rem 1rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .feature-title {
        font-size: 1.2rem;
    }
    
    .feature-description {
        font-size: 0.9rem;
    }
}

/* Landscape Mobile Orientation */
@media (max-width: 896px) and (orientation: landscape) {
    .hero-section {
        padding: 40px 0 30px;
    }
    
    .hero-stats {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .stat-item {
        min-width: 100px;
        padding: 8px 12px;
    }
    
    .register-form {
        margin-top: 1rem;
    }
}

/* Touch Gestures and Interactions */
.swipe-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.swipe-container::-webkit-scrollbar {
    display: none;
}

/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-to-refresh::before {
    content: '';
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    border: 3px solid var(--primary-color);
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0;
    transition: all 0.3s ease;
}

.pull-to-refresh.refreshing::before {
    top: 10px;
    opacity: 1;
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: no-preference) {
    .mobile-fade-in {
        animation: mobileSlideUp 0.6s ease-out;
    }
    
    @keyframes mobileSlideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .coin-logo,
    .feature-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) {
    .navbar-collapse {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .register-form {
        background: rgba(45, 55, 72, 0.95);
        border: 1px solid #4a5568;
    }
    
    .feature-card {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .market-table {
        background: #2d3748;
        border-color: #4a5568;
    }
}
