// 币种图标加载修复脚本
(function() {
    'use strict';
    
    // 币种图标映射表
    const coinIconMap = {
        'BTC': '/Public/Home/static/imgs/coins/btc.png',
        'ETH': '/Public/Home/static/imgs/coins/eth.png',
        'USDT': '/Public/Home/static/imgs/coins/usdt.png',
        'ADA': '/Public/Home/static/imgs/coins/ada.svg',
        'DOT': '/Public/Home/static/imgs/coins/dot.png',
        'LINK': '/xm/link.png',
        'LTC': '/xm/ltc.png',
        'XRP': '/xm/xrp.png',
        'BCH': '/xm/bch.png',
        'EOS': '/xm/eos.png',
        'TRX': '/xm/trx.png',
        'XLM': '/xm/xlm.svg',
        'ATOM': '/xm/atom.svg',
        'DASH': '/xm/dash.svg',
        'ZEC': '/xm/zec.svg',
        'XTZ': '/xm/xtz.svg',
        'FIL': '/xm/fil.png',
        'UNI': '/xm/uni.png',
        'YFI': '/xm/yfi.png',
        'SUSHI': '/xm/sushi.png',
        'LUNA': '/xm/luna.svg',
        'AVAX': '/xm/avax.svg',
        'DOGE': '/xm/doge.png'
    };
    
    // 默认图标路径
    const defaultIcon = '/Public/Home/static/imgs/empty-dark.png';
    
    // 修复图标加载
    function fixCoinIcons() {
        const coinLogos = document.querySelectorAll('.coin-logo');
        
        coinLogos.forEach(function(img) {
            const coinName = img.alt || '';
            const upperCoinName = coinName.toUpperCase();
            
            // 如果图片加载失败，尝试使用映射表
            img.addEventListener('error', function() {
                const mappedIcon = coinIconMap[upperCoinName];
                if (mappedIcon && this.src !== mappedIcon) {
                    this.src = mappedIcon;
                } else {
                    this.src = defaultIcon;
                }
            });
            
            // 预加载图标
            preloadIcon(img.src);
        });
    }
    
    // 预加载图标
    function preloadIcon(src) {
        if (!src || src === defaultIcon) return;
        
        const img = new Image();
        img.onload = function() {
            console.log('Icon loaded successfully:', src);
        };
        img.onerror = function() {
            console.warn('Icon failed to load:', src);
        };
        img.src = src;
    }
    
    // 动态更新图标
    function updateCoinIcon(coinName, newIconPath) {
        const coinLogos = document.querySelectorAll('.coin-logo');
        coinLogos.forEach(function(img) {
            if (img.alt && img.alt.toUpperCase() === coinName.toUpperCase()) {
                img.src = newIconPath;
            }
        });
    }
    
    // 添加图标到映射表
    function addCoinIcon(coinName, iconPath) {
        coinIconMap[coinName.toUpperCase()] = iconPath;
    }
    
    // 检查图标是否存在
    function checkIconExists(iconPath) {
        return new Promise(function(resolve) {
            const img = new Image();
            img.onload = function() {
                resolve(true);
            };
            img.onerror = function() {
                resolve(false);
            };
            img.src = iconPath;
        });
    }
    
    // 批量检查图标
    async function checkAllIcons() {
        const results = {};
        for (const [coin, path] of Object.entries(coinIconMap)) {
            results[coin] = await checkIconExists(path);
        }
        return results;
    }
    
    // 初始化
    function init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fixCoinIcons);
        } else {
            fixCoinIcons();
        }
        
        // 监听动态内容变化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    const coinLogos = mutation.target.querySelectorAll('.coin-logo');
                    if (coinLogos.length > 0) {
                        fixCoinIcons();
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 暴露公共方法
    window.CoinIcons = {
        fix: fixCoinIcons,
        update: updateCoinIcon,
        add: addCoinIcon,
        check: checkIconExists,
        checkAll: checkAllIcons,
        map: coinIconMap
    };
    
    // 自动初始化
    init();
    
})(); 