<include file="Public:header"/>
<link href="__PUBLIC__/Admin/index_css/style.css" rel="stylesheet">
<link href="__PUBLIC__/Admin/index_js/morris.js-0.4.3/morris.css" rel="stylesheet">
<script src="__PUBLIC__/Admin/index_js/morris.js-0.4.3/morris.min.js" type="text/javascript"></script>
<script src="__PUBLIC__/Admin/index_js/morris.js-0.4.3/raphael-min.js" type="text/javascript"></script>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <section class="wrapper">
        <!--state overview start-->
        <div class="row state-overview">
           <style>
			   .panel-heading{text-align: center;font-size: 18px;}
			   .symbol{width:30%!important;}
			   .state-overview .value {width:70%!important;}
			</style>
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-user" style="color: #4acea1;"></i>
                    </div>
                    <div class="value">
                        <h1 class="count" style="font-size: 24px;">{$alluser} </h1>

                        <p>注册总人数（人）</p>
                    </div>
                </section>
            </div>
            
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-th" style="color: #fa4b4c;"></i>
                    </div>
                    <div class="value">
                        <h1 class=" count2" style="font-size: 24px;">{$allhy} </h1>

                        <p>秒合约未平仓（条）</p>
                    </div>
                </section>
            </div>
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-transfer" style="color: #ffdc3a;"></i>
                    </div>
                    <div class="value">
                        <h1 class=" count3" style="font-size: 24px;">{$bball}</h1>

                        <p>币币交易额度（USDT）</p>
                    </div>
                </section>
            </div>
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-tasks" style="color: #4b9afa;"></i>
                    </div>
                    <div class="value">
                        <h1 class=" count4" style="font-size: 24px;">{$allkj} </h1>

                        <p>全网有效矿机总数（台）</p>
                    </div>
                </section>
            </div>
        </div>
        
        <div class="row state-overview">
           <style>
			   .panel-heading{text-align: center;font-size: 18px;}
			</style>
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-gift" style="color: #4acea1;"></i>
                    </div>
                    <div class="value">
                        <h1 class="count" style="font-size: 24px;">{$allissue}</h1>

                        <p>认购记录数（条）</p>
                    </div>
                </section>
            </div>
            
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-save" style="color: #fa4b4c;"></i>
                    </div>
                    <div class="value">
                        <h1 class=" count2" style="font-size: 24px;">{$daycz} | {$allcz}</h1>

                        <p>今日充值/充值总量（USDT）</p>
                    </div>
                </section>
            </div>
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-open" style="color: #ffdc3a;"></i>
                    </div>
                    <div class="value">
                        <h1 class=" count3" style="font-size: 24px;">{$daytx} | {$alltx}</h1>

                        <p>今日提现/提币总量（USDT）</p>
                    </div>
                </section>
            </div>
            <div class="col-lg-3 col-sm-6">
                <section class="panel">
                    <div class="symbol">
                        <i class="glyphicon glyphicon-check" style="color: #4b9afa;"></i>
                    </div>
                    <div class="value">
                        <h1 class=" count4" style="font-size: 24px;">{$allline}</h1>

                        <p>今日访客量（人）</p>
                    </div>
                </section>
            </div>
        </div>
        

        <div id="morris">
            <div class="row">
                <div class="col-lg-6">
                    <section class="panel">
                        <header class="panel-heading">
                            用户注册报表(30天)
                        </header>
                        <div class="panel-body">
                            <div id="hero-bar" class="graph"></div>
                        </div>
                    </section>
                </div>
                <div class="col-lg-6">
                    <section class="panel">
                        <header class="panel-heading">
                            充值/提现 统计图(30天)
                        </header>
                        <div class="panel-body">
                            <div id="hero-graph" class="graph"></div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
    var Script = function () {
        $(function () {
            show_cztx({$cztx});
            show_reg({$reg});

            //系统 充值/提现 统计图
            function show_cztx(data) {
                Morris.Line({
                    element: 'hero-graph',
                    data: data,
                    xkey: 'date',
                    ykeys: [
                        'charge',
                        'withdraw'
                    ],
                    labels: [
                        '充值',
                        '提现'
                    ],
                    lineColors: [
                        '#8075c4',
                        '#6883a3'
                    ],
                    xLabels: 'day',
                    postUnits: ' '
                });
            }

            //用户注册报表
            function show_reg(data) {
                Morris.Bar({
                    element: 'hero-bar',
                    data: data,
                    xkey: 'date',
                    ykeys: ['sum'],
                    labels: ['人数'],
                    barRatio: 0.4,
                    xLabelAngle: 35,
                    hideHover: 'auto',
                    barColors: ['#6883a3'],
                    xLabels: 'day',
                    postUnits: ' 人',
                });
            }

            //市场交易报表
            function show_trance(data) {
                Morris.Area({
                    element: 'hero-area',
                    data: [

                    ],

                    xkey: 'date',
                    ykeys: [

                    ],
                    labels: [

                    ],
                    hideHover: 'auto',
                    lineWidth: 1,
                    pointSize: 10,
                    lineColors: [
                        '#4a8bc2',
                        '#ff6c60',
                        '#a9d86e'
                    ],
                    fillOpacity: 0.5,
                    smooth: true,
                    postUnits: ' 元',
                    xLabels: 'day',
                });
            }

        });

    }();
</script>

 <script src="__PUBLIC__/Admin/js/jquery-1.8.3.min.js"></script>
<include file="Public:footer"/>
 <div style="display:none">
  
<script type="text/javascript">
  $.post("{:U('Admin/Trade/getxiaoxi')}",
        // {'st':st},
        function(data){
            if(data.code == 1){
                  var mp3 = new Audio('/kefu.mp3');  // 创建音频对象
                mp3.play(); 
                return false;
                layer.confirm('有新的合约订单', {
                  btn: ['知道了'] //按钮
                }, function(){
                    
                    $.post("{:U('Admin/Trade/settzstatus')}",
                    function(data){
                        if(data.code == 1){
                            window.location.reload();  
                        } 
                    });
                });
            }   
        });
 /*   $(function(){
        setInterval("xiaoxi()",300000);
    });
    
    function xiaoxi(){
      //  alert(333);
        var st = 1;
        $.post("{:U('Admin/Trade/getxiaoxi')}",
        // {'st':st},
        function(data){
            if(data.code == 1){
                  var mp3 = new Audio('/ding1.mp3');  // 创建音频对象
                mp3.play(); 
                return false;
                layer.confirm('有新的合约订单', {
                  btn: ['知道了'] //按钮
                }, function(){
                    
                    $.post("{:U('Admin/Trade/settzstatus')}",
                    function(data){
                        if(data.code == 1){
                            window.location.reload();  
                        } 
                    });
                });
            }   
        });
    }*/
        
        
</script>


  </div>
<block name="script">
    <script type="text/javascript" charset="utf-8">
        //导航高亮
        highlight_subnav("{:U('Index/index')}");
    </script>
</block>